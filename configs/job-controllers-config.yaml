Namespace: hero-user
ImageBuildMaxTime: 30m
LocalURL: http://job-controller-service.hero-system.svc.cluster.local:8088
TokenPath: /etc/websockify/token/token.conf
ToolImage: rregistry.cnbita.com:5000/leinaoyun/imagebuildtool-squash:v0.12.10
LicenseURL: http://license-auth.monitoring:8000
ServeNodeLabel: node-role.kubernetes.io/service
LokiURL: http://10.0.102.61:30100
ResourceRecycle:
  IsRecycle: true
  FilterLabels:
    {"karmada.io/managed":"true"}
  Resource:
    Syncaction:
      RecycletimeHour: 5
      DelayTimeHour: 5
    Trainingjob:
      RecycletimeHour: 5
      DelayTimeHour: 5
    Notebook:
      RecycletimeHour: 5
      DelayTimeHour: 5
    Imagemaker:
      RecycletimeHour: 1
      DelayTimeHour: 1
    Tensorboard:
      RecycletimeHour: 5
      DelayTimeHour: 5
FaultTolerance:
  # 当 trainingjob 健康检查 FailedJobRestartableCheck 的 policy 为 Advanced 时，运行失败的 job 是否可以重启的条件
  FailedJobRestart:
    # 如果用户容器的 exitCode 在 UnrestartableExitCodeRanges 范围内，则不能重启。
    # UnrestartableExitCodeRanges[*][0] 表示 exitCode 起始值，UnrestartableExitCodeRanges[*][1] 表示 exitCode 结束值。
    NonrestartableExitCodeRanges: [[2, 127], [130], [134], [137], [139], [143]]
    # 分析失败任务的最后 LatestLogs 条日志判断是否可以重启
    LatestLogs: 1000
    # 如果失败的任务符合任意一条 Policies 中的规则，则可以重启。优先级低于 UnrestartableExitCodeRanges 字段。
    Policies:
      # ExitCode 和 LogExps 两者必须同时满足，都不是必填项
      # LogExpr 中的所有条件必须同时满足
      - LogExps: ["torch.distributed.DistBackendError"]
      - LogExps: ["ncclRemoteError: A call failed possibly due to a network error or a remote process exiting prematurely"]
      - LogExps: ["socketProgress: Connection closed by remote peer"]
      - LogExps: ["RuntimeError: Gloo connectFullMesh failed with"]
      - LogExps: ["NCCL WARN Call to ibv_modify_qp failed with error Connection timed out"]
      - LogExps: ["socketProgressOpt: Call to recv from", "failed : Connection reset by peer"]
      - LogExps: ["Watchdog caught collective operation timeout", "WorkNCCL"]
  # 健康检查容器镜像
  HealthCheckImage: registry.cnbita.com:5000/leinaoyun/healthcheck:v0.1
rdma:
  secondaryNetwork: kube-system/macvlan-conf1,kube-system/macvlan-conf2,kube-system/macvlan-conf3,kube-system/macvlan-conf4,kube-system/macvlan-conf5,kube-system/macvlan-conf6,kube-system/macvlan-conf7,kube-system/macvlan-conf8
  ncclEnvs:
    - NCCL_IB_GID_INDEX=5
    - NCCL_IB_TC=128
    - NCCL_SOCKET_IFNAME=eth0
  hcclEnvs:
    - HCCL_IB_TC=128
    - HCCL_RDMA_SL=3
