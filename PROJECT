# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: hero.ai
layout:
- go.kubebuilder.io/v4
projectName: hero-controllers
repo: hero.ai/hero-controllers
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: hero.ai
  group: system
  kind: Notebook
  path: hero.ai/hero-controllers/api/v1alpha1
  version: v1alpha1
  webhooks:
    defaulting: true
    validation: true
    webhookVersion: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: hero.ai
  group: system
  kind: TrainingJob
  path: hero.ai/hero-controllers/api/v1alpha1
  version: v1alpha1
  webhooks:
    defaulting: true
    validation: true
    webhookVersion: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: hero.ai
  group: system
  kind: Tensorboard
  path: hero.ai/hero-controllers/api/v1alpha1
  version: v1alpha1
  webhooks:
    defaulting: true
    validation: true
    webhookVersion: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: hero.ai
  group: system
  kind: ImageMaker
  path: hero.ai/hero-controllers/api/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
  controller: true
  domain: hero.ai
  group: system
  kind: ResourcePool
  path: hero.ai/hero-controllers/api/v1alpha1
  version: v1alpha1
  webhooks:
    defaulting: true
    validation: true
    webhookVersion: v1
- controller: true
  domain: hero.ai
  group: system
  kind: ContainerIgnoreReady
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: hero.ai
  group: system
  kind: SyncAction
  path: hero.ai/hero-controllers/api/v1alpha1
  version: v1alpha1
version: "3"
