# tensorboard 作业

## 资源模型定义(CRD)

资源类型`Tensorboard` ，Group为`system.hero.ai`, Version为`v1alpha1`

spec 定义为:

```golang
type TensorboardSpec struct {
	ImageUrl              string              `json:"imageUrl,omitempty"`
	DataSource            DataSource          `json:"dataSource,omitempty"`
	Resources             corev1.ResourceList `json:"resourceConfig,omitempty"`
	MaxRunningTimeMinutes int32               `json:"maxRunningTimeMinutes,omitempty"`
}
type DataType string
type DataSource struct {
	// TODO 依赖统一存储, 暂使用hostpath
	HostPath  string   `json:"hostPath,omitempty"`
	MountPath string   `json:"mountPath,omitempty"`
	ReadOnly  bool     `json:"readOnly,omitempty"`
	DataType  DataType `json:"dataType,omitempty"`
}
```

status 定义为:

```golang
type TensorboardPhase string

const (
	TBPending  TensorboardPhase = "Pending"
	TBRunning  TensorboardPhase = "Running"
	TBStopped  TensorboardPhase = "Stopped"
	TBStopping TensorboardPhase = "Stopping"
)

type TensorboardState struct {
	Phase              TensorboardPhase `json:"phase,omitempty"`
	Reason             string           `json:"reason,omitempty"`
	Message            string           `json:"message,omitempty"`
	LastTransitionTime metav1.Time      `json:"lastTransitionTime,omitempty"`
}

// TensorboardStatus defines the observed state of Tensorboard
type TensorboardStatus struct {
	State       TensorboardState `json:"state,omitempty"`
	CreateTime  metav1.Time      `json:"createTime,omitempty"`
	StartTime   metav1.Time      `json:"startTime,omitempty"`
	StoppedTime metav1.Time      `json:"stoppedTime,omitempty"`
	// TensorboardUrl is the url of the tensorboard
	RunningDuration metav1.Duration `json:"runningDuration,omitempty"`
	TensorboardUrl  string          `json:"tensorboardUrl,omitempty"`
}
```

## 3.4.1.2.3 CR 样例

CR 示例如下

```yaml
apiVersion: system.hero.ai/v1alpha1
kind: Tensorboard
metadata:
  labels:
    app.kubernetes.io/name: tensorboard
    app.kubernetes.io/instance: tensorboard-sample
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/created-by: hero-controllers
  name: tensorboard-sample
spec:
  imageUrl: registry.bitahub.com:5000/leinaoyun/tensorboard:2.10.1-tf2.10.0-py39
  dataSource: 
    mountPath: /logs
  maxRunningTimeMinutes: 15

```

