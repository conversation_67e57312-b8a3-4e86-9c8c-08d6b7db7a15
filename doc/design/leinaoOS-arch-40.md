# Leinao OS 4.0 架构设计

[toc]

## 1 系统技术目标

![f5e118e6839308c50cf191fa82cea56](https://kingstone95.oss-cn-hangzhou.aliyuncs.com/img/f5e118e6839308c50cf191fa82cea56.png)

![3efa7a4a0f038f8d35c2caa2dbef1b3](https://kingstone95.oss-cn-hangzhou.aliyuncs.com/img/3efa7a4a0f038f8d35c2caa2dbef1b3.png)

## 2 AS-IS

服务组件: job-server、job-moniter、 image-server、image-agent、resource-manager、log-proxy
基础中间件: MySQL、RocketMQ
底层基础设施相关组件: kubesphere、volcano、nvidia-dp、gpu-manager、multus、sriov、whereabounts、dcgm-exporter、node-exporter、promethues

JobServer/Moniter 当前提供的功能:

1. 作业的创建及状态查询
2. 作业资源可分配判断（dry-run）
3. 容器镜像导出
4. 查询集群资源用量

不足

系统架构上:

1. 过多基础中间件的引用 会增加系统运维复杂度，当前数据库也存在SPOF, 可靠性受到影响
2. JobServer/Moniter 的设计较为冗余，也不够
3. 引入kubesphere 组件过重、其上的使用却很简单

实现细节上:

1. 用户易用性: 在提交分布式作业时， 用户需要根据平台特定的环境变量（如Volcano的给出的）拼接具体框架所需的变量（如 TF_CONFIG）
2. 容器安全: 容器未限制运行用户，使用root极大的暴露了系统的安全
3. 存储限额: a. 用户可利用镜像可写层将主机的文件系统写满(rootfs 未限制，root 用户具备全局可写权限) b. 外接存储挂载Code, 也可以被恶意写满

## 3 TO-BE

### 3.1 设计原则

符合产品需求的同时，着重考虑**提升用户体验**、**解决历史遗留问题**、**提升系统性能**、**增强可靠性**

### 3.2 产品需求

保留原平台的功能规格
增加一些额外细节功能（如专属资源-节点维度）
内部考虑后续是否支持**流水线**、**超参搜索**等能力

### 3.3 架构

![arch40.drawio](https://kingstone95.oss-cn-hangzhou.aliyuncs.com/img/arch40.drawio.png)

类脑OS4.0中去除了一些外部组件（如mysql、rocketmq、kubesphere等）， 使得整个系统更加稳固

类脑OS4.0中所有的API均构建与Kubernetes APIServer之上，使得构建的API更加规范整齐， 并能够利用kubernetes 原生提供的认证、授权、准入、限流等机制

CRD作为API的重要组成，对上提供简易的资源描述，屏蔽诸如a)涉及的多资源创建和b)内部实现为Volcano还是tf/pytorch 等得operator; 并通过event 更加便于观测任务的创建过程
CRD+Controller的开发使用kube-builder

AA 作为API中对CRD的补充，主要实现一些非重要资源, 不具备status, 可以同步返回任务结果的逻辑， 如节点标签、节点/卡隔离、获取集群总体资源等辅助功能。
AA的开发可以利用官方工具 [apiserver-builder](https://github.com/kubernetes-sigs/apiserver-builder-alpha) 来快速实现项目骨架

在详设与实现中, 遵循 [Kubernetes API Conventions](https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md) 及 [Operator最佳实践](https://cloud.redhat.com/blog/kubernetes-operators-best-practices)

### 3.4 训练子系统

1. **CRD**

   - **NoteBook**

   1. 支持将 jupyter notebook、ssh、vscode 在同个容器内启动，这些服务的进程管理使用[supervisord](http://supervisord.org)来做
   2. 支持用户自定义控制启动的服务列表
   3. 提供用户自定义镜像规范

   - **Training**
     单任务、分布式

     1. 支持感知不同训练框架、简化用户算法开发
        将 TF、PyTorch、XGBoost 等类型封装在一个All-in-One的CRD中（即感知不同训练框架的负载类型， 如PS/Worker/Chief,  Master/Worker等）, 底层控制器针对不同类型做相应处理
     2. 支持启用RDMA 计算加速, 并且明确内部实现中各个网络插件的发挥的作用, 功能不明确的需从基础组件中去除
     3. 支持启用数据访问加速（统一存储）
   - **Tensorboard**
     支持通过任务、或文件来源启动tensorboard
   - hyperparameter tuning
     支持运行超参搜索的任务
2. **AA-API**
   支持TrainJob的Dry-Run: 即原JobAllocatableAPI
3. **实现细节**
   容器安全控制：

   1. 方案一:用户创建的任务均以非root执行(SecurityContext: runAsUser, image中存在), 并在计算节点划分一块固定大小的临时存储供用户作为workspace使用，并在前端引导用户
      ![arch40-rootless.drawio](https://kingstone95.oss-cn-hangzhou.aliyuncs.com/img/arch40-rootless.drawio.png)
      优点：方案可执行性强，不会给系统其余由组件带来问题，影响可控
      缺点：用户体验上带来限制，并需要对用户提交的镜像做校验，避免一些提权操作的发生
      &nbsp;
   2. 方案二: 只讨论避免用户写满本地磁盘的情况, 可以使用[docker的-storage-opt](https://docs.docker.com/engine/reference/commandline/dockerd/#docker-runtime-execution-options)选项来限制单个容器的rootfs
      优点：实现简单，仅需进行配置; 仅能解决存储的问题, 容器安全并不能得以保证， 容器逃逸问题仍然很容器发生。
      缺点：强依赖底层运行时，如docker或isulad, 而当前主流的containerd 目前并没有支持计划
      &nbsp;
   3. 方案三: 在调试任务中, 为了更好的用户体验（开放root）与 安全隔离， 普通的解决方案无法同时很好的满足， 此时可以考虑将低阶运行时（oci-runtime）更换为kata（或类似借助虚拟机的运行时）; 将创建调试的Pod时, 以该runtimeclass运行; 而训练任务为保证性能不受影响，可以以默认的runc去执行（会导致用户体验一致性较差，kata官方文档说与标准Linux 容器性能相同，但在我们的场景下性能的影响需要进行测试后来做trade-off）。
      ![arch40-vm.drawio](https://kingstone95.oss-cn-hangzhou.aliyuncs.com/img/arch40-vm.drawio.png))
      优点：用户体验好， 隔离性强
      缺点：未做POC测试
      1. 对硬件与内核的依赖
      2. 如何支持GPU(与NVIDIA runtime 如何兼容)
      3. 与其他基础组件间的版本依赖

#### 3.4.1 详设

##### ******* 开发环境

###### *******.1资源模型定义(CRD)

资源类型 `Notebook` ，Group为 `system.hero.ai`, Version为 `v1alpha1`

```
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: notebooks.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: Notebook
    listKind: NotebookList
    plural: notebooks
    singular: notebook
  scope: Namespaced
```

Spec定义

```golang
type ExtendResource struct {
	CPUArch      string `json:"cpuArch,omitempty"`
	SharedMemory string `json:"sharedMemory,omitempty"`
}

type NotebookSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Foo is an example field of Notebook. Edit notebook_types.go to remove/update
	//Foo string `json:"foo,omitempty"`
	//jupyter notebook/ssh/gotty/vscode 能力参数
	// +kubebuilder:validation:Required
	ImageUrl       string          `json:"imageUrl,omitempty"`          //镜像地址
	Queue          string          `json:"queue,omitempty"`             //队列,默认default
	Command        string          `json:"command,omitempty"`           //启动命令
	SecretName     string          `json:"secretName,omitempty"`        //secret名称，key: authorized_keys, value: id_rsa.pub
	Plugins        []string        `json:"plugins,omitempty"`           //调试插件
	CodeSource     CodeSource      `json:"codeSource,omitempty"` 	    //代码源信息，详情参考CodeSource
	DataSources    []DataSource    `json:"dataSources,omitempty"`       //数据源，详情参考DataSource
	Resource       v1.ResourceList `json:"resource,omitempty"`          //请求计算资源
	ExtendResource ExtendResource  `json:"extendResource,omitempty"`    //额外资源，详情参考ExtendResource
	MaxRunTime     int             `json:"maxRunTime,omitempty"`        //最大运行时长，单位：分钟
	// +kubebuilder:validation:Required
	MaxRetryCount int32 `json:"maxRetryCount,omitempty"`                //最大重试次数
}

type CodeSource struct {
	GitUrl      string `json:"gitUrl,omitempty"`                       //git仓库地址
	Branch      string `json:"branch,omitempty"`                       //分支
	AccessName  string `json:"accessName,omitempty"`                   //私有仓库accessName
	AccessToken string `json:"accessToken,omitempty"`                  //私有仓库accessToken
	MountPath   string `json:"mountPath,omitempty"`                    //挂载容器内路径
}

type DataType string

const (
	OSS         DataType = "OSS"                                       //对象存储
	FS          DataType = "FS"                                        //文件存储
	Git         DataType = "git"                                       //忽略
	ShareMemory DataType = "shareMemory"                               //忽略
	App         DataType = "app"                                       //忽略
)

type DataSource struct {
	// TODO 依赖统一存储, 暂使用hostpath
	Name      string `json:"name,omitempty"`                         //名称
	HostPath  string `json:"hostPath,omitempty"`                     //物理机文件位置
	MountPath string `json:"mountPath,omitempty"`                    //需要挂载到容器内部的位置
	//only used by sharedMemory 
	SizeLimit    string          `json:"-"`                          //忽略
	ReadOnly     bool            `json:"readOnly,omitempty"`         //是否只读
	DataType     DataType        `json:"dataType,omitempty"`         //存储类型{OSS/FS}
	HostPathType v1.HostPathType `json:"hostPathType,omitempty"`     //文件夹、文件
}

type ExtendResource struct {
	CPUArch      string `json:"cpuArch,omitempty"`                  //cpu架构，amd64、arm64
	SharedMemory string `json:"sharedMemory,omitempty"`             //共享内存，单位MB
}
```

Status定义

```golang
type NotebookStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	State NotebookState `json:"notebookState,omitempty"`      //状态 
	// +optional 
	CreateTime string `json:"createTime,omitempty"`           //创建时间
	// +optional 
	StartTime string `json:"startTime,omitempty"`             //运行时间
	// +optional
	StoppedTime string `json:"stoppedTime,omitempty"`         //停止时间
	// +optional
	JupyterURL string `json:"jupyterURL,omitempty"`           //web访问jupyter地址
	// +optional
	WebTerminalURL string `json:"webTerminalURL,omitempty"`   //web访问web-terminal地址
	// +optional
	VscodeURL string `json:"vscodeURL,omitempty"`             //web访问vscode地址
	// +optional
	//SshURL string `json:"sshURL,omitempty"`                 //web访问ssh地址
	// +optional
	Conditions TjConditions `json:"conditions,omitempty"`     //有notebook创建的pod状态时间点
	// +optional
	RetryCount int32 `json:"retryCount,omitempty"`            //重试次数
}

type TjConditions map[string]TjPodConditions

type TjPodConditions struct {
	// Name is unique name in the node tree used to generate the node ID
	// +optional
	Name string `json:"name,omitempty"`                      //容器名称
    // TaskName is unique name in the node tree used to generate the node ID
	// +optional
	TaskName string `json:"taskName,omitempty"`             //task名称
	// A human readable message indicating details about why the node is in this condition.
	// +optional
	Message string `json:"message,omitempty"`               //message
	// Time at which this node started
	// +optional
	LaunchedTime string `json:"launchedTime"`               //启动时间
	// Time at which this node completed
	// +optional
	CompletedTime string `json:"completedTime"`            //完成时间
	// Phase a simple, high-level summary of where the node is in its lifecycle.
	// Can be used as a state machine.
	// +optional
	Phase v1.PodPhase `json:"phase,omitempty"`            //pod状态
}

type NotebookState string

const (
	//initialize------>初始化
	NotebookStatePending NotebookState = "Pending"
	//starting---->入队
	NotebookStateStartUp NotebookState = "Queuing"
	//running----->运行中
	NotebookStateRunning NotebookState = "Running"
	//stopping----->停止中
	NotebookStateStopping NotebookState = "Stopping"
	//stopped------>已停止
	NotebookStateStopped NotebookState = "Stopped"
	//startFailed----->容器启动异常
	NotebookStateStartFailed NotebookState = "Failed"
)
```

###### *******.2 CR示例

```yaml
apiVersion: system.hero.ai/v1alpha1
kind: Notebook
metadata:
  name: dk-1
  namespace: default
spec:
  queue: default
  imageUrl: registry.cnbita.com:5000/training/pytorch-1.6-cuda-11.0-py3:v1.0
  plugins:
  - web-terminal
  - jupyter
  codeSource:
    gitUrl: "https://gitee.com/golang_json/mysq-model-tool.git"
    accessName: ""
    accessToken: ""
    mountPath: "/code"
    branch: "master"
  dataSources:
  - name: "hfdata"
    hostPath: "/home/<USER>/hfdata1"
    mountPath: "/etc"
    readOnly: false
    dataType: "FS"
    hostPathType: "Directory"
  resource:
    cpu: 1
    memory: 2Gi
  extendResource:
    sharedMemory: "4096"
    cpuArch: amd64
```

##### ******* 训练作业

###### *******.1 资源模型定义(CRD)

资源类型 `TrainingJob` ，Group为 `system.hero.ai`, Version为 `v1alpha1`

```yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: trainingjobs.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: TrainingJob
    listKind: TrainingJobList
    plural: trainingjobs
    singular: trainingjob
  scope: Namespaced
```

spec 定义为:

```golang
// TrainingSpec defines the desired state of Training
// TrainingJobSpec defines the desired state of TrainingJob
type TrainingJobSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// ImageUrl is the url of the training job image

	// +kubebuilder:validation:Required
	ImageUrl string `json:"imageUrl,omitempty"`

	Queue string `json:"queue,omitempty"`

	Plugins []string `json:"plugins,omitempty"`

	Tasks []Task `json:"tasks,omitempty"`

	Envs []Env `json:"envs,omitempty"`

	CodeSource CodeSource `json:"codeSource,omitempty"`

	DataSources []DataSource `json:"dataSources,omitempty"`

	// +kubebuilder:validation:Required
	MaxRetryCount int32 `json:"maxRetryCount,omitempty"`
}
type Env struct {
	Name  string `json:"name,omitempty"`
	Value string `json:"value,omitempty"`
}

type CodeSource struct {
	GitUrl      string `json:"gitUrl,omitempty"`                       //git仓库地址
	Branch      string `json:"branch,omitempty"`                       //分支
	AccessName  string `json:"accessName,omitempty"`                   //私有仓库accessName
	AccessToken string `json:"accessToken,omitempty"`                  //私有仓库accessToken
	MountPath   string `json:"mountPath,omitempty"`                    //挂载容器内路径
}


type DataType string

const (
	OSS DataType = "OSS"
	FS  DataType = "FS"
)

type DataSource struct {
	// TODO 依赖统一存储, 暂使用hostpath
	Name      string `json:"name,omitempty"`                         //名称
	HostPath  string `json:"hostPath,omitempty"`                     //物理机文件位置
	MountPath string `json:"mountPath,omitempty"`                    //需要挂载到容器内部的位置
	//only used by sharedMemory 
	SizeLimit    string          `json:"-"`                          //忽略
	ReadOnly     bool            `json:"readOnly,omitempty"`         //是否只读
	DataType     DataType        `json:"dataType,omitempty"`         //存储类型{OSS/FS}
	HostPathType v1.HostPathType `json:"hostPathType,omitempty"`     //文件夹、文件
}

type ExtendResource struct {
	CPUArch      string `json:"cpuArch,omitempty"`                  //cpu架构，amd64、arm64
	SharedMemory string `json:"sharedMemory,omitempty"`             //共享内存，单位MB
}

type Task struct {
	Name           string                  `json:"name,omitempty"`            //task名称
	TaskType       string                  `json:"taskType,omitempty"`        //task role，根据不同分布式框架填写
	Command        string                  `json:"command,omitempty"`         //启动命令
	MinAvaluable   int32                   `json:"minAvaluable,omitempty"`    //最小运行数
	Replicas       int32                   `json:"replicas,omitempty"`        //副本数
	Resource       v1.ResourceRequirements `json:"resource,omitempty"`        //资源，k8s原生资源
	ExtendResource ExtendResource          `json:"extendResource,omitempty"`  //扩展资源
}
```

status 定义为:

```golang
// TrainingJobStatus defines the observed state of TrainingJob
type TrainingJobStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	State           TrainingJobState `json:"state,omitempty"`
	RetryCount      int32            `json:"retryCount,omitempty"`
	CreateTime      string           `json:"createTime,omitempty"`
	StartTime       string           `json:"startTime,omitempty"`
	StoppedTime     string           `json:"stoppedTime,omitempty"`
	CompletionTime  string           `json:"completionTime,omitempty"`
	Conditions      TjConditions     `json:"conditions,omitempty"`
}

type TjConditions map[string]TjPodConditions

type TjPodConditions struct {
	// Name is unique name in the node tree used to generate the node ID
	// +optional
	Name string `json:"name,omitempty"`
	// TaskName is unique name in the node tree used to generate the node ID
	// +optional
	TaskName string `json:"taskName,omitempty"`             //task名称
	// A human readable message indicating details about why the node is in this condition.
	// +optional
	Message string `json:"message,omitempty"`
	// Time at which this node started
	// +optional
	LaunchedTime string `json:"launchedTime"`
	// Time at which this node completed
	// +optional
	CompletedTime string `json:"completedTime"`
	// Phase a simple, high-level summary of where the node is in its lifecycle.
	// Can be used as a state machine.
	// +optional
	Phase v1.PodPhase `json:"phase,omitempty"`
}

type TrainingState struct {
	Phase              TrainingPhase `json:"phase,omitempty"`
	Reason             string        `json:"reason,omitempty"`
	Message            string        `json:"message,omitempty"`
	LastTransitionTime metav1.Time   `json:"lastTransitionTime,omitempty"`
}

// +kubebuilder:validation:Enum=Pending;Queuing;Running;Stopping;Stopped;Failed;Completed
type TrainingPhase string

const (
	Pending   TrainingPhase = "Pending"
	Queuing   TrainingPhase = "Queuing"
	Running   TrainingPhase = "Running"
	Stopping  TrainingPhase = "Stopping"
	Stopped   TrainingPhase = "Stopped"
	Failed    TrainingPhase = "Failed"
	Completed TrainingPhase = "Completed"
)
```

###### *******.2 函数流程设计

NoteBook/Training这类资源发生以下条件时，需要满足以下的顺序和动作

1. CR 创建

   - Webhook 接受到CR的创建请求, 进行合法性校验
   - Controller 接受到CR创建事件，默认状态为 `Phase: Pending`
   - Controller 为 CR 设置finalizer `system.hero.ai/finalizer`
   - Controller 根据 CR的spec 创建出vcJob及其他伴生资源(创建时, 这些资源的ownerReferences 设置为当前CR, vcjob上打上label `from.system.hero.ai: 该CR NamespacedName`),并将状态更新为 `Phase:Queuing`; 创建发生失败，则创建event记录, 状态不变
   - Controller 监听vcJob, 当vcJob的状态变为 `Running`时，则将 CR 状态设置为 `Phase:Running`, 并设置 `StartTime`为vcJob的 `conditions`中 `Running`的 `lastTransitionTime`
   - Controller 监听vcJob, 当vcJob的状态变为 `Completed`或 `Failed`时, 处理同上
2. CR 停止

   - 用户为CR 打上Annotation `command.system.hero.ai: stop`
   - Controller 接受到事件, 发现CR 上具有该注解， 创建 `Commad.bus.volcano.sh: AbortJob`, 并将状态更新为 `Phase:Stopping`
   - Controller 监听vcJob, 当vcJob的状态变为 `Aborted`时，更新CR状态为 `Phase:Stopped`
3. CR 删除

   - Controller 接受到事件， 发现具有 `DeletionTimestamp`, 则将所有 `ownerrefernce`为当前CR的资源删除
   - Controller 判断所有 `ownerrefernce`的资源删除后, 删除finalizer
   - CR 被GC清楚

注: 这里的时间统计处理, 不做具体统计到各个container

资源状态转换:
![arch40-training.drawio](https://kingstone95.oss-cn-hangzhou.aliyuncs.com/img/arch40-training.drawio.png)

###### *******.3 CR 样例

CR 示例如下

```yaml
apiVersion: system.hero.ai/v1alpha1
kind: TrainingJob
metadata:
  name: hf-tj-1
  namespace: default
spec:
  queue: default
  tasks:
  - name: hf-test1
    taskType: master
    minAvaluable: 1
    replicas: 1
    command: "sleep 10000"
    resource:
      cpu: 1
      memory: 1Gi
    extendResource:
      cpuArch: "amd64"
      sharedMemory: "4096"
  - name: hf-test2
    taskType: worker
    minAvaluable: 1
    replicas: 1
    command: "sleep 10000"
    resource:
      cpu: 1
      memory: 1Gi
  imageUrl: "ubuntu"
  codeSource:
    gitUrl: "https://gitee.com/golang_json/mysq-model-tool.git"
    accessName: ""
    accessToken: ""
    mountPath: "/code"
  envs:
  - name: "ekey1"
    value: "vvalue1"
  - name: "ekey2"
    value: "vvalue2"
  dataSources:
  - name: "hfdata"
    hostPath: "/home/<USER>/hfdata1"
    mountPath: "/etc"
    readOnly: false
    dataType: "FS"
    hostPathType: "Directory"
  - name: "hfgit"
    hostPath: "/home/<USER>/hfdata2"
    mountPath: "/data"
    readOnly: false
    dataType: "FS"
    hostPathType: "Directory"
  maxRetryCount: 2

```

##### ******* Tensorboard
  [Tensorboard](./tensorboard.md)

### 3.5 推理子系统

TODO
对上层同样以CRD 的方式提供
产品规划目前未出
内部需考虑

1. 功能上支持serverless、影子测试（生产环境中的新旧模型对比）等
2. 实现上引入KServer(KNative+Istio)

### 3.6 镜像子系统
  [ImageMaker](./ImageMaker.md)

### 3.7 集群管理子系统

1. 单个计算中心内部需支持

   1. 集群总资源
   2. 队列
   3. 节点/计算卡隔离
   4. ...
2. (TODO) 支持附属集群(对接公有云较底层API)
   不同于集群联邦，只有在本计算中心资源不足的情况下，才将任务下发到附属集群
   设计需遵循逻辑独立，对CRD侵入最小

### 3.8 监控子系统

TODO

### 3.9 日志子系统

TODO

## 4 Reference

- [containerd issues - fs quota](https://github.com/containerd/containerd/issues/759)
- [containerd issues - overlayfs quota](https://github.com/containerd/containerd/issues/3329)

## 5 开源项目

| 项目                                                                                    | stars | language |
| --------------------------------------------------------------------------------------- | ----- | -------- |
| [https://github.com/kubeflow/kubeflow](https://github.com/kubeflow/kubeflow)               | 12.8k | go       |
| [https://github.com/tencentmusic/cube-studio](https://github.com/tencentmusic/cube-studio) | 1.3k  |          |
| [https://github.com/mlflow/mlflow](https://github.com/mlflow/mlflow)                       | 14.8k | python   |
| [https://github.com/apache/airflow](https://github.com/apache/airflow)                     | 31.3k | python   |

## 6 故事拆分

| 任务                                  | 详情                                                                               | 工时(人/天) |
| ------------------------------------- | ---------------------------------------------------------------------------------- | ----------- |
| 基础组件版本调研                      | 完成k8s、volcano、NVIDIA-DP、GPUCNI插件等版本确定,以及在系统中的作用; 输出调研文档 | 5           |
| 基础组件部署                          | 完成上述组件部署, 验证基本功能                                                     |             |
| CRD+AA详设                            | 完成Notebook、TrainingJob等的CRD与流程详设                                         |             |
| hero-controller 框架搭建              | 确定kubebuilder版本，完成框架搭建，保证后续CRD开发快速使用                         |             |
| hero-apiserver 框架搭建               | 完成框架搭建，保证后续使用AA的API快速开发                                          |             |
| Notebook Controller、Webhook开发      |                                                                                    | 5           |
| Notebook 访问方式调研（host or port） |                                                                                    |             |
| Training Controller、Webhook开发      |                                                                                    | 7           |
| Tensorboard Controller、Webhook开发   |                                                                                    | 4           |
| ImageMaker Controller、Webhook开发    |                                                                                    | 5           |
| 卡隔离设计与开发                      |                                                                                    |             |
| 节点隔离设计与开发                    |                                                                                    |             |
| CR GC 设计开发                        | 完成过期CR的清除                                                                   |             |
|                                       |                                                                                    |             |

1. crd改动
2. 事件统计 - watch pod
3. 状态变更 -watch vj
4. 创建
5. 停止（创建command）
6. 删除

## 开发工具版本

| 组件              | 版本    | 备注                              |
| ----------------- | ------- | --------------------------------- |
| kubebuilder       | v3.10.0 | go 需要1.19; 已支持golang标准布局 |
| apiserver-builder | v1.23.0 |                                   |

## 项目初始化

### hero-controller

```bash
mkdir hero-controllers;cd hero-controllers
kubebuilder init --domain hero.ai --repo hero.ai/hero-controllers

kubebuilder create api --group system --version v1alpha1 --kind Notebook
kubebuilder create api --group system --version v1alpha1 --kind TrainingJob
kubebuilder create api --group system --version v1alpha1 --kind Tensorboard
kubebuilder create api --group system --version v1alpha1 --kind ImageMaker

```

### hero-apiserver

```bash
apiserver-boot init repo --domain hero.ai --module-name hero.ai/hero-apiserver

apiserver-boot create group version resource --group system --version v1 --kind Isolation

```
