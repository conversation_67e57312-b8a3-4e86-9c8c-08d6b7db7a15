# 镜像子系统


## 详设

#### 资源模型定义(CRD)

资源类型 `ImageMaker` ，Group为 `system.hero.ai`, Version为 `v1alpha1`

```yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: imagemakers.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: ImageMaker
    listKind: ImageMakerList
    plural: imagemakers
    singular: imagemaker
  scope: Namespaced
```

spec 定义为:

```golang
type ImageMakerSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	// ImageUrl is the url of the image to be made
	DestImageUrl string `json:"destImageUrl"`
	Source   Source `json:"source"`
}

type Source struct {
	Type           MakeMethod `json:"type"`
	Containers     Containers `json:"containers,omitempty"`
	SourceImageUrl string     `json:"sourceImageUrl,omitempty"`
	DataSource     DataSource `json:"dataSource,omitempty"`
}

type MakeMethod string

const (
	Dockerfile  MakeMethod = "Dockerfile"
	TarPkg      MakeMethod = "TarPkg"
	PublicImage MakeMethod = "PublicImage"
	Container   MakeMethod = "Container"
)

type Containers struct {
	Name      string `json:"name,omitempty"`
	Namespace string `json:"namespace,omitempty"`
	Kind      string `json:"kind,omitempty"`
}
```

status 定义为:

```golang
// +kubebuilder:validation:Enum=Pending;Running;Failed;Succeeded
type ImageMakerPhase string

const (
	IMPending   ImageMakerPhase = "Pending"
	IMRunning   ImageMakerPhase = "Running"
	IMFailed    ImageMakerPhase = "Failed"
	IMSucceeded ImageMakerPhase = "Succeeded"
  IMStopped   ImageMakerPhase = "Stopped"
)

type ImageMakerState struct {
	Phase              ImageMakerPhase `json:"phase,omitempty"`
	Reason             string          `json:"reason,omitempty"`
	Message            string          `json:"message,omitempty"`
	LastTransitionTime metav1.Time     `json:"lastTransitionTime,omitempty"`
}

// ImageMakerStatus defines the observed state of ImageMaker
type ImageMakerStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	State ImageMakerState `json:"state,omitempty"`

	CreateTime metav1.Time `json:"createTime,omitempty"`
}
```

#### 样例
1. 全部字段说明
```yaml
apiVersion: system.hero.ai/v1alpha1 #默认
kind: ImageMaker  #默认
metadata:
  name: imagemaker-sample  #cr名字，自定义即可
spec:
  destImageUrl: registry.bitahub.com:5000/aliang/imagedemo:ctlnotebook #构建完成的镜像，推送的地址
  source:
    type:   # 构建的类型
    containers:  
      name:  # 名称
      namespace:   # 命名空间
      kind:  # 类型
    sourceImageUrl: # 公共镜像地址
    datasource: 
      volumeName: # pvc name
      volumeSubpath:  # pvc 挂载的子路径
```

注意：  
destImageUrl字段和makeType字段是必须有， notebookNameSpacedName 、 sourceImageUrl 、 volume 是三选一（参考以下四个样例yaml）。

情况说明：  
type： 是用户镜像处理的方式，分别的：  
 "Dockerfile" 对应 dockerfile的构建方式   
 "TarPkg" 对应的镜像通过save指令得到的压缩文件的构建方式  
 "PublicImage" 对应的是公开仓库的镜像构建方式  
 "Container" 对应的是用户作业容器的镜像构建方式   

2. Container样例yaml

```yaml
apiVersion: system.hero.ai/v1alpha1
kind: ImageMaker
metadata:
  name: imagemaker-sample
spec:
  destImageUrl: registry.bitahub.com:5000/xxx/imagedemo:notebook 
  source:
    type: Container
    containers: 
      name: notebook-test
      namespace: default
      kind: Notebook
```
type为"Container" 只填以下字段信息:

kind 只有Notebook   
name 是Notebook的name    
namespace 是Notebook的namespace

3. PublicImage样例yaml

```yaml
apiVersion: system.hero.ai/v1alpha1
kind: ImageMaker
metadata:
  name: imagemaker-sample
spec:
  destImageUrl: registry.bitahub.com:5000/xxx/imagedemo:public 
  source:
    type: PublicImage
    sourceImageUrl: kwuliang/gindockdemo:v1
```
type为"PublicImage" 只填以下字段信息:  
sourceImageUrl 是公有仓库镜像名称     
 
3. TarPkg样例yaml

```yaml
apiVersion: system.hero.ai/v1alpha1
kind: ImageMaker
metadata:
  name: imagemaker-sample
spec:
  destImageUrl: registry.bitahub.com:5000/xxx/imagedemo:tar
  source:
    type: TarPkg
    dataSource: 
      volumeName: user-pvc
      volumeSubPath: dir/image.tar
```
type为"TarPkg" 只填以下字段信息,datasource 是挂载的pvc:      
    - volumeName: 是pvc 的name   
    - volumeSubpath: 是tarpkg的子路径


4. Dockerfile样例yaml

```yaml
apiVersion: system.hero.ai/v1alpha1
kind: ImageMaker
metadata:
  name: imagemaker-sample
spec:
  destImageUrl: registry.bitahub.com:5000/xxx/imagedemo:dokerfile
  source:
    type: Dockerfile
    dataSource: 
      volumeName: user-pvc
      volumeSubPath: dir/Dockerfile
```
type为"Dockerfile" 只填以下字段信息,datasource 是挂载的pvc:    
    - volumeName: 是pvc 的name   
    - volumeSubpath: 是dockerfile的子路径
