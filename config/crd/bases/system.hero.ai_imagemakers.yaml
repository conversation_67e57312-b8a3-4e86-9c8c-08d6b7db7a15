---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.3
  creationTimestamp: null
  name: imagemakers.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: ImageMaker
    listKind: ImageMakerList
    plural: imagemakers
    shortNames:
    - im
    - image
    singular: imagemaker
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.state.phase
      name: STATUS
      type: string
    - jsonPath: .status.nodeName
      name: NODENAME
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ImageMaker is the Schema for the imagemakers API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ImageMakerSpec defines the desired state of ImageMaker
            properties:
              destImageUrl:
                description: 'INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                  ImageUrl is the url of the image to be made'
                type: string
              source:
                properties:
                  containers:
                    properties:
                      kind:
                        type: string
                      name:
                        type: string
                      namespace:
                        type: string
                    type: object
                  cpuArch:
                    type: string
                  dataSource:
                    properties:
                      dataType:
                        type: string
                      mountPath:
                        type: string
                      name:
                        type: string
                      readOnly:
                        type: boolean
                      volumeName:
                        type: string
                      volumeSubPath:
                        type: string
                    required:
                    - volumeName
                    type: object
                  dockerfileUrl:
                    type: string
                  imageSecret:
                    properties:
                      password:
                        type: string
                      username:
                        type: string
                    type: object
                  sourceImageUrl:
                    type: string
                  type:
                    enum:
                    - Dockerfile
                    - TarPkg
                    - PublicImage
                    - PrivateImage
                    - Container
                    - UrlDockerfile
                    type: string
                required:
                - type
                type: object
            required:
            - destImageUrl
            - source
            type: object
          status:
            description: ImageMakerStatus defines the observed state of ImageMaker
            properties:
              cpuArch:
                type: string
              createTime:
                format: date-time
                type: string
              nodeName:
                type: string
              state:
                description: 'INSERT ADDITIONAL STATUS FIELD - define observed state
                  of cluster Important: Run "make" to regenerate code after modifying
                  this file'
                properties:
                  lastTransitionTime:
                    format: date-time
                    type: string
                  message:
                    type: string
                  phase:
                    enum:
                    - Pending
                    - Running
                    - Failed
                    - Succeeded
                    - Stopped
                    - Stopping
                    type: string
                  reason:
                    description: LastPhase          ImageMakerPhase `json:"lastPhase,omitempty"`
                    type: string
                type: object
              stoppedTime:
                format: date-time
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
