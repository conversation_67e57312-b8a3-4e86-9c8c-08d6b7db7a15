---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.3
  creationTimestamp: null
  name: notebooks.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: Notebook
    listKind: NotebookList
    plural: notebooks
    shortNames:
    - nb
    - nj
    singular: notebook
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.notebookState
      name: STATUS
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Notebook is the Schema for the notebooks API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: NotebookSpec defines the desired state of Notebook
            properties:
              codeSource:
                properties:
                  accessName:
                    type: string
                  accessToken:
                    type: string
                  branch:
                    type: string
                  gitUrl:
                    type: string
                  mountPath:
                    type: string
                type: object
              command:
                type: string
              customizePorts:
                additionalProperties:
                  type: integer
                type: object
              dataSources:
                items:
                  properties:
                    dataType:
                      type: string
                    mountPath:
                      type: string
                    name:
                      type: string
                    readOnly:
                      type: boolean
                    volumeName:
                      type: string
                    volumeSubPath:
                      type: string
                  required:
                  - volumeName
                  type: object
                type: array
              extendResource:
                properties:
                  cpuArch:
                    type: string
                  sharedMemory:
                    type: string
                type: object
              imageSecret:
                properties:
                  password:
                    type: string
                  username:
                    type: string
                type: object
              imageUrl:
                description: Foo is an example field of Notebook. Edit notebook_types.go
                  to remove/update Foo string `json:"foo,omitempty"` jupyter notebook/ssh/gotty/vscode
                  能力参数
                type: string
              maxRetryCount:
                format: int32
                type: integer
              maxRunTime:
                type: integer
              plugins:
                items:
                  type: string
                type: array
              resource:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: ResourceList is a set of (resource name, quantity) pairs.
                type: object
              resourcePool:
                type: string
              secretName:
                type: string
            type: object
          status:
            description: NotebookStatus defines the observed state of Notebook
            properties:
              conditions:
                items:
                  description: NotebookCondition contains details for the current
                    condition of this job.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one phase
                        to another.
                      format: date-time
                      type: string
                    status:
                      description: Status is the new phase of job after performing
                        the state's action.
                      type: string
                  required:
                  - status
                  type: object
                type: array
              createTime:
                type: string
              jupyter:
                properties:
                  state:
                    type: boolean
                  url:
                    type: string
                  vncPWD:
                    type: string
                type: object
              notebookState:
                type: string
              retryCount:
                format: int32
                type: integer
              serverDurations:
                additionalProperties:
                  properties:
                    completedTime:
                      description: Time at which this node completed
                      type: string
                    launchedTime:
                      description: Time at which this node started
                      type: string
                    message:
                      description: A human readable message indicating details about
                        why the node is in this condition.
                      type: string
                    name:
                      description: Name is unique name in the node tree used to generate
                        the node ID
                      type: string
                    nodeName:
                      description: Time at which this node nodeName
                      type: string
                    phase:
                      description: Phase a simple, high-level summary of where the
                        node is in its lifecycle. Can be used as a state machine.
                      type: string
                    reason:
                      description: The reason why pod failed
                      type: string
                    taskName:
                      description: TaskName is unique name in the node tree used to
                        generate the node ID
                      type: string
                    version:
                      description: Job version
                      format: int32
                      type: integer
                    webTerminalUrl:
                      description: Time at which this node webTerminalUrl
                      type: string
                  type: object
                type: object
              ssh:
                properties:
                  state:
                    type: boolean
                  url:
                    type: string
                  vncPWD:
                    type: string
                type: object
              startTime:
                type: string
              stoppedTime:
                type: string
              vnc:
                properties:
                  state:
                    type: boolean
                  url:
                    type: string
                  vncPWD:
                    type: string
                type: object
              vscode:
                properties:
                  state:
                    type: boolean
                  url:
                    type: string
                  vncPWD:
                    type: string
                type: object
              webTerminal:
                properties:
                  state:
                    type: boolean
                  url:
                    type: string
                  vncPWD:
                    type: string
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
