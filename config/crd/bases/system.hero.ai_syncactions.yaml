---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.3
  creationTimestamp: null
  name: syncactions.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: SyncAction
    listKind: SyncActionList
    plural: syncactions
    shortNames:
    - sync
    - syncaction
    singular: syncaction
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.state.phase
      name: STATUS
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    - jsonPath: .spec.resourcePool.type
      name: TYPES
      type: string
    - jsonPath: .spec.resourcePool.nodes
      name: NODES
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: SyncAction is the Schema for the syncactions API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: SyncActionSpec defines the desired state of SyncAction
            properties:
              exportFullLogParameters:
                properties:
                  bucket_file_path:
                    type: string
                  bucket_name:
                    type: string
                  container_name:
                    type: string
                  end_time:
                    type: string
                  from:
                    type: string
                  key_word_filter:
                    type: string
                  namespaces:
                    type: string
                  pods:
                    type: string
                  show_time:
                    type: string
                  size:
                    type: string
                  sort:
                    type: string
                  start_time:
                    type: string
                  storage_type:
                    type: string
                type: object
              name:
                description: Action defines the action that will be took to the target
                  object.
                type: string
              resourcePool:
                properties:
                  nodes:
                    items:
                      type: string
                    type: array
                  type:
                    enum:
                    - Bind
                    - UnBind
                    type: string
                type: object
              target:
                description: TargetObject defines the target object of this syncAction.
                properties:
                  apiVersion:
                    type: string
                  kind:
                    description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  name:
                    description: 'Name of the referent. More info: http://kubernetes.io/docs/user-guide/identifiers#names'
                    type: string
                required:
                - apiVersion
                - kind
                - name
                type: object
            type: object
          status:
            description: SyncActionStatus defines the observed state of SyncAction
            properties:
              state:
                description: 'INSERT ADDITIONAL STATUS FIELD - define observed state
                  of cluster Important: Run "make" to regenerate code after modifying
                  this file'
                properties:
                  phase:
                    enum:
                    - Syncing
                    - Failed
                    - Succeed
                    type: string
                  reason:
                    type: string
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
