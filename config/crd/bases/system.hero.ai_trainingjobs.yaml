---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.3
  creationTimestamp: null
  name: trainingjobs.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: TrainingJob
    listKind: TrainingJobList
    plural: trainingjobs
    shortNames:
    - tj
    - train
    singular: trainingjob
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.state.phase
      name: STATUS
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: TrainingJob is the Schema for the trainingjobs API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: TrainingJobSpec defines the desired state of TrainingJob
            properties:
              codeSource:
                properties:
                  accessName:
                    type: string
                  accessToken:
                    type: string
                  branch:
                    type: string
                  gitUrl:
                    type: string
                  mountPath:
                    type: string
                type: object
              dataSources:
                items:
                  properties:
                    dataType:
                      type: string
                    mountPath:
                      type: string
                    name:
                      type: string
                    readOnly:
                      type: boolean
                    volumeName:
                      type: string
                    volumeSubPath:
                      type: string
                  required:
                  - volumeName
                  type: object
                type: array
              envs:
                items:
                  properties:
                    name:
                      type: string
                    value:
                      type: string
                  type: object
                type: array
              faultTolerance:
                description: FaultTolerance 定义任务诊断和重启配置
                properties:
                  healthChecks:
                    description: 需要进行的健康检查项
                    items:
                      properties:
                        args:
                          type: object
                          x-kubernetes-preserve-unknown-fields: true
                        name:
                          type: string
                      type: object
                    type: array
                  restartPolicy:
                    description: 根据健康检查结果定义重启策略
                    properties:
                      delay:
                        description: 任务可重启时，任务停止后，等待 delay 秒再重新创建启动任务
                        format: int32
                        type: integer
                      maxRetry:
                        description: 最大重启次数
                        format: int32
                        type: integer
                      policies:
                        description: 当健康检查结果在给定的 Policies 中时才可重启任务
                        items:
                          type: string
                        type: array
                    type: object
                type: object
              imageSecret:
                properties:
                  password:
                    type: string
                  username:
                    type: string
                type: object
              imageUrl:
                type: string
              maxRetryCount:
                format: int32
                type: integer
              plugins:
                items:
                  type: string
                type: array
              resourcePool:
                type: string
              tasks:
                items:
                  properties:
                    command:
                      type: string
                    extendResource:
                      properties:
                        cpuArch:
                          type: string
                        sharedMemory:
                          type: string
                      type: object
                    minAvaluable:
                      format: int32
                      type: integer
                    name:
                      type: string
                    replicas:
                      format: int32
                      type: integer
                    resource:
                      additionalProperties:
                        anyOf:
                        - type: integer
                        - type: string
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      description: ResourceList is a set of (resource name, quantity)
                        pairs.
                      type: object
                    resourcePool:
                      type: string
                    taskType:
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: TrainingJobStatus defines the observed state of TrainingJob
            properties:
              completionTime:
                type: string
              conditions:
                items:
                  description: TrainingJobCondition contains details for the current
                    condition of this job.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one phase
                        to another.
                      format: date-time
                      type: string
                    status:
                      description: Status is the new phase of job after performing
                        the state's action.
                      enum:
                      - Pending
                      - Queuing
                      - Running
                      - Stopping
                      - Stopped
                      - Failed
                      - Completed
                      - Restarting
                      - NodeHealthChecking
                      type: string
                  required:
                  - status
                  type: object
                type: array
              createTime:
                type: string
              faultTolerance:
                additionalProperties:
                  properties:
                    completionTime:
                      description: vcJob 结束时间
                      format: date-time
                      type: string
                    createTime:
                      description: vcJob 创建时间
                      format: date-time
                      type: string
                    healthChecks:
                      description: TrainingJobSpec 中定义的每种健康检查的结果
                      items:
                        properties:
                          message:
                            description: Human-readable message indicating details
                              about result.
                            type: string
                          name:
                            type: string
                          result:
                            description: Unique, one-word, CamelCase reason for the
                              health check result.
                            type: string
                          time:
                            description: Time when the health check result was generated.
                            format: date-time
                            type: string
                        type: object
                      type: array
                    phase:
                      description: 当前版本 job 状态
                      enum:
                      - Pending
                      - Queuing
                      - Running
                      - Stopping
                      - Stopped
                      - Failed
                      - Completed
                      - Restarting
                      - NodeHealthChecking
                      type: string
                    restartReasons:
                      description: 触发重启的原因
                      items:
                        type: string
                      type: array
                    startTime:
                      description: vcJob Running 时间
                      format: date-time
                      type: string
                  type: object
                description: TrainingJob 每个 version 对应的健康检查结果和重启状态
                type: object
              retryCount:
                format: int32
                type: integer
              serverDurations:
                additionalProperties:
                  properties:
                    completedTime:
                      description: Time at which this node completed
                      type: string
                    launchedTime:
                      description: Time at which this node started
                      type: string
                    message:
                      description: A human readable message indicating details about
                        why the node is in this condition.
                      type: string
                    name:
                      description: Name is unique name in the node tree used to generate
                        the node ID
                      type: string
                    nodeName:
                      description: Time at which this node nodeName
                      type: string
                    phase:
                      description: Phase a simple, high-level summary of where the
                        node is in its lifecycle. Can be used as a state machine.
                      type: string
                    reason:
                      description: The reason why pod failed
                      type: string
                    taskName:
                      description: TaskName is unique name in the node tree used to
                        generate the node ID
                      type: string
                    version:
                      description: Job version
                      format: int32
                      type: integer
                    webTerminalUrl:
                      description: Time at which this node webTerminalUrl
                      type: string
                  type: object
                type: object
              startTime:
                type: string
              state:
                properties:
                  lastTransitionTime:
                    type: string
                  message:
                    type: string
                  phase:
                    enum:
                    - Pending
                    - Queuing
                    - Running
                    - Stopping
                    - Stopped
                    - Failed
                    - Completed
                    - Restarting
                    - NodeHealthChecking
                    type: string
                  reason:
                    type: string
                type: object
              stoppedTime:
                type: string
              version:
                description: 当前 job 的版本号，从 0 开始递增。
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
