# This kustomization.yaml is not intended to be run by itself,
# since it depends on service name and namespace that are out of this kustomize package.
# It should be run by config/default
resources:
- bases/system.hero.ai_notebooks.yaml
- bases/system.hero.ai_trainingjobs.yaml
- bases/system.hero.ai_tensorboards.yaml
- bases/system.hero.ai_imagemakers.yaml
#- bases/batch.volcano.sh.hero.ai_jobs.yaml
- bases/system.hero.ai_resourcepools.yaml
- bases/system.hero.ai_syncactions.yaml
#+kubebuilder:scaffold:crdkustomizeresource

patchesStrategicMerge:
# [WEBHOOK] To enable webhook, uncomment all the sections with [WEBHOOK] prefix.
# patches here are for enabling the conversion webhook for each CRD
#- patches/webhook_in_notebooks.yaml
#- patches/webhook_in_trainingjobs.yaml
#- patches/webhook_in_tensorboards.yaml
#- patches/webhook_in_imagemakers.yaml
#- patches/webhook_in_jobs.yaml
#- patches/webhook_in_resourcepools.yaml
#- path: patches/webhook_in_syncactions.yaml
#+kubebuilder:scaffold:crdkustomizewebhookpatch

# [CERTMANAGER] To enable cert-manager, uncomment all the sections with [CERTMANAGER] prefix.
# patches here are for enabling the CA injection for each CRD
#- patches/cainjection_in_notebooks.yaml
#- patches/cainjection_in_trainingjobs.yaml
#- patches/cainjection_in_tensorboards.yaml
#- patches/cainjection_in_imagemakers.yaml
#- patches/cainjection_in_jobs.yaml
#- patches/cainjection_in_resourcepools.yaml
#- path: patches/cainjection_in_syncactions.yaml
#+kubebuilder:scaffold:crdkustomizecainjectionpatch

# the following config is for teaching kustomize how to do kustomization for CRDs.
configurations:
- kustomizeconfig.yaml
