apiVersion: system.hero.ai/v1alpha1
kind: ResourcePool
metadata:
  labels:
    app.kubernetes.io/name: resourcepool
    app.kubernetes.io/instance: resourcepool-sample
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/created-by: hero-controllers
  name: resourcepool-sample
spec:
  nodes:
  - yigou-dev-102-45
  - yigou-dev-102-46

---
apiVersion: system.hero.ai/v1alpha1
kind: ResourcePool
metadata:
  name: a12687755544621056485535
spec:
status:
  nodes:
  - yigou-stg-101-65
  - yigou-stg-101-66
  - yigou-stg-101-67
  - yigou-stg-101-68
  - yigou-stg-101-69
  idle:
    cpu: "16"
    intel.com/infiniband_rdma_netdevs: "2"
    memory: 32346056Ki
    nvidia.com/nvidia-rtx-3090-24GB: "1"
  state: Open