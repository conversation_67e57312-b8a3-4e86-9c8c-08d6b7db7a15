apiVersion: system.hero.ai/v1alpha1
kind: TrainingJob
metadata:
  name: dk-1
  namespace: default
spec:
  queue: default
  plugins:
  - pytorch
  codeSource:
    accessName: "dk-test"
    branch: "1"
  tasks:
  - name: test1
    taskType: master
    minAvaluable: 1
    replicas: 1
    resource:
      limits:
        cpu: 1000
        memory: 2Gi
      requests:
        cpu: 1000
        memory: 2Gi
  - name: test2
    taskType: worker
    minAvaluable: 1
    replicas: 2
    resource:
      limits:
        cpu: 1000
        memory: 2Gi
      requests:
        cpu: 1000
        memory: 2Gi