apiVersion: system.hero.ai/v1alpha1
kind: SyncAction
metadata:
  labels:
    cluster.hero.ai/cluster: a12559780286951424534014
  name: syncaction-sample
spec:
  name: FullLogExportAction
  exportFullLogParameters: # 接口参数
    bucket_file_path: "tb1"
    bucket_name: "b20240417134837656ypdzlp"
    end_time: "1724040391000000000"
    from:
    key_word_filter: ""
    namespaces: "monitoring"
    pods: "loki-stack-0"
    show_time: 
    # size: ""    
    sort: "asc"
    start_time: "1724040339000000000"
    storage_type: "test"


---

apiVersion: system.hero.ai/v1alpha1
kind: SyncAction
metadata:
  name: a12618616203767808661501
  namespace: hero-user
spec:
  exportFullLogParameters:
    bucket_file_path: suqiliang
    bucket_name: b202408161031538959oy7t1
    end_time: "1723877499000000000"
    namespaces: hero-user
    pods: trainingjob-a12584882464747520645734-task1-0
    sort: asc
    start_time: "1723877435000000000"
    storage_type: filesystem
  name: FullLogExportAction

---

apiVersion: system.hero.ai/v1alpha1
kind: SyncAction
metadata:
  name: a12618616203767808661501
  namespace: hero-user
spec:
  resourcePool:
    type: Bind
    nodes:
    - yigou-stg-101-66
    - yigou-stg-101-67
    - yigou-stg-101-68
    - yigou-stg-101-69
  target:
    apiVersion: system.hero.ai/v1alpha1
    kind: ResourcePool
    name: default
  name: SyncAction

---

apiVersion: system.hero.ai/v1alpha1
kind: SyncAction
metadata:
  name: a12618616203767808661501
  namespace: hero-user
spec:
  resourcePool:
    type: UnBind
    nodes:
    - yigou-stg-101-66
    - yigou-stg-101-67
    - yigou-stg-101-68
    - yigou-stg-101-69
  target:
    apiVersion: system.hero.ai/v1alpha1
    kind: ResourcePool
    name: default
  name: SyncAction
