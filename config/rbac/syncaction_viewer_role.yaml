# permissions for end users to view syncactions.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: syncaction-viewer-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: hero-controllers
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
  name: syncaction-viewer-role
rules:
- apiGroups:
  - system.hero.ai
  resources:
  - syncactions
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - syncactions/status
  verbs:
  - get
