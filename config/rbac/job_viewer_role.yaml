# permissions for end users to view jobs.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: job-viewer-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: hero-controllers
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
  name: job-viewer-role
rules:
- apiGroups:
  - batch.volcano.sh.hero.ai
  resources:
  - jobs
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - batch.volcano.sh.hero.ai
  resources:
  - jobs/status
  verbs:
  - get
