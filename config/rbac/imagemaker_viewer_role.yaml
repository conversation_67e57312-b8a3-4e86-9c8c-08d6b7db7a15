# permissions for end users to view imagemakers.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: imagemaker-viewer-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: hero-controllers
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
  name: imagemaker-viewer-role
rules:
- apiGroups:
  - system.hero.ai
  resources:
  - imagemakers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - imagemakers/status
  verbs:
  - get
