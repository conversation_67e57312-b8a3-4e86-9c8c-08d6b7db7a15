---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  name: manager-role
rules:
- apiGroups:
  - batch.volcano.sh
  resources:
  - jobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - bus.volcano.sh
  resources:
  - commands
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - events
  - nodes
  - pods
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - services/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - scheduling.incubator.k8s.io
  - scheduling.volcano.sh
  resources:
  - queues
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - scheduling.incubator.k8s.io
  - scheduling.volcano.sh
  resources:
  - queues/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - scheduling.volcano.sh
  resources:
  - podgroups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - imagemakers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - imagemakers/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - imagemakers/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - notebooks
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - notebooks/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - notebooks/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - resourcepools
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - resourcepools/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - resourcepools/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - syncactions
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - syncactions/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - syncactions/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - tensorboards
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - tensorboards/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - tensorboards/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - trainingjobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - trainingjobs/finalizers
  verbs:
  - update
- apiGroups:
  - system.hero.ai
  resources:
  - trainingjobs/status
  verbs:
  - get
  - patch
  - update
