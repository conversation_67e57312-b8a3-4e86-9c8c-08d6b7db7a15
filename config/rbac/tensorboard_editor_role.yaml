# permissions for end users to edit tensorboards.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: tensorboard-editor-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: hero-controllers
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
  name: tensorboard-editor-role
rules:
- apiGroups:
  - system.hero.ai
  resources:
  - tensorboards
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - tensorboards/status
  verbs:
  - get
