apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: metrics-reader
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/created-by: hero-controllers
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
  name: metrics-reader
rules:
- nonResourceURLs:
  - "/metrics"
  verbs:
  - get
