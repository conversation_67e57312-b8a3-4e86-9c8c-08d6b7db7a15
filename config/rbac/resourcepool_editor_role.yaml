# permissions for end users to edit resourcepools.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: resourcepool-editor-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: hero-controllers
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
  name: resourcepool-editor-role
rules:
- apiGroups:
  - system.hero.ai
  resources:
  - resourcepools
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - system.hero.ai
  resources:
  - resourcepools/status
  verbs:
  - get
