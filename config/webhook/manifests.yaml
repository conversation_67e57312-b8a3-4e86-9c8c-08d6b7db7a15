---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  creationTimestamp: null
  name: mutating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-system-hero-ai-v1alpha1-imagemaker
  failurePolicy: Fail
  name: mimagemaker.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - imagemakers
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-system-hero-ai-v1alpha1-notebook
  failurePolicy: Fail
  name: mnotebook.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - notebooks
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-system-hero-ai-v1alpha1-resourcepool
  failurePolicy: Fail
  name: mresourcepool.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - resourcepools
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-system-hero-ai-v1alpha1-tensorboard
  failurePolicy: Fail
  name: mtensorboard.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - tensorboards
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-system-hero-ai-v1alpha1-trainingjob
  failurePolicy: Fail
  name: mtrainingjob.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - trainingjobs
  sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  creationTimestamp: null
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-system-hero-ai-v1alpha1-imagemaker
  failurePolicy: Fail
  name: vimagemaker.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - imagemakers
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-system-hero-ai-v1alpha1-notebook
  failurePolicy: Fail
  name: vnotebook.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - notebooks
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-system-hero-ai-v1alpha1-resourcepool
  failurePolicy: Fail
  name: vresourcepool.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - resourcepools
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-system-hero-ai-v1alpha1-tensorboard
  failurePolicy: Fail
  name: vtensorboard.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - tensorboards
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-system-hero-ai-v1alpha1-trainingjob
  failurePolicy: Fail
  name: vtrainingjob.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - trainingjobs
  sideEffects: None
