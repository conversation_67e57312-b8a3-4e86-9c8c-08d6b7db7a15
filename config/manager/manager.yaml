apiVersion: v1
kind: Namespace
metadata:
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: namespace
    app.kubernetes.io/instance: system
    app.kubernetes.io/component: manager
    app.kubernetes.io/created-by: hero-controllers
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
  name: system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: system
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: deployment
    app.kubernetes.io/instance: controller-manager
    app.kubernetes.io/component: manager
    app.kubernetes.io/created-by: hero-controllers
    app.kubernetes.io/part-of: hero-controllers
    app.kubernetes.io/managed-by: kustomize
spec:
  selector:
    matchLabels:
      control-plane: controller-manager
  replicas: 1
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
      # TODO(user): Uncomment the following code to configure the nodeAffinity expression
      # according to the platforms which are supported by your solution.
      # It is considered best practice to support multiple architectures. You can
      # build your manager image using the makefile target docker-buildx.
      # affinity:
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #         - matchExpressions:
      #           - key: kubernetes.io/arch
      #             operator: In
      #             values:
      #               - amd64
      #               - arm64
      #               - ppc64le
      #               - s390x
      #           - key: kubernetes.io/os
      #             operator: In
      #             values:
      #               - linux
      securityContext:
        runAsNonRoot: true
        # TODO(user): For common cases that do not require escalating privileges
        # it is recommended to ensure that all your Pods/Containers are restrictive.
        # More info: https://kubernetes.io/docs/concepts/security/pod-security-standards/#restricted
        # Please uncomment the following code if your project does NOT have to work on old Kubernetes
        # versions < 1.19 or on vendors versions which do NOT support this field by default (i.e. Openshift < 4.11 ).
        # seccompProfile:
        #   type: RuntimeDefault
      initContainers:
      - name: init-ca
        image: registry.bitahub.com:5000/leinaoyun-tag/job-controllers-init:latest
        env:
        - name: SERVICENAME
          value: job-controllers
        - name: CA_CERT_FILE
          value: "/tmp/k8s-webhook-server/serving-certs/ca.crt"
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: webhook-secret
          readOnly: true
      containers:
      - command:
        - /manager
        args:
        - --leader-elect
        image: controller:latest
        name: manager
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - "ALL"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        # TODO(user): Configure the resources accordingly based on the project requirements.
        # More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        volumeMounts:
        - name: secret-volume
          mountPath: /etc/secret
          readOnly: true
        env:
        - name: DOMAIN
          value: https://easytraining-stg2.cnbita.com:11443
        - name: HOST
          value: www.bitahub.com
        - name: TLSSECRETNAME
          value: bitahub-ssl
        - name: NANESPACE
          value: heros-user
        - name: IMAGEBUILDMAXTIME
          value: 30m
        - name: LOCALURL
          value: http://hero-controllers-controller-manager-server.heros-controllers-system.svc.cluster.local:8088
        - name: IMAGEREPO
          value: https://registry.cnbita.com:5000
        - name: TOOLIMAGE
          value: registry.cnbita.com:5000/leinaoyun-tag/imagebuildtool:v0.5
        - name: VNCBASEURL
          value: http://***********:30086/vnc.html?path=websockify/?token=
        - name: TOKENPATH
          value: /etc/websockify/token/token.conf
        volumeMounts:
        - mountPath: /etc/websockify/token
          name: token-path
        imagePullPolicy: Always
      serviceAccountName: controller-manager
      terminationGracePeriodSeconds: 10
      volumes:
      - name: token-path
        hostPath:
          path: /gdata/vnctoken
          type: DirectoryOrCreate
      - name: secret-volume
        secret:
          secretName: private-image-secret
