apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: novnc-web
    app.kubernetes.io/instance: job-controller-dev61
  name: novnc-web
  namespace: hero-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: novnc-web
  template:
    metadata:
      labels:
        app: novnc-web
    spec:
      containers:
      - image: registry.bitahub.com:5000/leinaoyun-tag/websockify:latest
        imagePullPolicy: Always
        name: websockify-container
        ports:
        - containerPort: 6081
          protocol: TCP
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /websockify/token
          name: token-config
        - mountPath: /etc/secret
          name: cert
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      initContainers:
      - name: cert-init
        image: registry.cnbita.com:5000/heros-stg/init-config:v1.0.0
        imagePullPolicy: Always
        env:
        - name: NACOS_SERVER_URL
          value: nacos-server-service.basic-component.svc:8848
        volumeMounts:
        - mountPath: /etc/secret
          name: cert
      volumes:
      - hostPath:
          path: /gdata/vnctoken
          type: ""
        name: token-config
      - emptyDir: {}
        name: cert

      