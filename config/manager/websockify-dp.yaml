apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: default
  name: novnc-web
  labels:
    app: novnc-web
spec:
  selector:
    matchLabels:
      app: novnc-web
  replicas: 1
  template:
    metadata:
      labels:
        app: novnc-web
    spec:
      containers:
       - name: websockify-container
         image: registry.bitahub.com:5000/leinaoyun-tag/websockify:latest
         ports:
         - containerPort: 6081
         volumeMounts:
           - mountPath: /websockify/token
             name: token-config
         imagePullPolicy: Always
      restartPolicy: Always
      volumes:
        - name: token-config
          hostPath:
            path: /gdata/vnctoken
---

apiVersion: v1            
kind: Service             
metadata:             
  name: novnc-web       
  namespace: default
spec:
  selector:
    app: novnc-web
  type: ClusterIP                 
  ports:              
  - protocol: TCP
    port: 6081
    targetPort: 6081
    name: websockify
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novnc-web
  namespace: default
spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - backend:
          service:
            name: novnc-web
            port:
              number: 6081
        path: /
        pathType: ImplementationSpecific