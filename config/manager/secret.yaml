apiVersion: batch/v1
kind: Job
metadata:
  name: job-controllers-admission-init
  namespace: heros-controllers-system
  labels:
    app: job-controllers-admission-init
spec:
  backoffLimit: 3
  template:
    spec:
      serviceAccountName: controller-manager
      priorityClassName: system-cluster-critical
      restartPolicy: Never
      containers:
        - name: main
          image: registry.bitahub.com:5000/leinaoyun-tag/hero-controllers:v0.1.15
          imagePullPolicy: Always
          command: ["./gen-admission-secret.sh", "--service", "webhook-service", "--namespace",
                    "heros-controllers-system", "--secret", "webhook-server-cert"]