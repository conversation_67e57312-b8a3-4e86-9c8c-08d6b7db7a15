#!/bin/bash

export NACOS_SERVER_URL="10.0.102.61:38848"
export JASYPT_ENCRYPTOR_PASSWORD="123#lei@nao@ai@#123"

go mod vendor
mkdir -p sonar/reports
golangci-lint run --config=./.golangci.yml ./... --out-format checkstyle > sonar/reports/report.xml
go test ./... -json > sonar/reports/test-report.out
go test ./... -v -coverprofile=sonar/reports/coverage.out 2>&1 | go-junit-report > sonar/reports/test-report.xml
go tool cover -func="sonar/reports/coverage.out"