# Default values for file-adapter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
name: job-controllers
replicaCount: 1

image:
  imagePath: registry.bitahub.com:5000/leinaoyun-tag/controller-manager:latest
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVers·ion.

imagePullSecrets: []

domain: https://easytraining-stg2.cnbita.com:11443

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: controller-manager

podAnnotations: {}

service:
  port: 443
  targetPort: 9443

namespace: hero-system

recycleConfigmap:
  config.yaml: |-
    isRecycle: true
    object:
      trainingjob:
        recycletimeHour: 5
        delayTimeHour: 5
      notebook:
        recycletimeHour: 5
        delayTimeHour: 5
      imagemaker:
        recycletimeHour: 1
        delayTimeHour: 1
      tensorboard:
        recycletimeHour: 5
        delayTimeHour: 5

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
#   memory: 128Mi




tolerations: []

affinity: {}

# datasource ephemeral storage limit, unit is Mi
ephemeralStorageLimit: 500
