apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  labels:
    app.kubernetes.io/name: certificate
    app.kubernetes.io/instance: serving-cert
    app.kubernetes.io/component: certificate
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
  name: selfsigned-issuer
  namespace: {{ .Values.namespace | default "system"}}
spec:
  selfSigned: {}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  labels:
    app.kubernetes.io/name: certificate
    app.kubernetes.io/instance: serving-cert
    app.kubernetes.io/component: certificate
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
  name: serving-cert
  namespace: {{ .Values.namespace | default "system"}}
spec:
  dnsNames:
  - webhook-service.{{ .Values.namespace | default "system"}}.svc
  - webhook-service.{{ .Values.namespace | default "system"}}.svc.cluster.local
  issuerRef:
    kind: Issuer
    name: selfsigned-issuer
  secretName: webhook-server-cert 
