apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: mutating-webhook-configuration
  labels:
    app.kubernetes.io/name: validatingwebhookconfiguration
    app.kubernetes.io/instance: validating-webhook-configuration
    app.kubernetes.io/component: webhook
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
  annotations:
    cert-manager.io/inject-ca-from: {{ .Values.namespace | default "system"}}/serving-cert
webhooks:
  - admissionReviewVersions:
    - v1
    clientConfig:
      service:
        name: webhook-service
        namespace: {{ .Values.namespace | default "system"}}
        path: /mutate-system-hero-ai-v1alpha1-imagemaker
    failurePolicy: Fail
    name: mimagemaker.kb.io
    sideEffects: None
    rules:
    - apiGroups:
      - system.hero.ai
      apiVersions:
      - v1alpha1
      operations:
      - CREATE
      - UPDATE
      resources:
      - imagemakers
    timeoutSeconds: 10
  - admissionReviewVersions:
    - v1
    clientConfig:
      service:
        name: webhook-service
        namespace: {{ .Values.namespace | default "system"}}
        path: /mutate-system-hero-ai-v1alpha1-notebook
    failurePolicy: Fail
    name: mnotebook.kb.io
    rules:
    - apiGroups:
      - system.hero.ai
      apiVersions:
      - v1alpha1
      operations:
      - CREATE
      - UPDATE
      resources:
      - notebooks
    sideEffects: None
  - admissionReviewVersions:
    - v1
    clientConfig:
      service:
        name: webhook-service
        namespace: {{ .Values.namespace | default "system"}}
        path: /mutate-system-hero-ai-v1alpha1-resourcepool
    failurePolicy: Fail
    name: mresourcepool.kb.io
    rules:
    - apiGroups:
      - system.hero.ai
      apiVersions:
      - v1alpha1
      operations:
      - CREATE
      - UPDATE
      resources:
      - resourcepools
    sideEffects: None
  - admissionReviewVersions:
    - v1
    clientConfig:
      service:
        name: webhook-service
        namespace: {{ .Values.namespace | default "system"}}
        path: /mutate-system-hero-ai-v1alpha1-tensorboard
    failurePolicy: Fail
    name: mtensorboard.kb.io
    rules:
    - apiGroups:
      - system.hero.ai
      apiVersions:
      - v1alpha1
      operations:
      - CREATE
      - UPDATE
      resources:
      - tensorboards
    sideEffects: None
  - admissionReviewVersions:
    - v1
    clientConfig:
      service:
        name: webhook-service
        namespace: {{ .Values.namespace | default "system"}}
        path: /mutate-system-hero-ai-v1alpha1-trainingjob
    failurePolicy: Fail
    name: mtrainingjob.kb.io
    rules:
    - apiGroups:
      - system.hero.ai
      apiVersions:
      - v1alpha1
      operations:
      - CREATE
      - UPDATE
      resources:
      - trainingjobs
    sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validating-webhook-configuration
  labels:
    app.kubernetes.io/name: validatingwebhookconfiguration
    app.kubernetes.io/instance: validating-webhook-configuration
    app.kubernetes.io/component: webhook
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
  annotations:
    cert-manager.io/inject-ca-from: {{ .Values.namespace | default "system"}}/serving-cert
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: {{ .Values.namespace | default "system"}}
      path: /validate-system-hero-ai-v1alpha1-imagemaker
  failurePolicy: Fail
  name: vimagemaker.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - imagemakers
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: {{ .Values.namespace | default "system"}}
      path: /validate-system-hero-ai-v1alpha1-notebook
  failurePolicy: Fail
  name: vnotebook.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - notebooks
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: {{ .Values.namespace | default "system"}}
      path: /validate-system-hero-ai-v1alpha1-resourcepool
  failurePolicy: Fail
  name: vresourcepool.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - resourcepools
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: {{ .Values.namespace | default "system"}}
      path: /validate-system-hero-ai-v1alpha1-tensorboard
  failurePolicy: Fail
  name: vtensorboard.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - tensorboards
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: {{ .Values.namespace | default "system"}}
      path: /validate-system-hero-ai-v1alpha1-trainingjob
  failurePolicy: Fail
  name: vtrainingjob.kb.io
  rules:
  - apiGroups:
    - system.hero.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - trainingjobs
  sideEffects: None
  