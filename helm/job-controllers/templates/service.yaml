apiVersion: v1
kind: Service
metadata:
  name: webhook-service
  labels:
    app.kubernetes.io/name: service
    app.kubernetes.io/instance: webhook-service
    app.kubernetes.io/component: webhook
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
  namespace: {{ .Values.namespace | default "system"}}    
spec:
  ports:
  - port: {{ .Values.service.port }}
    targetPort: {{ .Values.service.targetPort }}
    protocol: TCP
  selector:
    control-plane: {{ .Values.name | default "controller-manager"}}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.name | default "controller-manager"}}-metrics-service
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: service
    app.kubernetes.io/instance: controller-manager-metrics-service
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
  namespace: {{ .Values.namespace | default "system"}}    
spec:
  ports:
  - port: 8443
    targetPort: https
    protocol: TCP
    name: https
  selector:
    control-plane: {{ .Values.name | default "controller-manager"}}
