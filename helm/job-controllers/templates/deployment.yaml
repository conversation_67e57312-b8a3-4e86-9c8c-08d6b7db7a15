apiVersion: v1
kind: ServiceAccount
metadata:
  name:  {{ .Values.name | default "controller-manager"}}
  namespace: {{ .Values.namespace| default "system" }}
  labels:
    app.kubernetes.io/name: serviceaccount
    app.kubernetes.io/instance: controller-manager-sa
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of:  {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: manager-role
rules:
- apiGroups: ["batch.volcano.sh"]
  resources: ["jobs"]
  verbs: ["create", "get", "list", "watch", "delete", "patch", "update"]
- apiGroups: ["bus.volcano.sh"]
  resources: ["commands"]
  verbs: ["create", "get", "list", "watch", "delete", "patch", "update"]
- apiGroups: [""]
  resources: ["events", "nodes", "pods", "services"]
  verbs: ["create", "get", "list", "watch", "delete", "patch", "update"]
- apiGroups: [""]
  resources: ["pods/status", "services/status"]
  verbs: ["get", "patch", "update"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["create", "get", "list", "watch", "delete", "patch", "update"]
- apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
  resources: ["queues"]
  verbs: ["create", "get", "list", "watch", "update", "bind", "delete", "patch"]
- apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
  resources: ["queues/status"]
  verbs: ["update", "patch", "get"]
- apiGroups: ["scheduling.volcano.sh"]
  resources: ["podgroups"]
  verbs: ["create", "get", "list", "watch", "delete", "patch", "update"]
- apiGroups: ["system.hero.ai"]
  resources: ["imagemakers", "notebooks", "resourcepools", "tensorboards", "trainingjobs"]
  verbs: ["create", "get", "list", "watch", "update", "bind", "delete", "patch"]
- apiGroups: ["system.hero.ai"]
  resources: ["imagemakers/finalizers", "imagemakers/status", "notebooks/finalizers", "notebooks/status", "resourcepools/finalizers", "resourcepools/status", "tensorboards/finalizers", "tensorboards/status", "trainingjobs/finalizers", "trainingjobs/status"]
  verbs: ["get", "patch", "update"]
 
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: manager-rolebinding
  labels:
    app.kubernetes.io/name: clusterrolebinding
    app.kubernetes.io/instance: manager-rolebinding
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of:  {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
subjects:
- kind: ServiceAccount
  name:  {{ .Values.name | default "controller-manager"}}
  namespace:  {{ .Values.namespace | default "system"}}
roleRef:
  kind: ClusterRole
  name: manager-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.name | default "controller-manager"}}
  labels:
    app.kubernetes.io/name: deployment
    app.kubernetes.io/instance: {{ .Values.name | default "controller-manager"}}
    app.kubernetes.io/component: manager
    app.kubernetes.io/created-by: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/part-of: {{ .Values.name | default "job-controllers"}}
    app.kubernetes.io/managed-by: kustomize
    control-plane: {{ .Values.name | default "controller-manager"}}
  namespace: {{ .Values.namespace | default "system"}}    
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      control-plane: {{ .Values.name | default "controller-manager"}}
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: {{ .Values.name | default "controller-manager"}}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: manager
        image: "{{ .Values.image.imagePath }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        command:
        - /manager
        args:
        - --leader-elect
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - "ALL"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        ports:
        - containerPort: 9443
          name: webhook-server
          protocol: TCP
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        env:
        - name: DOMAIN
          value:  {{ .Values.domain | default "https://easytraining-stg2.cnbita.com:11443"}}
        - name: IMAGEBUILDMAXTIME
          value:  {{ .Values.imagebuildmaxtime | default "30m"}}
        - name: NANESPACE
          value: heros-user
        - name: IMAGEREPO
          value:  https://registry.cnbita.com:5000
        - name: TOOLIMAGE
          value: registry.cnbita.com:5000/leinaoyun-tag/imagebuildtool:v0.5
        - name: VNCBASEURL
          value: http://10.0.102.45:30086/vnc.html?path=websockify/?token=
        - name: TOKENPATH
          value: /etc/websockify/token/token.conf
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
        - mountPath: /etc/websockify/token 
          name: token-path
        - mountPath: /etc/recycleconfig  
          name: config-recycletime    
      - name: kube-rbac-proxy
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - "ALL"
        image: quay.io/brancz/kube-rbac-proxy:v0.13.1
        args:
        - "--secure-listen-address=0.0.0.0:8443"
        - "--upstream=http://127.0.0.1:8080/"
        - "--logtostderr=true"
        - "--v=0"
        ports:
        - containerPort: 8443
          protocol: TCP
          name: https
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 5m
            memory: 64Mi
      serviceAccountName:  {{ .Values.serviceAccount.name | default "controller-manager"}}
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName: webhook-server-cert
      - name: token-path
        hostPath:
          path: /gdata/vnctoken
          type: Directory
      - name: config-recycletime
            configMap:
              name: recycleconfig

---
apiVersion: v1
{{- with .Values.recycleConfigmap }}
data:
  {{- toYaml . | nindent 2 }}
{{- end }}
kind: ConfigMap
metadata:
  name: recycleconfig


