---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.3
  creationTimestamp: null
  name: tensorboards.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: Tensorboard
    listKind: TensorboardList
    plural: tensorboards
    singular: tensorboard
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Tensorboard is the Schema for the tensorboards API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: TensorboardSpec defines the desired state of Tensorboard
            properties:
              dataSource:
                properties:
                  dataType:
                    type: string
                  mountPath:
                    type: string
                  name:
                    type: string
                  readOnly:
                    type: boolean
                  volumeName:
                    type: string
                  volumeSubPath:
                    type: string
                required:
                - volumeName
                type: object
              imageUrl:
                type: string
              maxRunningTimeMinutes:
                format: int32
                type: integer
              resourceConfig:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: ResourceList is a set of (resource name, quantity) pairs.
                type: object
            type: object
          status:
            description: TensorboardStatus defines the observed state of Tensorboard
            properties:
              createTime:
                format: date-time
                type: string
              runningDuration:
                description: TensorboardUrl is the url of the tensorboard
                type: string
              startTime:
                format: date-time
                type: string
              state:
                properties:
                  lastTransitionTime:
                    format: date-time
                    type: string
                  message:
                    type: string
                  phase:
                    enum:
                    - Pending
                    - Running
                    - Stopped
                    - Stopping
                    type: string
                  reason:
                    type: string
                type: object
              stoppedTime:
                format: date-time
                type: string
              tensorboardUrl:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
