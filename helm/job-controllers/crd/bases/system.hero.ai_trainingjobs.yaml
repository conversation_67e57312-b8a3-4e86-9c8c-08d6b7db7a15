---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.3
  creationTimestamp: null
  name: trainingjobs.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: TrainingJob
    listKind: TrainingJobList
    plural: trainingjobs
    shortNames:
    - tj
    - train
    singular: trainingjob
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.state.phase
      name: STATUS
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: TrainingJob is the Schema for the trainingjobs API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: TrainingJobSpec defines the desired state of TrainingJob
            properties:
              codeSource:
                properties:
                  accessName:
                    type: string
                  accessToken:
                    type: string
                  branch:
                    type: string
                  gitUrl:
                    type: string
                  mountPath:
                    type: string
                type: object
              dataSources:
                items:
                  properties:
                    dataType:
                      type: string
                    mountPath:
                      type: string
                    name:
                      type: string
                    readOnly:
                      type: boolean
                    volumeName:
                      type: string
                    volumeSubPath:
                      type: string
                  required:
                  - volumeName
                  type: object
                type: array
              envs:
                items:
                  properties:
                    name:
                      type: string
                    value:
                      type: string
                  type: object
                type: array
              imageUrl:
                type: string
              maxRetryCount:
                format: int32
                type: integer
              plugins:
                items:
                  type: string
                type: array
              resourcePool:
                type: string
              tasks:
                items:
                  properties:
                    command:
                      type: string
                    extendResource:
                      properties:
                        cpuArch:
                          type: string
                        sharedMemory:
                          type: string
                      type: object
                    minAvaluable:
                      format: int32
                      type: integer
                    name:
                      type: string
                    replicas:
                      format: int32
                      type: integer
                    resource:
                      additionalProperties:
                        anyOf:
                        - type: integer
                        - type: string
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      description: ResourceList is a set of (resource name, quantity)
                        pairs.
                      type: object
                    taskType:
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: TrainingJobStatus defines the observed state of TrainingJob
            properties:
              completionTime:
                type: string
              conditions:
                additionalProperties:
                  properties:
                    completedTime:
                      description: Time at which this node completed
                      type: string
                    launchedTime:
                      description: Time at which this node started
                      type: string
                    message:
                      description: A human readable message indicating details about
                        why the node is in this condition.
                      type: string
                    name:
                      description: Name is unique name in the node tree used to generate
                        the node ID
                      type: string
                    phase:
                      description: Phase a simple, high-level summary of where the
                        node is in its lifecycle. Can be used as a state machine.
                      type: string
                    taskName:
                      description: TaskName is unique name in the node tree used to
                        generate the node ID
                      type: string
                  type: object
                type: object
              createTime:
                type: string
              retryCount:
                format: int32
                type: integer
              startTime:
                type: string
              state:
                properties:
                  lastTransitionTime:
                    type: string
                  message:
                    type: string
                  phase:
                    enum:
                    - Pending
                    - Queuing
                    - Running
                    - Stopping
                    - Stopped
                    - Failed
                    - Completed
                    type: string
                  reason:
                    type: string
                type: object
              stoppedTime:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
