---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.3
  creationTimestamp: null
  name: resourcepools.system.hero.ai
spec:
  group: system.hero.ai
  names:
    kind: ResourcePool
    listKind: ResourcePoolList
    plural: resourcepools
    shortNames:
    - rp
    singular: resourcepool
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.nodes
      name: NODES
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ResourcePool is the Schema for the resourcepools API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ResourcePoolSpec defines the desired state of ResourcePool
            properties:
              Description:
                type: string
              capabilities:
                additionalProperties:
                  additionalProperties:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  description: ResourceList is a set of (resource name, quantity)
                    pairs.
                  type: object
                type: object
              nodes:
                description: Foo is an example field of ResourcePool. Edit resourcepool_types.go
                  to remove/update
                items:
                  type: string
                type: array
            type: object
          status:
            description: ResourcePoolStatus defines the observed state of ResourcePool
            properties:
              allocated:
                additionalProperties:
                  additionalProperties:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  description: ResourceList is a set of (resource name, quantity)
                    pairs.
                  type: object
                type: object
              createTime:
                type: string
              idle:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: ResourceList is a set of (resource name, quantity) pairs.
                type: object
              state:
                description: 'INSERT ADDITIONAL STATUS FIELD - define observed state
                  of cluster Important: Run "make" to regenerate code after modifying
                  this file'
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
