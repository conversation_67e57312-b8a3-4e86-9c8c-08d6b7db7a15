package v1alpha1

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTensorboardWebhookDefault(t *testing.T) {
	License = false
	tensorboard := &Tensorboard{
		Spec: TensorboardSpec{
			MaxRunningTimeMinutes: 0, // 测试时设置为默认值
		},
	}

	tensorboard.Default()

	assert.NotNil(t, tensorboard.Status.CreateTime)
	assert.Equal(t, TBPending, tensorboard.Status.State.Phase)
	assert.Equal(t, int32(30), tensorboard.Spec.MaxRunningTimeMinutes)
}

func TestTensorboardWebhookValidateCreate(t *testing.T) {
	License = false
	tensorboard := &Tensorboard{}

	err := tensorboard.ValidateCreate()
	assert.NoError(t, err)

	// 测试不合法情况
	License = true
	err = tensorboard.ValidateUpdate(tensorboard)
	assert.Error(t, err)
}

func TestTensorboardWebhookValidateUpdate(t *testing.T) {
	License = false
	oldTensorboard := &Tensorboard{}
	newTensorboard := &Tensorboard{}

	err := newTensorboard.ValidateUpdate(oldTensorboard)
	assert.NoError(t, err)

	// 测试不合法情况
	License = true
	err = newTensorboard.ValidateUpdate(newTensorboard)
	assert.Error(t, err)
}

func TestTensorboardWebhookValidateDelete(t *testing.T) {
	License = false
	tensorboard := &Tensorboard{}

	err := tensorboard.ValidateDelete()
	assert.NoError(t, err)

	// 测试不合法情况
	License = true
	err = tensorboard.ValidateUpdate(tensorboard)
	assert.Error(t, err)
}
