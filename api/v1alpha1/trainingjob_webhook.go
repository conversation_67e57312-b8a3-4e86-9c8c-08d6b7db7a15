//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog"
	ctrl "sigs.k8s.io/controller-runtime"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

const (
	DefaultVirtualCluster = "default"
	DefaultMaxRetryCount  = 3
	PrefixGitHttpsRepo    = "https://"
	PrefixGitHttpRepo     = "http://"
	VcjobNameLabels       = "system.hero.ai/job-name"
	VcjobNsLabels         = "system.hero.ai/job-namespace"
	NodeLabelKey          = "resourcepool.system.hero.ai"
	VcjobNbLabels         = "system.hero.ai/notebook"
	VcjobTjLabels         = "system.hero.ai/trainingjob"
)

var (
	License bool
	Mutex   sync.Mutex
)

// log is for logging in this package.
var trainingjoblog = logf.Log.WithName("trainingjob-resource")

func (r *TrainingJob) SetupWebhookWithManager(mgr ctrl.Manager) error {
	return ctrl.NewWebhookManagedBy(mgr).
		For(r).
		Complete()
}

// TODO(user): EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!

//+kubebuilder:webhook:path=/mutate-system-hero-ai-v1alpha1-trainingjob,mutating=true,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=trainingjobs,verbs=create;update,versions=v1alpha1,name=mtrainingjob.kb.io,admissionReviewVersions=v1

var _ webhook.Defaulter = &TrainingJob{}

// Default implements webhook.Defaulter so a webhook will be registered for the type
func (r *TrainingJob) Default() {
	trainingjoblog.Info("default", "name", r.Name)

	// if len(r.Spec.ResourcePool) == 0 {
	// 	r.Spec.ResourcePool = DefaultVirtualCluster
	// 	trainingjoblog.Info("ResourcePool is nil, set default value now", "ResourcePool", r.Spec.ResourcePool)
	// }
	if r.Spec.MaxRetryCount == 0 {
		r.Spec.MaxRetryCount = DefaultMaxRetryCount
		trainingjoblog.Info("MaxRetryCount is nil, set default value now", "MaxRetryCount", r.Spec.MaxRetryCount)
	}
	for i := range r.Spec.Tasks {
		task := &r.Spec.Tasks[i]
		if task.MinAvaluable == 0 {
			task.MinAvaluable = task.Replicas
		}
	}

	r.addJobLabels()
}

// TODO(user): change verbs to "verbs=create;update;delete" if you want to enable deletion validation.
//+kubebuilder:webhook:path=/validate-system-hero-ai-v1alpha1-trainingjob,mutating=false,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=trainingjobs,verbs=create;update,versions=v1alpha1,name=vtrainingjob.kb.io,admissionReviewVersions=v1

var _ webhook.Validator = &TrainingJob{}

// ValidateCreate implements webhook.Validator so a webhook will be registered for the type
func (r *TrainingJob) ValidateCreate() error {
	trainingjoblog.Info("validate create", "name", r.Name)
	klog.Info("----------------------start ValidateCreate----------------------------")

	if err := checkLicense(); err != nil {
		return err
	}

	// TODO(user): fill in your validation logic upon object creation.
	err := r.checkCodeRepo()
	if err != nil {
		return err
	}

	if !r.checkStorageType() {
		return errors.New("no found storage type, datatype is err")
	}

	if err = r.checkAscendNPU(); err != nil {
		return err
	}

	if err = r.checkSharedMemory(); err != nil {
		return err
	}

	if err = r.checkTask(); err != nil {
		return err
	}
	return r.validateFaultToleranceCreate()
}

// ValidateUpdate implements webhook.Validator so a webhook will be registered for the type
func (r *TrainingJob) ValidateUpdate(old runtime.Object) error {
	trainingjoblog.Info("validate update", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}
	// TODO(user): fill in your validation logic upon object update.
	return nil
}

// ValidateDelete implements webhook.Validator so a webhook will be registered for the type
func (r *TrainingJob) ValidateDelete() error {
	trainingjoblog.Info("validate delete", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}

	return nil
}

func (r *TrainingJob) checkTask() error {
	if r == nil {
		return errors.New("job is nil")
	}

	if len(r.Spec.Tasks) == 0 {
		return errors.New("the task must num more than zero")
	}

	for index, v := range r.Spec.Tasks {
		// cpuarch
		if len(v.ExtendResource.CPUArch) > 0 {
			if v.ExtendResource.CPUArch != "amd64" && v.ExtendResource.CPUArch != "arm64" {
				return errors.New("cpuArch must be amd64 or arm64, but get " + v.ExtendResource.CPUArch)
			}
		}

		if len(v.ResourcePool) == 0 {
			r.Spec.Tasks[index].ResourcePool = DefaultVirtualCluster
			trainingjoblog.Info("ResourcePool is nil, set default value now", "ResourcePool", r.Spec.ResourcePool)
		}
	}

	return nil
}

func (r *TrainingJob) addJobLabels() {
	if r.Labels == nil {
		r.Labels = make(map[string]string)
	}
	if _, found := r.Labels[VcjobNameLabels]; !found {
		r.Labels[VcjobNameLabels] = r.Name
		r.Labels[VcjobNsLabels] = r.Namespace

		var rps []string
		for _, v := range r.Spec.Tasks {
			rps = append(rps, v.ResourcePool)
			break
		}

		r.Labels[NodeLabelKey] = strings.Join(rps, "-")
	}
}

func (r *TrainingJob) checkCodeRepo() error {
	if len(r.Spec.CodeSource.AccessToken) > 0 {
		if !strings.Contains(r.Spec.CodeSource.GitUrl, PrefixGitHttpsRepo) && !strings.Contains(r.Spec.CodeSource.GitUrl, PrefixGitHttpRepo) {
			return errors.New("private code repo must to be https or http")
		}
	}
	return nil
}

// 存储类型校验
func (r *TrainingJob) checkStorageType() bool {
	for _, ds := range r.Spec.DataSources {
		if ds.DataType != Volume {
			return false
		}
	}

	return true
}

func (r *TrainingJob) checkSharedMemory() error {
	for _, task := range r.Spec.Tasks {
		if len(task.ExtendResource.SharedMemory) > 0 {
			if _, err := strconv.Atoi(task.ExtendResource.SharedMemory); err != nil {
				return errors.New("SharedMemory must be a numeric string")
			}
		}
	}

	return nil
}

func (r *TrainingJob) checkAscendNPU() error {
	allowedNPUNumber := []int64{1, 2, 4, 8}
	for i := range r.Spec.Tasks {
		task := &r.Spec.Tasks[i]
		for resourceName := range task.Resource {
			if !strings.Contains(strings.ToLower(string(resourceName)), "ascend") {
				continue
			}
			q := task.Resource[resourceName]
			npuNum := q.Value()

			// 单机训练时，华为昇腾 NPU 数量只能为 1,2,4,8
			if task.Replicas == 1 {
				if !slices.Contains(allowedNPUNumber, npuNum) {
					return fmt.Errorf("the number of requests of Ascend NPU must be one of (1, 2, 4, 8) if replicas is 1")
				}
			}

			// 多机训练时，每个节点华为昇腾 NPU 数量必须都是 8
			if task.Replicas > 1 && npuNum != 8 {
				return fmt.Errorf("the number of requests of Ascend NPU must be 8 if replicas is greater than 1")
			}
		}
	}
	return nil
}

func (r *TrainingJob) validateFaultToleranceCreate() error {
	if r.Spec.FaultTolerance == nil {
		return nil
	}

	if err := r.validateHealthCheckCreate(); err != nil {
		return err
	}

	if err := r.validateRestartPolicyCreate(); err != nil {
		return err
	}

	return nil
}

func (r *TrainingJob) validateHealthCheckCreate() error {
	for i := range r.Spec.FaultTolerance.HealthChecks {
		healthCheck := &r.Spec.FaultTolerance.HealthChecks[i]
		switch healthCheck.Name {
		case NodeHealthBeforeStartCheck:
		case JobHangCheck:
			var args JobHangCheckArgs
			if err := json.Unmarshal(healthCheck.Args.Raw, &args); err != nil {
				return fmt.Errorf("invalid %s healthCheck args: %v", JobHangCheck, err)
			}
			if args.MaxLogInterval < 60 {
				return fmt.Errorf("invalid %s healthCheck args: maxLogInterval must be greater than 60", JobHangCheck)
			}
		case FailedJobRestartableCheck:
			var args FailedJobRestartableCheckArgs
			if err := json.Unmarshal(healthCheck.Args.Raw, &args); err != nil {
				return fmt.Errorf("invalid %s healthCheck args: %v", FailedJobRestartableCheck, err)
			}
			supportedPolicies := []FailedJobRestartablePolicy{OnFailureRestartable, ExitCodeAndErrorMsgRestartable, AdvancedRestartable}
			if !slices.Contains(supportedPolicies, args.Policy) {
				return fmt.Errorf("invalid %s healthCheck args: unsupported policy %s, must be one of %+v",
					FailedJobRestartableCheck, args.Policy, supportedPolicies)
			}
		default:
			return fmt.Errorf("unsupported healthCheck name %s, must be one of %+v",
				healthCheck.Name, []HealthCheckType{NodeHealthBeforeStartCheck, JobHangCheck, FailedJobRestartableCheck})
		}
	}
	return nil
}

func (r *TrainingJob) validateRestartPolicyCreate() error {
	if r.Spec.FaultTolerance.RestartPolicy == nil {
		return nil
	}
	if r.Spec.FaultTolerance.RestartPolicy.MaxRetry < 1 {
		return fmt.Errorf(".spec.faultTolerance.restartPolicy.maxRetry must be greater than 0")
	}
	if r.Spec.FaultTolerance.RestartPolicy.Delay < 60 {
		return fmt.Errorf(".spec.faultTolerance.restartPolicy.delay must be greater than 60")
	}
	for _, v := range r.Spec.FaultTolerance.RestartPolicy.Policies {
		if !slices.Contains(AllowedRestartPolices, v) {
			return fmt.Errorf("restart policy %s is not allowed, must be one of %+v", v, AllowedRestartPolices)
		}
	}
	return nil
}
