//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// ImageMakerSpec defines the desired state of ImageMaker
type ImageMakerSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	// ImageUrl is the url of the image to be made
	DestImageUrl string `json:"destImageUrl"`
	Source       Source `json:"source"`
}

type Source struct {
	Type           MakeMethod  `json:"type"`
	Containers     Containers  `json:"containers,omitempty"`
	SourceImageUrl string      `json:"sourceImageUrl,omitempty"`
	DockerfileUrl  string      `json:"dockerfileUrl,omitempty"`
	ImageSecret    ImageSecret `json:"imageSecret,omitempty"`
	DataSource     DataSource  `json:"dataSource,omitempty"`
	CpuArch        string      `json:"cpuArch,omitempty"`
}

// +kubebuilder:validation:Enum=Dockerfile;TarPkg;PublicImage;PrivateImage;Container;UrlDockerfile
type MakeMethod string

const (
	Dockerfile    MakeMethod = "Dockerfile"
	TarPkg        MakeMethod = "TarPkg"
	PublicImage   MakeMethod = "PublicImage"
	PrivateImage  MakeMethod = "PrivateImage"
	Container     MakeMethod = "Container"
	UrlDockerfile MakeMethod = "UrlDockerfile"
)

type Containers struct {
	Name      string        `json:"name,omitempty"`
	Namespace string        `json:"namespace,omitempty"`
	Kind      ContainerKind `json:"kind,omitempty"`
}

type ContainerKind string

const (
	// 添加自定义作业类型
	NotebookType ContainerKind = "Notebook"
)

// +kubebuilder:validation:Enum=Pending;Running;Failed;Succeeded;Stopped;Stopping
type ImageMakerPhase string

const (
	IMPending   ImageMakerPhase = "Pending"
	IMRunning   ImageMakerPhase = "Running"
	IMFailed    ImageMakerPhase = "Failed"
	IMSucceeded ImageMakerPhase = "Succeeded"
	IMStopping  ImageMakerPhase = "Stopping"
	IMStopped   ImageMakerPhase = "Stopped"
)

type ImageMakerState struct {
	Phase ImageMakerPhase `json:"phase,omitempty"`
	// LastPhase          ImageMakerPhase `json:"lastPhase,omitempty"`
	Reason             string      `json:"reason,omitempty"`
	Message            string      `json:"message,omitempty"`
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty"`
}

// ImageMakerStatus defines the observed state of ImageMaker
type ImageMakerStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	State       ImageMakerState `json:"state,omitempty"`
	CreateTime  metav1.Time     `json:"createTime,omitempty"`
	StoppedTime metav1.Time     `json:"stoppedTime,omitempty"`
	CpuArch     string          `json:"cpuArch,omitempty"`
	NodeName    string          `json:"nodeName,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:printcolumn:name="STATUS",type="string",JSONPath=".status.state.phase"
//+kubebuilder:printcolumn:name="NODENAME",type="string",JSONPath=".status.nodeName"
//+kubebuilder:printcolumn:name="AGE",type="date",JSONPath=".metadata.creationTimestamp"
//+kubebuilder:resource:shortName=im;image
//+kubebuilder:subresource:status

// ImageMaker is the Schema for the imagemakers API
type ImageMaker struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ImageMakerSpec   `json:"spec,omitempty"`
	Status ImageMakerStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// ImageMakerList contains a list of ImageMaker
type ImageMakerList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ImageMaker `json:"items"`
}

func init() {
	SchemeBuilder.Register(&ImageMaker{}, &ImageMakerList{})
}
