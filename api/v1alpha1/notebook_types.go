//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type NotebookState string

const (
	//initialize------>初始化
	NotebookStatePending NotebookState = "Pending"
	//starting---->入队
	NotebookStateStartUp NotebookState = "Queuing"
	//running----->运行中
	NotebookStateRunning NotebookState = "Running"
	//stopping----->停止中
	NotebookStateStopping NotebookState = "Stopping"
	//stopped------>已停止
	NotebookStateStopped NotebookState = "Stopped"
	//startFailed----->容器启动异常
	NotebookStateStartFailed NotebookState = "Failed"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// NotebookSpec defines the desired state of Notebook
type NotebookSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Foo is an example field of Notebook. Edit notebook_types.go to remove/update
	//Foo string `json:"foo,omitempty"`
	//jupyter notebook/ssh/gotty/vscode 能力参数
	// +kubebuilder:validation:Required
	ImageUrl       string          `json:"imageUrl,omitempty"`
	ImageSecret    ImageSecret     `json:"imageSecret,omitempty"`
	ResourcePool   string          `json:"resourcePool,omitempty"`
	Command        string          `json:"command,omitempty"`
	SecretName     string          `json:"secretName,omitempty"`
	Plugins        []string        `json:"plugins,omitempty"`
	CodeSource     CodeSource      `json:"codeSource,omitempty"`
	DataSources    []DataSource    `json:"dataSources,omitempty"`
	Resource       v1.ResourceList `json:"resource,omitempty"`
	ExtendResource ExtendResource  `json:"extendResource,omitempty"`
	MaxRunTime     int             `json:"maxRunTime,omitempty"`
	CustomizePorts map[string]int  `json:"customizePorts,omitempty"`
	// +kubebuilder:validation:Required
	MaxRetryCount int32 `json:"maxRetryCount,omitempty"`
}

// NotebookStatus defines the observed state of Notebook
type NotebookStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	State NotebookState `json:"notebookState,omitempty"`
	// +optional
	CreateTime string `json:"createTime,omitempty"`
	// +optional
	StartTime string `json:"startTime,omitempty"`
	// +optional
	StoppedTime string `json:"stoppedTime,omitempty"`
	// +optional
	Jupyter PluginState `json:"jupyter,omitempty"`
	// +optional
	WebTerminal PluginState `json:"webTerminal,omitempty"`
	// +optional
	Vscode PluginState `json:"vscode,omitempty"`
	// +optional
	Vnc PluginState `json:"vnc,omitempty"`
	// +optional
	SSH PluginState `json:"ssh,omitempty"`
	// +optional
	PodDurations TrainingJobDurations `json:"serverDurations,omitempty"`
	// +optional
	RetryCount int32 `json:"retryCount,omitempty"`
	// +optional
	Conditions []NotebookCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"status"`
}

type PluginState struct {
	URL    string `json:"url,omitempty"`
	State  bool   `json:"state,omitempty"`
	VncPWD string `json:"vncPWD,omitempty"`
}

// NotebookCondition contains details for the current condition of this job.
type NotebookCondition struct {
	// Status is the new phase of job after performing the state's action.
	Status NotebookState `json:"status"`
	// Last time the condition transitioned from one phase to another.
	// +optional
	LastTransitionTime *metav1.Time `json:"lastTransitionTime,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:printcolumn:name="STATUS",type="string",JSONPath=".status.notebookState"
//+kubebuilder:printcolumn:name="AGE",type="date",JSONPath=".metadata.creationTimestamp"
//+kubebuilder:resource:shortName=nb;nj

// Notebook is the Schema for the notebooks API
type Notebook struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   NotebookSpec   `json:"spec,omitempty"`
	Status NotebookStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// NotebookList contains a list of Notebook
type NotebookList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Notebook `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Notebook{}, &NotebookList{})
}
