//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// ResourcePoolSpec defines the desired state of ResourcePool
type ResourcePoolSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Foo is an example field of ResourcePool. Edit resourcepool_types.go to remove/update
	Description string `json:"Description,omitempty"`
}

type ResourcePoolState string

const (
	ResourcePoolStateOpen    ResourcePoolState = "Open"    //运行中
	ResourcePoolStateClosed  ResourcePoolState = "Closed"  //已关闭
	ResourcePoolStateFailed  ResourcePoolState = "Failed"  //失败
	ResourcePoolStateClosing ResourcePoolState = "Closing" //关闭中
)

// ResourcePoolStatus defines the observed state of ResourcePool
type ResourcePoolStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	State ResourcePoolState `json:"state,omitempty"`
	// +optional
	Nodes []string `json:"nodes,omitempty"`
	//+optional
	Capabilities map[string]v1.ResourceList `json:"capabilities,omitempty"`
	// +optional
	CreateTime string `json:"createTime,omitempty"`
	// +optional
	Allocated map[string]v1.ResourceList `json:"allocated"`
	// +optional
	Idle v1.ResourceList `json:"idle"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:resource:shortName=rp,scope=Cluster
//+kubebuilder:printcolumn:name="NODES",type="string",JSONPath=".status.nodes"

// ResourcePool is the Schema for the resourcepools API
type ResourcePool struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ResourcePoolSpec   `json:"spec,omitempty"`
	Status ResourcePoolStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// ResourcePoolList contains a list of ResourcePool
type ResourcePoolList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ResourcePool `json:"items"`
}

func init() {
	SchemeBuilder.Register(&ResourcePool{}, &ResourcePoolList{})
}
