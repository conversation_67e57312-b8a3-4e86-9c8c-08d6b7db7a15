//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

// log is for logging in this package.
var resourcepoollog = logf.Log.WithName("resourcepool-resource")

//var cli client.Client

func (r *ResourcePool) SetupWebhookWithManager(mgr ctrl.Manager) error {
	//cli = mgr.GetClient()
	return ctrl.NewWebhookManagedBy(mgr).
		For(r).
		Complete()
}

// TODO(user): EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!

//+kubebuilder:webhook:path=/mutate-system-hero-ai-v1alpha1-resourcepool,mutating=true,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=resourcepools,verbs=create;update,versions=v1alpha1,name=mresourcepool.kb.io,admissionReviewVersions=v1

var _ webhook.Defaulter = &ResourcePool{}

// Default implements webhook.Defaulter so a webhook will be registered for the type
func (r *ResourcePool) Default() {
	resourcepoollog.Info("default", "name", r.Name)

	// TODO(user): fill in your defaulting logic.
}

// TODO(user): change verbs to "verbs=create;update;delete" if you want to enable deletion validation.
//+kubebuilder:webhook:path=/validate-system-hero-ai-v1alpha1-resourcepool,mutating=false,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=resourcepools,verbs=create;update,versions=v1alpha1,name=vresourcepool.kb.io,admissionReviewVersions=v1

var _ webhook.Validator = &ResourcePool{}

// ValidateCreate implements webhook.Validator so a webhook will be registered for the type
func (r *ResourcePool) ValidateCreate() error {
	resourcepoollog.Info("validate create", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}

	// TODO(user): fill in your validation logic upon object creation.
	return nil
}

// ValidateUpdate implements webhook.Validator so a webhook will be registered for the type
func (r *ResourcePool) ValidateUpdate(old runtime.Object) error {
	resourcepoollog.Info("validate update", "name", r.Name)
	if err := checkLicense(); err != nil {
		return err
	}

	if _, ok := old.(*ResourcePool); !ok {
		return nil
	}

	return nil
}

// ValidateDelete implements webhook.Validator so a webhook will be registered for the type
func (r *ResourcePool) ValidateDelete() error {
	resourcepoollog.Info("validate delete", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}
	// TODO(user): fill in your validation logic upon object deletion.
	return nil
}
