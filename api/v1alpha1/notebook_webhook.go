//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"fmt"
	"slices"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

// log is for logging in this package.
var (
	notebooklog             = logf.Log.WithName("notebook-resource")
	NotebookNameLabels      = "notebooks.system.hero.ai/name"
	NotebookNamespaceLabels = "notebooks.system.hero.ai/namespace"
)

func (r *Notebook) SetupWebhookWithManager(mgr ctrl.Manager) error {
	return ctrl.NewWebhookManagedBy(mgr).
		For(r).
		Complete()
}

// TODO(user): EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!

//+kubebuilder:webhook:path=/mutate-system-hero-ai-v1alpha1-notebook,mutating=true,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=notebooks,verbs=create;update,versions=v1alpha1,name=mnotebook.kb.io,admissionReviewVersions=v1

var _ webhook.Defaulter = &Notebook{}

// Default implements webhook.Defaulter so a webhook will be registered for the type
func (r *Notebook) Default() {
	notebooklog.Info("default", "name", r.Name)
	if len(r.Spec.ResourcePool) == 0 {
		r.Spec.ResourcePool = DefaultVirtualCluster
		notebooklog.Info("ResourcePool is nil, set default value now", "ResourcePool", r.Spec.ResourcePool)
	}

	if r.Labels == nil {
		r.Labels = map[string]string{}
	}

	if _, found := r.Labels[NotebookNameLabels]; !found {
		r.Labels[NotebookNameLabels] = r.Name
		r.Labels[NotebookNamespaceLabels] = r.Namespace
		r.Labels[NodeLabelKey] = r.Spec.ResourcePool
	}

	// TODO(user): fill in your defaulting logic.
}

// TODO(user): change verbs to "verbs=create;update;delete" if you want to enable deletion validation.
//+kubebuilder:webhook:path=/validate-system-hero-ai-v1alpha1-notebook,mutating=false,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=notebooks,verbs=create;update,versions=v1alpha1,name=vnotebook.kb.io,admissionReviewVersions=v1

var _ webhook.Validator = &Notebook{}

// ValidateCreate implements webhook.Validator so a webhook will be registered for the type
func (r *Notebook) ValidateCreate() error {
	notebooklog.Info("validate create", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}

	// TODO(user): fill in your validation logic upon object creation.
	err := r.checkCodeRepo()
	if err != nil {
		return err
	}

	if !r.checkStorageType() {
		return errors.New("no found storage type, datatype is err")
	}

	if err = r.checkAscendNPU(); err != nil {
		return err
	}

	if err = r.checkSharedMemory(); err != nil {
		return err
	}
	return r.checkCpuArch()
}

// ValidateUpdate implements webhook.Validator so a webhook will be registered for the type
func (r *Notebook) ValidateUpdate(old runtime.Object) error {
	notebooklog.Info("validate update", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}
	// TODO(user): fill in your validation logic upon object update.
	return nil
}

// ValidateDelete implements webhook.Validator so a webhook will be registered for the type
func (r *Notebook) ValidateDelete() error {
	notebooklog.Info("validate delete", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}
	// TODO(user): fill in your validation logic upon object deletion.
	return nil
}

func (r *Notebook) checkCodeRepo() error {
	if len(r.Spec.CodeSource.AccessToken) > 0 {
		if !strings.Contains(r.Spec.CodeSource.GitUrl, PrefixGitHttpsRepo) && !strings.Contains(r.Spec.CodeSource.GitUrl, PrefixGitHttpRepo) {
			return errors.New("private code repo must to be https or http")
		}
	}
	return nil
}

func (r *Notebook) checkStorageType() bool {
	for _, ds := range r.Spec.DataSources {
		if ds.DataType != Volume {
			return false
		}
	}
	return true
}

func (r *Notebook) checkSharedMemory() error {

	if len(r.Spec.ExtendResource.SharedMemory) > 0 {
		if _, err := strconv.Atoi(r.Spec.ExtendResource.SharedMemory); err != nil {
			return errors.New("SharedMemory must be a numeric string")
		}
	}

	return nil
}

func (r *Notebook) checkCpuArch() error {
	if len(r.Spec.ExtendResource.CPUArch) > 0 {
		if r.Spec.ExtendResource.CPUArch != "amd64" && r.Spec.ExtendResource.CPUArch != "arm64" {
			return errors.New("cpuArch must be amd64 or arm64, but get " + r.Spec.ExtendResource.CPUArch)
		}
	}

	return nil
}

func (r *Notebook) checkAscendNPU() error {
	// 单机训练时，华为昇腾 NPU 数量只能为 1,2,4,8
	allowedNPUNumber := []int64{1, 2, 4, 8}

	for resourceName := range r.Spec.Resource {
		if !strings.Contains(strings.ToLower(string(resourceName)), "ascend") {
			continue
		}
		q := r.Spec.Resource[resourceName]
		npuNum := q.Value()

		if !slices.Contains(allowedNPUNumber, npuNum) {
			return fmt.Errorf("the number of requests of Ascend NPU must be one of (1, 2, 4, 8)")
		}
	}
	return nil
}
