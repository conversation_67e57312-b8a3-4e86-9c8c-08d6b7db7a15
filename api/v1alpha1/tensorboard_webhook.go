//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

// log is for logging in this package.
var tensorboardlog = logf.Log.WithName("tensorboard-resource")

func (r *Tensorboard) SetupWebhookWithManager(mgr ctrl.Manager) error {
	return ctrl.NewWebhookManagedBy(mgr).
		For(r).
		Complete()
}

// TODO(user): EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!

//+kubebuilder:webhook:path=/mutate-system-hero-ai-v1alpha1-tensorboard,mutating=true,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=tensorboards,verbs=create;update,versions=v1alpha1,name=mtensorboard.kb.io,admissionReviewVersions=v1

var _ webhook.Defaulter = &Tensorboard{}

// Default implements webhook.Defaulter so a webhook will be registered for the type
func (r *Tensorboard) Default() {
	tensorboardlog.Info("default", "name", r.Name)

	// TODO(user): fill in your defaulting logic.
	r.Status.CreateTime = metav1.Now()
	r.Status.State.Phase = TBPending

	if r.Spec.MaxRunningTimeMinutes == 0 {
		r.Spec.MaxRunningTimeMinutes = 30
	}

	// if r.Spec.Resources == nil {
	// 	r.Spec.Resources = make(corev1.ResourceList)
	// 	// cpuQuantity := resource.NewQuantity(1, resource.DecimalSI)
	// 	cpuQuantity := resource.NewMilliQuantity(500, resource.DecimalSI)
	// 	r.Spec.Resources[corev1.ResourceCPU] = *cpuQuantity
	// 	memoryQuantity := resource.NewQuantity(500*1024*1024, resource.BinarySI)
	// 	r.Spec.Resources[corev1.ResourceMemory] = *memoryQuantity
	// }

}

// TODO(user): change verbs to "verbs=create;update;delete" if you want to enable deletion validation.
//+kubebuilder:webhook:path=/validate-system-hero-ai-v1alpha1-tensorboard,mutating=false,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=tensorboards,verbs=create;update,versions=v1alpha1,name=vtensorboard.kb.io,admissionReviewVersions=v1

var _ webhook.Validator = &Tensorboard{}

// ValidateCreate implements webhook.Validator so a webhook will be registered for the type
func (r *Tensorboard) ValidateCreate() error {
	tensorboardlog.Info("validate create", "name", r.Name)
	// TODO(user): fill in your validation logic upon object creation.

	if err := checkLicense(); err != nil {
		return err
	}

	return nil
}

// ValidateUpdate implements webhook.Validator so a webhook will be registered for the type
func (r *Tensorboard) ValidateUpdate(old runtime.Object) error {
	tensorboardlog.Info("validate update", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}

	// TODO(user): fill in your validation logic upon object update.
	return nil
}

// ValidateDelete implements webhook.Validator so a webhook will be registered for the type
func (r *Tensorboard) ValidateDelete() error {
	tensorboardlog.Info("validate delete", "name", r.Name)
	if err := checkLicense(); err != nil {
		return err
	}
	//r.Status.State.Phase = TBStopping

	// TODO(user): fill in your validation logic upon object deletion.
	return nil
}
