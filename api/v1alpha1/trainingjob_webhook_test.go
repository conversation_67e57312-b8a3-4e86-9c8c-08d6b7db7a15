package v1alpha1

import (
	"testing"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/stretchr/testify/assert"
)

func TestTrainingjobWebhookDefault(t *testing.T) {
	License = false
	trainingJob := &TrainingJob{
		Spec: TrainingJobSpec{
			ResourcePool:  "",
			MaxRetryCount: 0,
		},
	}

	trainingJob.Default()

	//assert.Equal(t, DefaultVirtualCluster, trainingJob.Spec.ResourcePool)
	assert.Equal(t, int32(DefaultMaxRetryCount), trainingJob.Spec.MaxRetryCount)
	assert.NotNil(t, trainingJob.Labels)
	assert.Equal(t, trainingJob.Name, trainingJob.Labels[VcjobNameLabels])
	assert.Equal(t, trainingJob.Namespace, trainingJob.Labels[VcjobNsLabels])
	assert.Equal(t, trainingJob.Spec.ResourcePool, trainingJob.Labels[NodeLabelKey])
}

func TestTrainingjobWebhookValidateCreate(t *testing.T) {
	License = false
	trainingJob := &TrainingJob{
		Spec: TrainingJobSpec{
			CodeSource: CodeSource{
				GitUrl:      "https://github.com/test/repo.git",
				AccessToken: "test-token",
			},
			Tasks: []Task{
				{ExtendResource: ExtendResource{CPUArch: "amd64"}},
			},
			DataSources: []DataSource{
				{DataType: Volume},
			},
		},
	}

	err := trainingJob.ValidateCreate()
	assert.NoError(t, err)

	// 测试无效情况：无效的 Git URL
	trainingJob.Spec.CodeSource.GitUrl = "ftp://github.com/test/repo.git"
	err = trainingJob.ValidateCreate()
	assert.Error(t, err)

	// 测试无效情况：任务数为零
	trainingJob.Spec.Tasks = nil
	err = trainingJob.ValidateCreate()
	assert.Error(t, err)

	// 测试无效情况：无效的 CPU 架构
	trainingJob.Spec.Tasks = []Task{
		{ExtendResource: ExtendResource{CPUArch: "invalid"}},
	}
	err = trainingJob.ValidateCreate()
	assert.Error(t, err)

	// 测试无效情况：存储类型无效
	trainingJob.Spec.DataSources = []DataSource{
		{DataType: "invalid"},
	}
	err = trainingJob.ValidateCreate()
	assert.Error(t, err)
}

func TestTrainingjobWebhookValidateUpdate(t *testing.T) {
	License = false
	oldTrainingJob := &TrainingJob{}
	newTrainingJob := &TrainingJob{}

	err := newTrainingJob.ValidateUpdate(oldTrainingJob)
	assert.NoError(t, err)
}

func TestTrainingjobWebhookValidateDelete(t *testing.T) {
	License = false
	trainingJob := &TrainingJob{}

	err := trainingJob.ValidateDelete()
	assert.NoError(t, err)
}

func TestCheckTask(t *testing.T) {
	License = false
	trainingJob := &TrainingJob{
		Spec: TrainingJobSpec{
			Tasks: []Task{
				{ExtendResource: ExtendResource{CPUArch: "amd64"}},
			},
		},
	}

	err := trainingJob.checkTask()
	assert.NoError(t, err)

	// 测试无效情况：任务数为零
	trainingJob.Spec.Tasks = nil
	err = trainingJob.checkTask()
	assert.Error(t, err)

	// 测试无效情况：无效的 CPU 架构
	trainingJob.Spec.Tasks = []Task{
		{ExtendResource: ExtendResource{CPUArch: "invalid"}},
	}
	err = trainingJob.checkTask()
	assert.Error(t, err)
}

func TestAddJobLabels(t *testing.T) {
	License = false
	trainingJob := &TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
		Spec: TrainingJobSpec{
			ResourcePool: DefaultVirtualCluster,
		},
	}

	trainingJob.addJobLabels()

	assert.Equal(t, trainingJob.Name, trainingJob.Labels[VcjobNameLabels])
	assert.Equal(t, trainingJob.Namespace, trainingJob.Labels[VcjobNsLabels])
	//assert.Equal(t, DefaultVirtualCluster, trainingJob.Labels[NodeLabelKey])
}

func TestTrainingjobWebhookCheckCodeRepo(t *testing.T) {
	License = false
	trainingJob := &TrainingJob{
		Spec: TrainingJobSpec{
			CodeSource: CodeSource{
				GitUrl:      "https://github.com/test/repo.git",
				AccessToken: "test-token",
			},
		},
	}

	err := trainingJob.checkCodeRepo()
	assert.NoError(t, err)

	// 测试无效情况：不合法的 Git URL
	trainingJob.Spec.CodeSource.GitUrl = "ftp://github.com/test/repo.git"
	err = trainingJob.checkCodeRepo()
	assert.Error(t, err)
}

func TestTrainingjobWebhookCheckStorageType(t *testing.T) {
	License = false
	trainingJob := &TrainingJob{
		Spec: TrainingJobSpec{
			DataSources: []DataSource{
				{DataType: Volume},
			},
		},
	}

	assert.True(t, trainingJob.checkStorageType())

	trainingJob.Spec.DataSources = []DataSource{
		{DataType: "invalid"},
	}

	assert.False(t, trainingJob.checkStorageType())
}

func TestTrainingjobWebhookCheckAscendNPU(t *testing.T) {
	generateTj := func(replicas, numNPU int32) *TrainingJob {
		return &TrainingJob{
			Spec: TrainingJobSpec{
				Tasks: []Task{
					{
						Replicas: replicas,
						Resource: map[v1.ResourceName]resource.Quantity{
							"huawei.com/Ascend910": *resource.NewQuantity(int64(numNPU), resource.DecimalSI),
						},
					},
				},
			},
		}
	}

	tests := []struct {
		trainingJob *TrainingJob
		wantErr     bool
	}{
		{trainingJob: generateTj(1, 1), wantErr: false},
		{trainingJob: generateTj(1, 2), wantErr: false},
		{trainingJob: generateTj(1, 3), wantErr: true},
		{trainingJob: generateTj(1, 4), wantErr: false},
		{trainingJob: generateTj(1, 5), wantErr: true},
		{trainingJob: generateTj(1, 6), wantErr: true},
		{trainingJob: generateTj(1, 7), wantErr: true},
		{trainingJob: generateTj(1, 8), wantErr: false},
		{trainingJob: generateTj(1, 9), wantErr: true},
		{trainingJob: generateTj(2, 1), wantErr: true},
		{trainingJob: generateTj(2, 4), wantErr: true},
		{trainingJob: generateTj(2, 8), wantErr: false},
	}

	for _, tt := range tests {
		assert.Equal(t, tt.wantErr, tt.trainingJob.checkAscendNPU() != nil)
	}
}
