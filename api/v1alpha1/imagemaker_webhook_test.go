package v1alpha1

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestValidateCreate(t *testing.T) {
	License = false
	im := &ImageMaker{
		Spec: ImageMakerSpec{
			Source: Source{
				Type:           PublicImage,
				SourceImageUrl: "http://example.com/image",
			},
		},
	}

	// 测试合法创建
	assert.NoError(t, im.ValidateCreate())

	// 测试不合法创建：缺少 SourceImageUrl
	im.Spec.Source.SourceImageUrl = ""
	err := im.ValidateCreate()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "has wrong imageMaker.SourceImageUrl")
}

func TestValidateUpdate(t *testing.T) {
	License = false
	im := &ImageMaker{
		Spec: ImageMakerSpec{
			Source: Source{
				Type:           PrivateImage,
				SourceImageUrl: "http://example.com/image",
				ImageSecret: ImageSecret{
					Username: "user",
					Password: "pass",
				},
			},
		},
	}

	old := &ImageMaker{
		Spec: ImageMakerSpec{
			Source: Source{
				Type: PrivateImage,
			},
		},
	}

	// 测试合法更新
	assert.NoError(t, im.ValidateUpdate(old))

	// 测试不合法更新：缺少 ImageSecret
	im.Spec.Source.ImageSecret = ImageSecret{}
	err := im.ValidateUpdate(old)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "has 'nil' imageMaker.ImageSecret")
}

func TestValidateDelete(t *testing.T) {
	License = false
	im := &ImageMaker{
		Spec: ImageMakerSpec{
			Source: Source{
				Type:           PublicImage,
				SourceImageUrl: "http://example.com/image",
			},
		},
	}

	// 测试合法删除
	assert.NoError(t, im.ValidateDelete())
}

func TestValidateImageMaker(t *testing.T) {
	License = false
	im := &ImageMaker{
		// 初始化必要的字段
		Spec: ImageMakerSpec{
			Source: Source{
				Type: Dockerfile, // 测试用例1：合法的类型
			},
		},
	}
	assert.NoError(t, im.validateImageMaker())

	// 测试用例2：源类型不合法
	im.Spec.Source.Type = "InvalidType"
	err := im.validateImageMaker()
	assert.Error(t, err)
	assert.Equal(t, "not regist this kind: 'InvalidType'", err.Error())
}

func TestValidateImageMakerSpec(t *testing.T) {
	License = false
	im := &ImageMaker{
		Spec: ImageMakerSpec{
			Source: Source{
				Type:           PublicImage,
				SourceImageUrl: "http://example.com/image",
			},
		},
	}

	assert.NoError(t, im.validateImageMakerSpec())

	// 测试源类型 PublicImage 时的缺失 SourceImageUrl
	im.Spec.Source.SourceImageUrl = ""
	err := im.validateImageMakerSpec()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "has wrong imageMaker.SourceImageUrl")
}

func TestCheckSpec(t *testing.T) {
	License = false
	im := &ImageMaker{
		Spec: ImageMakerSpec{
			Source: Source{
				Type: UrlDockerfile,
			},
		},
	}

	// 测试用例：合法的 DockerfileUrl
	im.Spec.Source.DockerfileUrl = "http://example.com/Dockerfile"
	assert.NoError(t, im.checkSpec())

	// 测试用例：缺失 DockerfileUrl
	im.Spec.Source.DockerfileUrl = ""
	err := im.checkSpec()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "has wrong imageMaker.DockerfileUrl")
}

func TestCheckVolume(t *testing.T) {
	License = false
	im := &ImageMaker{
		Spec: ImageMakerSpec{
			Source: Source{
				Type: Dockerfile,
				DataSource: DataSource{
					VolumeName:    "test-volume",
					VolumeSubPath: "test-path",
				},
			},
		},
	}

	// 测试合法情况
	assert.NoError(t, im.checkvolume())

	// 测试缺失 VolumeName
	im.Spec.Source.Type = ""
	im.Spec.Source.DataSource.VolumeName = ""
	err := im.checkvolume()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must has volumeName, volumeSubPath")
}

func TestCheckImageArch(t *testing.T) {
	License = false
	im := &ImageMaker{
		Spec: ImageMakerSpec{
			Source: Source{
				CpuArch: "",
			},
		},
	}

	// 检查初始值
	assert.NoError(t, im.checkImageArch())
	assert.Equal(t, "linux/amd64", im.Spec.Source.CpuArch)

	// 设定已存在的架构
	im.Spec.Source.CpuArch = "linux/arm64"
	assert.NoError(t, im.checkImageArch())
	assert.Equal(t, "linux/arm64", im.Spec.Source.CpuArch)
}
