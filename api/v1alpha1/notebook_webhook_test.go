package v1alpha1

import (
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

func TestDefault(t *testing.T) {
	License = false
	notebook := &Notebook{}
	notebook.Default()

	assert.Equal(t, DefaultVirtualCluster, notebook.Spec.ResourcePool)
	assert.NotNil(t, notebook.Labels)
	assert.Equal(t, notebook.Name, notebook.Labels[NotebookNameLabels])
	assert.Equal(t, notebook.Namespace, notebook.Labels[NotebookNamespaceLabels])
}

func TestNotebookWebhookValidateCreate(t *testing.T) {
	License = false
	notebook := &Notebook{
		Spec: NotebookSpec{
			CodeSource: CodeSource{
				GitUrl:      "https://example.com/repo",
				AccessToken: "token",
			},
			DataSources: []DataSource{
				{DataType: Volume},
			},
			ExtendResource: ExtendResource{
				CPUArch: "amd64",
			},
		},
	}

	assert.NoError(t, notebook.ValidateCreate())

	// 测试不合法情况：缺失 GitUrl
	notebook.Spec.CodeSource.GitUrl = ""
	err := notebook.ValidateCreate()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "private code repo must to be https or http")
}

func TestNotebookWebhookValidateUpdate(t *testing.T) {
	License = false
	notebook := &Notebook{
		Spec: NotebookSpec{},
	}
	oldNotebook := &Notebook{}

	assert.NoError(t, notebook.ValidateUpdate(oldNotebook))
}

func TestNotebookWebhookValidateDelete(t *testing.T) {
	License = false
	notebook := &Notebook{
		Spec: NotebookSpec{},
	}

	assert.NoError(t, notebook.ValidateDelete())
}

func TestCheckCodeRepo(t *testing.T) {
	License = false
	notebook := &Notebook{
		Spec: NotebookSpec{
			CodeSource: CodeSource{
				GitUrl:      "http://example.com/repo",
				AccessToken: "token",
			},
		},
	}

	assert.NoError(t, notebook.checkCodeRepo())

	// 测试不合法情况：使用不支持的 GitUrl
	notebook.Spec.CodeSource.GitUrl = "ftp://example.com/repo"
	err := notebook.checkCodeRepo()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "private code repo must to be https or http")
}

func TestCheckStorageType(t *testing.T) {
	License = false
	notebook := &Notebook{
		Spec: NotebookSpec{
			DataSources: []DataSource{
				{DataType: Volume},
			},
		},
	}

	assert.True(t, notebook.checkStorageType())

	// 测试不合法情况
	notebook.Spec.DataSources = append(notebook.Spec.DataSources, DataSource{DataType: "invalid"})
	assert.False(t, notebook.checkStorageType())
}

func TestCheckCpuArch(t *testing.T) {
	License = false
	notebook := &Notebook{
		Spec: NotebookSpec{
			ExtendResource: ExtendResource{
				CPUArch: "amd64",
			},
		},
	}

	assert.NoError(t, notebook.checkCpuArch())

	// 测试不合法情况
	notebook.Spec.ExtendResource.CPUArch = "invalid-arch"
	err := notebook.checkCpuArch()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cpuArch must be amd64 or arm64")
}

func TestNotebookWebhookCheckAscendNPU(t *testing.T) {
	generateNb := func(numNPU int32) *Notebook {
		return &Notebook{
			Spec: NotebookSpec{
				Resource: map[v1.ResourceName]resource.Quantity{
					"huawei.com/Ascend910": *resource.NewQuantity(int64(numNPU), resource.DecimalSI),
				},
			},
		}
	}

	tests := []struct {
		notebook *Notebook
		wantErr  bool
	}{
		{notebook: generateNb(1), wantErr: false},
		{notebook: generateNb(2), wantErr: false},
		{notebook: generateNb(3), wantErr: true},
		{notebook: generateNb(4), wantErr: false},
		{notebook: generateNb(5), wantErr: true},
		{notebook: generateNb(6), wantErr: true},
		{notebook: generateNb(7), wantErr: true},
		{notebook: generateNb(8), wantErr: false},
		{notebook: generateNb(9), wantErr: true},
	}

	for _, tt := range tests {
		assert.Equal(t, tt.wantErr, tt.notebook.checkAscendNPU() != nil)
	}
}
