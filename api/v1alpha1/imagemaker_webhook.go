//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"fmt"
	"strings"

	"k8s.io/apimachinery/pkg/runtime"

	// "k8s.io/utils/strings"
	ctrl "sigs.k8s.io/controller-runtime"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

// log is for logging in this package.
var (
	imagemakerlog        = logf.Log.WithName("imagemaker-resource")
	ImagemakerNameLabels = "imagemaker.system.hero.ai/name"
)

func (r *ImageMaker) SetupWebhookWithManager(mgr ctrl.Manager) error {
	return ctrl.NewWebhookManagedBy(mgr).
		For(r).
		Complete()
}

// TODO(user): EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!

//+kubebuilder:webhook:path=/mutate-system-hero-ai-v1alpha1-imagemaker,mutating=true,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=imagemakers,verbs=create;update,versions=v1alpha1,name=mimagemaker.kb.io,admissionReviewVersions=v1

var _ webhook.Defaulter = &ImageMaker{}

// Default implements webhook.Defaulter so a webhook will be registered for the type
func (r *ImageMaker) Default() {
	imagemakerlog.Info("default", "name", r.Name)

	// TODO(user): fill in your defaulting logic.

}

// TODO(user): change verbs to "verbs=create;update;delete" if you want to enable deletion validation.
//+kubebuilder:webhook:path=/validate-system-hero-ai-v1alpha1-imagemaker,mutating=false,failurePolicy=fail,sideEffects=None,groups=system.hero.ai,resources=imagemakers,verbs=create;update,versions=v1alpha1,name=vimagemaker.kb.io,admissionReviewVersions=v1

var _ webhook.Validator = &ImageMaker{}

// ValidateCreate implements webhook.Validator so a webhook will be registered for the type
func (r *ImageMaker) ValidateCreate() error {
	imagemakerlog.Info("validate create", "name", r.Name)
	if err := checkLicense(); err != nil {
		return err
	}

	// TODO(user): fill in your validation logic upon object creation.
	return r.validateImageMaker()
}

// ValidateUpdate implements webhook.Validator so a webhook will be registered for the type
func (r *ImageMaker) ValidateUpdate(old runtime.Object) error {
	imagemakerlog.Info("validate update", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}
	// TODO(user): fill in your validation logic upon object update.
	return r.validateImageMaker()
}

// ValidateDelete implements webhook.Validator so a webhook will be registered for the type admission.Warnings
func (r *ImageMaker) ValidateDelete() error {
	imagemakerlog.Info("validate delete", "name", r.Name)

	if err := checkLicense(); err != nil {
		return err
	}
	// TODO(user): fill in your validation logic upon object deletion.
	return nil
}

/*
We validate the name and the spec of the CronJob.
*/

func (r *ImageMaker) validateImageMaker() error {

	if err := r.validateImageMakerSpec(); err != nil {
		return err
	}
	return nil
}

func (r *ImageMaker) validateImageMakerSpec() error {

	return r.checkSpec()

}

func (r *ImageMaker) checkSpec() error {

	switch r.Spec.Source.Type {
	case Dockerfile, TarPkg:
		return r.checkvolume()
	case PublicImage:
		if r.Spec.Source.SourceImageUrl == "" {
			return fmt.Errorf("%s has wrong imageMaker.SourceImageUrl", r.Spec.Source.SourceImageUrl)
		}
		return nil
	case UrlDockerfile:
		if r.Spec.Source.DockerfileUrl == "" {
			return fmt.Errorf("%s has wrong imageMaker.DockerfileUrl", r.Spec.Source.DockerfileUrl)
		}
		return nil
	case PrivateImage:
		if r.Spec.Source.SourceImageUrl == "" {
			return fmt.Errorf("%s has wrong imageMaker.SourceImageUrl", r.Spec.Source.SourceImageUrl)
		}
		if r.Spec.Source.ImageSecret.Password == "" || r.Spec.Source.ImageSecret.Username == "" {
			return fmt.Errorf("%s has 'nil' imageMaker.ImageSecret", r.Spec.Source.SourceImageUrl)
		}
		return nil
	case Container:
		if r.Spec.Source.Containers.Kind == "" || r.Spec.Source.Containers.Name == "" || r.Spec.Source.Containers.Namespace == "" {
			return fmt.Errorf("each propery ('Kind' 'Name' 'Namespace') in  imagemaker.Spec.Source.Containers  must have value ")
		}
		return nil
	default:
		return fmt.Errorf("not regist this kind: '%s'", string(r.Spec.Source.Type))
	}
}

func (r *ImageMaker) checkvolume() error {

	if r.Spec.Source.Type == Dockerfile {
		return r.checkImageArch()
	}
	if r.Spec.Source.DataSource.VolumeName == "" || r.Spec.Source.DataSource.VolumeSubPath == "" {
		return fmt.Errorf("%s has wrong imageMaker.DataSource: must has volumeName, volumeSubPath", r.Spec.Source.DataSource.VolumeName)
	}
	if r.Spec.Source.Type == TarPkg && !strings.HasSuffix(r.Spec.Source.DataSource.VolumeSubPath, ".tar") {
		return fmt.Errorf("%s has wrong imageMaker.DataSource: tar must end with '.tar'", r.Spec.Source.DataSource.VolumeSubPath)
	}
	if len(r.Spec.Source.DataSource.VolumeSubPath) > 256 {
		return fmt.Errorf("imageMaker.DataSource.volumeSubPath length too long:(>256), '%s'", r.Spec.Source.DataSource.VolumeSubPath)
	}
	if strings.Count(r.Spec.Source.DataSource.VolumeSubPath, "/") > 16 {
		return fmt.Errorf("imageMaker.DataSource.volumeSubPath has too many directory('/'):(>16), '%s'", r.Spec.Source.DataSource.VolumeSubPath)
	}
	return nil

}
func (r *ImageMaker) checkImageArch() error {

	if r.Spec.Source.CpuArch == "" {
		r.Spec.Source.CpuArch = "linux/amd64"
	}

	return nil

}
