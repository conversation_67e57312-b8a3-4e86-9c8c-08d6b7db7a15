//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// +kubebuilder:validation:Enum=Syncing;Failed;Succeed
type SyncActionPhase string

const (
	SyncFailed  SyncActionPhase = "Failed"
	SyncSuccess SyncActionPhase = "Succeed"
	Syncing     SyncActionPhase = "Syncing"
)

// SyncActionSpec defines the desired state of SyncAction
type SyncActionSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Action defines the action that will be took to the target object.
	Name                ActionName              `json:"name,omitempty"`
	ExportFullLogAction ExportFullLogParameters `json:"exportFullLogParameters,omitempty" protobuf:"bytes,2,opt,name=exportFullLogParameters"`
	// TargetObject defines the target object of this syncAction.
	TargetObject *TargetObject     `json:"target,omitempty" protobuf:"bytes,3,opt,name=target"`
	ResourcePool ResourcePoolNodes `json:"resourcePool,omitempty"`
}

type TargetObject struct {
	APIVersion string `json:"apiVersion" protobuf:"bytes,5,opt,name=apiVersion"`
	// Kind of the referent.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
	Kind string `json:"kind" protobuf:"bytes,1,opt,name=kind"`
	// Name of the referent.
	// More info: http://kubernetes.io/docs/user-guide/identifiers#names
	Name string `json:"name" protobuf:"bytes,3,opt,name=name"`
}

// +kubebuilder:validation:Enum=Bind;UnBind
type BindType string

const (
	Bind   BindType = "Bind"
	UnBind BindType = "UnBind"
)

type ResourcePoolNodes struct {
	Nodes []string `json:"nodes,omitempty"`
	Type  BindType `json:"type,omitempty"`
}

type SyncActionState struct {
	Phase  SyncActionPhase `json:"phase,omitempty"`
	Reason string          `json:"reason,omitempty"`
}

// SyncActionStatus defines the observed state of SyncAction
type SyncActionStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	State SyncActionState `json:"state,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:printcolumn:name="STATUS",type="string",JSONPath=".status.state.phase"
//+kubebuilder:printcolumn:name="AGE",type="date",JSONPath=".metadata.creationTimestamp"
//+kubebuilder:printcolumn:name="TYPES",type="string",JSONPath=".spec.resourcePool.type"
//+kubebuilder:printcolumn:name="NODES",type="string",JSONPath=".spec.resourcePool.nodes"
//+kubebuilder:resource:shortName=sync;syncaction

// SyncAction is the Schema for the syncactions API
type SyncAction struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   SyncActionSpec   `json:"spec,omitempty"`
	Status SyncActionStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// SyncActionList contains a list of SyncAction
type SyncActionList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []SyncAction `json:"items"`
}

func init() {
	SchemeBuilder.Register(&SyncAction{}, &SyncActionList{})
}

type ExportFullLogParameters struct {
	BucketName     string `json:"bucket_name,omitempty"`
	StorageType    string `json:"storage_type,omitempty"`
	BucketFilePath string `json:"bucket_file_path,omitempty"`
	Containers     string `json:"container_name,omitempty"`
	Namespaces     string `json:"namespaces,omitempty"`
	Pods           string `json:"pods,omitempty"`
	StartTime      string `json:"start_time,omitempty"`
	EndTime        string `json:"end_time,omitempty"`
	Sort           string `json:"sort,omitempty"`
	ShowTime       string `json:"show_time,omitempty"`
	From           string `json:"from,omitempty"`
	Size           string `json:"size,omitempty"`
	KeywordFilter  string `json:"key_word_filter,omitempty"`
}

type ActionName string

const (
	SyncRpAction    ActionName = "SyncAction"
	LogExportAction ActionName = "FullLogExportAction"
)
