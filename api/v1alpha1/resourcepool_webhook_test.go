package v1alpha1

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestResourcepoolWebhookDefault(t *testing.T) {
	License = false
	resourcePool := &ResourcePool{}
	resourcePool.Default()
}

func TestResourcepoolWebhookValidateCreate(t *testing.T) {
	License = false
	resourcePool := &ResourcePool{}

	err := resourcePool.ValidateCreate()
	assert.NoError(t, err)

	// 测试不合法情况
	License = true
	err = resourcePool.ValidateCreate()
	assert.Error(t, err)
}

func TestResourcepoolWebhookValidateUpdate(t *testing.T) {
	License = false
	oldResourcePool := &ResourcePool{}
	newResourcePool := &ResourcePool{}

	err := newResourcePool.ValidateUpdate(oldResourcePool)
	assert.NoError(t, err)

	// 测试不合法情况
	License = true
	err = newResourcePool.ValidateUpdate(oldResourcePool)
	assert.Error(t, err)
}

func TestResourcepoolWebhookValidateDelete(t *testing.T) {
	License = false
	resourcePool := &ResourcePool{}

	err := resourcePool.ValidateDelete()
	assert.NoError(t, err)

	// 测试不合法情况
	License = true
	err = resourcePool.ValidateUpdate(resourcePool)
	assert.Error(t, err)
}
