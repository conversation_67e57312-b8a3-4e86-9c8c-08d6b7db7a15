//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// +kubebuilder:validation:Enum=Pending;Queuing;Running;Stopping;Stopped;Failed;Completed;Restarting;NodeHealthChecking
type TrainingJobPhase string

const (
	Pending            TrainingJobPhase = "Pending"
	Queuing            TrainingJobPhase = "Queuing"
	Running            TrainingJobPhase = "Running"
	Stopping           TrainingJobPhase = "Stopping"
	Stopped            TrainingJobPhase = "Stopped"
	Failed             TrainingJobPhase = "Failed"
	Completed          TrainingJobPhase = "Completed"
	Restarting         TrainingJobPhase = "Restarting"
	NodeHealthChecking TrainingJobPhase = "NodeHealthChecking"
)

// TrainingJobStatus defines the observed state of TrainingJob
type TrainingJobStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	State          TrainingJobState       `json:"state,omitempty"`
	RetryCount     int32                  `json:"retryCount,omitempty"`
	CreateTime     string                 `json:"createTime,omitempty"`
	StartTime      string                 `json:"startTime,omitempty"`
	StoppedTime    string                 `json:"stoppedTime,omitempty"`
	CompletionTime string                 `json:"completionTime,omitempty"`
	PodDurations   TrainingJobDurations   `json:"serverDurations,omitempty"`
	Conditions     []TrainingJobCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"status"`

	// 当前 job 的版本号，从 0 开始递增。
	// +optional
	Version int32 `json:"version"`

	// TrainingJob 每个 version 对应的健康检查结果和重启状态
	// +optional
	FaultTolerance map[string]FaultToleranceStatus `json:"faultTolerance,omitempty"`
}

// TrainingJobCondition contains details for the current condition of this job.
type TrainingJobCondition struct {
	// Status is the new phase of job after performing the state's action.
	Status TrainingJobPhase `json:"status"`
	// Last time the condition transitioned from one phase to another.
	// +optional
	LastTransitionTime *metav1.Time `json:"lastTransitionTime,omitempty"`
}

type TrainingJobDurations map[string]*TrainingJobPodDurations

type TrainingJobPodDurations struct {
	// Name is unique name in the node tree used to generate the node ID
	// +optional
	Name string `json:"name,omitempty"`
	// TaskName is unique name in the node tree used to generate the node ID
	// +optional
	TaskName string `json:"taskName,omitempty"`
	// A human readable message indicating details about why the node is in this condition.
	// +optional
	Message string `json:"message,omitempty"`
	// The reason why pod failed
	Reason string `json:"reason,omitempty"`
	// Time at which this node started
	// +optional
	LaunchedTime string `json:"launchedTime"`
	// Time at which this node completed
	// +optional
	CompletedTime string `json:"completedTime"`
	// Phase a simple, high-level summary of where the node is in its lifecycle.
	// Can be used as a state machine.
	// +optional
	Phase v1.PodPhase `json:"phase,omitempty"`
	// Time at which this node webTerminalUrl
	// +optional
	WebTerminalUrl string `json:"webTerminalUrl,omitempty"`
	// Time at which this node nodeName
	// +optional
	NodeName string `json:"nodeName,omitempty"`
	// Job version
	// +optional
	Version int32 `json:"version"`
}

type TrainingJobState struct {
	Phase              TrainingJobPhase `json:"phase,omitempty"`
	Reason             string           `json:"reason,omitempty"`
	Message            string           `json:"message,omitempty"`
	LastTransitionTime string           `json:"lastTransitionTime,omitempty"`
}

type FaultToleranceStatus struct {
	// vcJob 创建时间
	// +optional
	CreateTime *metav1.Time `json:"createTime,omitempty"`
	// vcJob Running 时间
	// +optional
	StartTime *metav1.Time `json:"startTime,omitempty"`
	// vcJob 结束时间
	// +optional
	CompletionTime *metav1.Time `json:"completionTime,omitempty"`
	// 当前版本 job 状态
	Phase TrainingJobPhase `json:"phase,omitempty"`
	// TrainingJobSpec 中定义的每种健康检查的结果
	// +optional
	HealthChecks []HealthCheckResult `json:"healthChecks,omitempty"`
	// 触发重启的原因
	// +optional
	RestartReasons []string `json:"restartReasons,omitempty"`
}

type HealthCheckResult struct {
	Name HealthCheckType `json:"name,omitempty"`

	// Unique, one-word, CamelCase reason for the health check result.
	// +optional
	Result HealthCheckResultName `json:"result,omitempty"`

	// Human-readable message indicating details about result.
	// +optional
	Message string `json:"message,omitempty"`

	// Time when the health check result was generated.
	// +optional
	Time metav1.Time `json:"time,omitempty"`
}

type HealthCheckResultName string

const (
	// 健康检查不通过的情况

	// NodeUnhealthyBeforeStart 表示 NodeHealthBeforeStartCheck 检查结果为存在节点异常
	NodeUnhealthyBeforeStart HealthCheckResultName = "NodeUnhealthyBeforeStart"
	// JobHang 表示 JobHangCheck 检查结果为任务 hang 住
	JobHang HealthCheckResultName = "JobHang"
	// FailedJobRestartable 表示 FailedJobRestartableCheck 检查结果为可重启
	FailedJobRestartable HealthCheckResultName = "FailedJobRestartable"
	// UnexpectedInternalError 可能是 pod 被驱逐导致任务停止了或者 pod 没有被创建即停止了等等
	UnexpectedInternalError HealthCheckResultName = "UnexpectedInternalError"

	// 健康检查通过的情况

	// NodeHealthyBeforeStart 表示 NodeHealthBeforeStartCheck 检查结果为所有节点都正常
	NodeHealthyBeforeStart HealthCheckResultName = "NodeHealthyBeforeStart"
	// FailedJobUnrestartable 表示 FailedJobRestartableCheck 检查结果为不可重启
	FailedJobUnrestartable HealthCheckResultName = "FailedJobUnrestartable"
)

// AllowedRestartPolices 所有支持重启的健康检查结果列表
var AllowedRestartPolices = []HealthCheckResultName{
	NodeUnhealthyBeforeStart,
	JobHang,
	FailedJobRestartable,
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:printcolumn:name="STATUS",type="string",JSONPath=".status.state.phase"
//+kubebuilder:printcolumn:name="AGE",type="date",JSONPath=".metadata.creationTimestamp"
//+kubebuilder:resource:shortName=tj;train

// TrainingJob is the Schema for the trainingjobs API
type TrainingJob struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   TrainingJobSpec   `json:"spec,omitempty"`
	Status TrainingJobStatus `json:"status,omitempty"`
}

// TrainingJobSpec defines the desired state of TrainingJob
type TrainingJobSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// ImageUrl is the url of the training job image

	// +kubebuilder:validation:Required
	ImageUrl string `json:"imageUrl,omitempty"`

	ImageSecret ImageSecret `json:"imageSecret,omitempty"`

	ResourcePool string `json:"resourcePool,omitempty"`

	Plugins []string `json:"plugins,omitempty"`

	Tasks []Task `json:"tasks,omitempty"`

	Envs []Env `json:"envs,omitempty"`

	CodeSource CodeSource `json:"codeSource,omitempty"`

	DataSources []DataSource `json:"dataSources,omitempty"`

	// +kubebuilder:validation:Required
	MaxRetryCount int32 `json:"maxRetryCount,omitempty"`

	// FaultTolerance 定义任务诊断和重启配置
	FaultTolerance *FaultTolerance `json:"faultTolerance,omitempty"`
}

type Env struct {
	Name  string `json:"name,omitempty"`
	Value string `json:"value,omitempty"`
}

type CodeSource struct {
	GitUrl      string `json:"gitUrl,omitempty"`
	Branch      string `json:"branch,omitempty"`
	AccessName  string `json:"accessName,omitempty"`
	AccessToken string `json:"accessToken,omitempty"`
	MountPath   string `json:"mountPath,omitempty"`
}

type ImageSecret struct {
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`
}

type DataType string

const (
	Volume      DataType = "Volume"
	Git         DataType = "git"
	ShareMemory DataType = "shareMemory"
)

type DataSource struct {
	Name          string   `json:"name,omitempty"`
	VolumeName    string   `json:"volumeName"`
	VolumeSubPath string   `json:"volumeSubPath,omitempty"`
	MountPath     string   `json:"mountPath,omitempty"`
	SizeLimit     string   `json:"-"`
	ReadOnly      bool     `json:"readOnly,omitempty"`
	DataType      DataType `json:"dataType,omitempty"`
}

type Task struct {
	Name           string          `json:"name,omitempty"`
	TaskType       string          `json:"taskType,omitempty"`
	Command        string          `json:"command,omitempty"`
	ResourcePool   string          `json:"resourcePool,omitempty"`
	MinAvaluable   int32           `json:"minAvaluable,omitempty"`
	Replicas       int32           `json:"replicas,omitempty"`
	Resource       v1.ResourceList `json:"resource,omitempty"`
	ExtendResource ExtendResource  `json:"extendResource,omitempty"`
}

type ExtendResource struct {
	CPUArch      string `json:"cpuArch,omitempty"`
	SharedMemory string `json:"sharedMemory,omitempty"`
}

type FaultTolerance struct {
	// 需要进行的健康检查项
	HealthChecks []HealthCheck `json:"healthChecks,omitempty"`
	// 根据健康检查结果定义重启策略
	RestartPolicy *RestartPolicy `json:"restartPolicy,omitempty"`
}

type HealthCheck struct {
	Name HealthCheckType       `json:"name,omitempty"`
	Args *runtime.RawExtension `json:"args,omitempty"`
}

type HealthCheckType string

const (
	// NodeHealthBeforeStartCheck 用户容器启动前对调度到的节点进行环境（CPU/内存/GPU/存储）健康检测
	NodeHealthBeforeStartCheck HealthCheckType = "NodeHealthBeforeStartCheck"
	// JobHangCheck 定期检查运行中的任务是否 hang 住
	JobHangCheck HealthCheckType = "JobHangCheck"
	// FailedJobRestartableCheck 判断已经失败的任务是否可以重试
	FailedJobRestartableCheck HealthCheckType = "FailedJobRestartableCheck"
)

type JobHangCheckArgs struct {
	metav1.TypeMeta `json:",inline"`

	// 如果运行中的任务所有 Pod 超过 MaxLogInterval 秒没有日志输出则判定为 hang
	MaxLogInterval int32 `json:"maxLogInterval,omitempty"`
}

type FailedJobRestartablePolicy string

const (
	// OnFailureRestartable 表示只要任务失败都可以重启
	OnFailureRestartable FailedJobRestartablePolicy = "OnFailure"
	// ExitCodeAndErrorMsgRestartable 表示分析失败任务最后的日志满足用户给定的规则即可重启
	ExitCodeAndErrorMsgRestartable FailedJobRestartablePolicy = "ExitCodeAndErrorMsg"
	// AdvancedRestartable 表示由系统预置的高级策略来决定失败的任务是否可以重启
	AdvancedRestartable FailedJobRestartablePolicy = "Advanced"
)

type FailedJobRestartableCheckArgs struct {
	metav1.TypeMeta `json:",inline"`

	Policy   FailedJobRestartablePolicy `json:"policy"`
	LogRules [][]string                 `json:"logRules,omitempty"`
}

type RestartPolicy struct {
	// 最大重启次数
	MaxRetry int32 `json:"maxRetry,omitempty"`
	// 任务可重启时，任务停止后，等待 delay 秒再重新创建启动任务
	Delay int32 `json:"delay,omitempty"`
	// 当健康检查结果在给定的 Policies 中时才可重启任务
	Policies []HealthCheckResultName `json:"policies,omitempty"`
}

//+kubebuilder:object:root=true

// TrainingJobList contains a list of TrainingJob
type TrainingJobList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []TrainingJob `json:"items"`
}

func init() {
	SchemeBuilder.Register(&TrainingJob{}, &TrainingJobList{})
}

const (
	EventStarting                = "Starting"
	EventCreateVolcanoJobSuccess = "CreateVolcanoJobSuccess"
	EventCreateVolcanoFailed     = "CreateVolcanoJobFailed"
	EventImagePullSuccess        = "PullImageSuccess"
	EventImagePullFailed         = "PullImageFailed"
	EventRunningSuccess          = "Success"
	EventRunFailed               = "Failed"
	EventCompeleted              = "Completed"
	EventScheduledSuccess        = "SchedulerSuccess"
	EventScheduledFailed         = "SchedulerFailed"
	EventWaitingScheduler        = "WaitingScheduler"
	EventStopping                = "Stopping"
	EventStopped                 = "Stopped"
	EventEnvCheck                = "Checked"

	EventMaxRunTimeSetting = "SetMaxRunTime"

	EventRestarting                    = "Restarting"
	EventUnrestartable                 = "Unrestartable"
	EventNodeHealthCheckingBeforeStart = "NodeHealthCheckingBeforeStart"
	EventNodeHealthyBeforeStart        = "NodeHealthyBeforeStart"
	EventNodeUnhealthyBeforeStart      = "NodeUnhealthyBeforeStart"
	EventJobHangHealthCheckStarted     = "JobHangHealthCheckStarted"
	EventJobHangHealthCheckStopped     = "JobHangHealthCheckStopped"
	EventJobHangDetected               = "JobHangDetected"
	EventFailedJobRestartableChecking  = "FailedJobRestartableChecking"
	EventFailedJobRestartable          = "FailedJobRestartable"
	EventFailedJobUnrestartable        = "FailedJobUnrestartable"
)

const (
	EventEnvCheckMessage                = "plugin [%s] env does not exist"
	EventStartingMessage                = "starting handle"
	EventCreateVolcanoJobSuccessMessage = "volcanoJob create successfully"
	EventCreateVolcanoFailedMessage     = "volcanoJob create failed: %s"
	EventImagePullSuccessMessage        = "image pull successfully"
	EventRunningSuccessMessage          = "server is running"
	EventRunFailedMessage               = "server failed: %s"
	EventCompeletedMessage              = "server is completed"
	EventScheduledSuccessMessage        = "server is scheduled successfully"
	EventScheduledFailedMessage         = "server is scheduled failed: %s"
	EventStoppingMessage                = "server is stopping"
	EventStoppedMessage                 = "server is stopped successfully"
	EventWaitingSchedulerMessage        = "task waiting"
	EventMaxRunTimeSettingMessage       = "maxRunTime is set"
	EventDistributedErrMessage          = "task %s pod %s is err, message: %s"

	EventRestartingMessage                    = "The job is restarting at version %d due to %+v"
	EventUnrestartableMessage                 = "The job cannot be restarted at version %d, it either does not match any restart policies or has reached the retry limit"
	EventNodeHealthCheckingBeforeStartMessage = "Checking node health before starting the job at version %d"
	EventNodeHealthyBeforeStartMessage        = "All nodes are healthy before starting the job at version %d"
	EventNodeUnhealthyBeforeStartMessage      = "There are unhealthy nodes before starting the job at version %d"
	EventJobHangHealthCheckStartedMessage     = "Job hang health check started at version %s"
	EventJobHangHealthCheckStoppedMessage     = "Job hang health check stopped at version %s"
	EventJobHangDetectedMessage               = "The job has been hanging at version %d for over %d seconds"
	EventFailedJobRestartableCheckingMessage  = "Verifying if the failed job at version %d is restartable"
	EventFailedJobRestartableMessage          = "The failed job is restartable at version %d, as verified by the %s policy"
	EventFailedJobUnrestartableMessage        = "The failed job is unrestartable at version %d, as verified by the %s policy"
)

const (
	DefaultUserContainerName         = "main"
	InitNodeHealthCheckContainerName = "init-node-health-check"
)
