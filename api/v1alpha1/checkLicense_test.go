package v1alpha1

import "testing"

func TestCheckLicense(t *testing.T) {
	tests := []struct {
		name       string
		license    bool
		wantErr    bool
		wantErrMsg string
	}{
		{
			name:    "License not set",
			license: false,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			License = tt.license // 设置 License 状态
			err := checkLicense()

			if (err != nil) != tt.wantErr {
				t.Errorf("checkLicense() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr && err != nil && err.Error() != tt.wantErrMsg {
				t.Errorf("checkLicense() error message = %v, wantErrMsg %v", err.Error(), tt.wantErrMsg)
			}
		})
	}
}
