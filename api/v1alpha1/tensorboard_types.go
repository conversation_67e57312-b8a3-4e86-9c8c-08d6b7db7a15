//nolint:nestif,cyclop
/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// TensorboardSpec defines the desired state of Tensorboard
type TensorboardSpec struct {
	ImageUrl              string              `json:"imageUrl,omitempty"`
	DataSources           []DataItem          `json:"dataSources,omitempty"`
	Resources             corev1.ResourceList `json:"resourceConfig,omitempty"`
	MaxRunningTimeMinutes int32               `json:"maxRunningTimeMinutes,omitempty"`
}

type DataItem struct {
	JobName    string     `json:"jobName,omitempty"`
	UserName   string     `json:"userName,omitempty"`
	DataSource DataSource `json:"dataSource,omitempty"`
}

// +kubebuilder:validation:Enum=Pending;Running;Stopped;Stopping
type TensorboardPhase string

const (
	TBPending  TensorboardPhase = "Pending"
	TBRunning  TensorboardPhase = "Running"
	TBStopped  TensorboardPhase = "Stopped"
	TBStopping TensorboardPhase = "Stopping"
)

type TensorboardState struct {
	Phase              TensorboardPhase `json:"phase,omitempty"`
	Reason             string           `json:"reason,omitempty"`
	Message            string           `json:"message,omitempty"`
	LastTransitionTime metav1.Time      `json:"lastTransitionTime,omitempty"`
}

// TensorboardStatus defines the observed state of Tensorboard
type TensorboardStatus struct {
	State       TensorboardState `json:"state,omitempty"`
	CreateTime  metav1.Time      `json:"createTime,omitempty"`
	StartTime   metav1.Time      `json:"startTime,omitempty"`
	StoppedTime metav1.Time      `json:"stoppedTime,omitempty"`
	// TensorboardUrl is the url of the tensorboard
	RunningDuration metav1.Duration `json:"runningDuration,omitempty"`
	TensorboardUrl  string          `json:"tensorboardUrl,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:printcolumn:name="STATUS",type="string",JSONPath=".status.state.phase"
//+kubebuilder:printcolumn:name="AGE",type="date",JSONPath=".metadata.creationTimestamp"
//+kubebuilder:resource:shortName=tb;tensor

// Tensorboard is the Schema for the tensorboards API
type Tensorboard struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   TensorboardSpec   `json:"spec,omitempty"`
	Status TensorboardStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// TensorboardList contains a list of Tensorboard
type TensorboardList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Tensorboard `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Tensorboard{}, &TensorboardList{})
}
