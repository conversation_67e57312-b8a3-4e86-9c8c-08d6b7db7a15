[inet_http_server]
port = :9001

[program:gotty]
command = /app/gotty/gotty --address 0.0.0.0 --port 8080 --ws-origin '.*' --permit-write --reconnect /bin/bash
stdout_logfile=/var/log/%(program_name)s.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=3

[program:vscode]
command = /app/vscode/vscode 
stdout_logfile=/var/log/%(program_name)s.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=3

[program:jupyter]
command = jupyter lab --no-browser --ip=0.0.0.0 --allow-root --notebook-dir='/code' --port=22 --NotebookApp.token='' --LabApp.base_url=${BASE_URL}
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
