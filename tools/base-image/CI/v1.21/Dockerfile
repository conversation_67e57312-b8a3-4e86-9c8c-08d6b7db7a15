FROM registry.cnbita.com:5000/golangci/golang:1.21

ENV GITLAB_ACCESS_TOKEN=**************************

RUN echo "machine gitlab.bitahub.com login oauth2accesstoken password ${GITLAB_ACCESS_TOKEN}" > ~/.netrc \
    && go env -w GOPROXY=https://goproxy.cn,direct \
    && go env -w GOPRIVATE=gitlab.bitahub.com/hero-os/hero-os-util \
    && go install github.com/golangci/golangci-lint/cmd/golangci-lint@v1.59.1