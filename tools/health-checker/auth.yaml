apiVersion: v1
kind: ServiceAccount
metadata:
  name: trainingjob-health-checker
  namespace: hero-user
--- 
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: trainingjob-health-checker-role
  namespace: hero-user
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: trainingjob-health-checker-binding
  namespace: hero-user
subjects:
- kind: ServiceAccount
  name: trainingjob-health-checker
roleRef:
  kind: Role
  name: trainingjob-health-checker-role
  apiGroup: rbac.authorization.k8s.io