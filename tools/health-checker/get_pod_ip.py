import sys
from kubernetes import client, config

def get_pod_ip(pod_name, namespace):
    try:
        config.load_incluster_config()
        v1 = client.CoreV1Api()
        pod = v1.read_namespaced_pod(name=pod_name, namespace=namespace)
        return pod.status.pod_ip
    except Exception as e:
        print(f"获取 Pod IP 时出错: {e}", file=sys.stderr)
        return None

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python get_pod_ip.py <pod_name> <namespace>", file=sys.stderr)
        sys.exit(1)
    pod_name = sys.argv[1]
    namespace = sys.argv[2]
    pod_ip = get_pod_ip(pod_name, namespace)
    if pod_ip:
        print(pod_ip)