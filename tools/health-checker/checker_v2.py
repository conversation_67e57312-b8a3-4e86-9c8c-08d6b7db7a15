import os
import torch
import logging
import faulthandler
import torch.multiprocessing as mp
from kubernetes import client, config
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Iterator, Mapping, Any
import torch.distributed as dist
import time
import socket
from contextlib import contextmanager
import json
import argparse
import datetime

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 常量定义
CHECK_ERROR = "CheckerFailed"
CHECK_GPU_COMMUNICATE_ERROR = "GPUDistributedCommunicationFailed"
CHECK_POD_COMMUNICATE_ERROR = "PodDistributedCommunicationFailed"
DEFAULT_NAMESPACE = "default"
COMM_TIMEOUT = datetime.timedelta(seconds=600)

class Timer(Mapping[str, float]):
    def __init__(self) -> None:
        self._times: Dict[str, List[float]] = {}

    @contextmanager
    def __call__(self, name: str) -> Iterator[None]:
        start = time.perf_counter()
        try:
            yield
        finally:
            end = time.perf_counter()
            elapsed_ms = 1000 * (end - start)
            self._times.setdefault(name, []).append(elapsed_ms)
            logger.debug(f"Timer '{name}' took {elapsed_ms:.2f}ms")

    def __getitem__(self, name: str) -> float:
        times = self._times.get(name, [])
        return max(times) if times else 0.0

    def __iter__(self) -> Iterator[str]:
        return iter(self._times)

    def __len__(self) -> int:
        return len(self._times)

@dataclass
class CheckerUnit:
    """单个检查项的结果容器"""
    check_type: str
    result: Optional[str] = None
    message: Optional[str] = None

@dataclass
class CheckerResp:
    message: List[CheckerUnit] = field(default_factory=list)

    def to_dict(self) -> List[CheckerUnit]:
        return [
            {"checkType": unit.check_type, "result": unit.result, "message": unit.message}
            for unit in self.message if unit.result is not None
        ]

class Checker:
    def __init__(self):
        self.pod_name = socket.gethostname()
        self.namespace = os.environ.get('NAMESPACE', DEFAULT_NAMESPACE)
        self.checker_key = "system.hero.ai/node-health-check-result"
        self.IBName = None
        self._pod = None
        self.checker_value = CheckerResp()

        self.master_addr = os.environ.get('MASTER_ADDR', 'localhost')
        self.master_port = os.environ.get('MASTER_PORT', '21000')  
        self.local_addr = os.environ.get('LOCAL_POD_IP', 'localhost')
        self.local_port = os.environ.get('LOCAL_PORT', '21001')  
        self.task_world_size = int(os.environ.get('TASK_WORLD_SIZE', 0))
        self.task_rank_index = int(os.environ.get('TASK_RANK_INDEX', 0)) + int(os.environ.get('VC_TASK_INDEX', 0))
        self._init_kubernetes()
        self.gpus_per_pod = int(os.environ.get('POD_DEVICE_NUM', 0)) 

    def _init_kubernetes(self):
        try:
            config.load_incluster_config()
            self.client = client.CoreV1Api()
        except Exception as e:
            logger.error(f"Kubernetes init failed: {e}")

    def _init_distributed_gloo(self) -> None:
        cunit = CheckerUnit(check_type="init_gloo_distributed")
        try:
            init_method = f'tcp://{self.master_addr}:{self.master_port}'
            dist.init_process_group(
                backend="gloo",
                init_method=init_method,
                rank=self.task_rank_index,
                world_size=self.task_world_size,
                timeout=COMM_TIMEOUT
            )
            logger.info(f"Gloo initialized (global rank {self.task_rank_index}/{self.task_world_size})")
        except Exception as e:
            logger.error("Gloo distributed init failed: %s", e)
            cunit.message="Gloo distributed init failed"
            self.checker_value.message.append(cunit)

    def check_alltoall(self) -> None:
        self._init_distributed_gloo()
        cunit = CheckerUnit(check_type="pod_alltoall_check")
        if not dist.is_initialized():
            logger.warning("Gloo distributed not initialized, skipping AlltoAll check")
            return

        try:
            logger.info("Starting AlltoAll communication checks")
            
            input_tensor = torch.ones(1024  * self.task_world_size , dtype=torch.float32) * (self.task_rank_index + 1)
            output_tensor = torch.zeros(1024 * self.task_world_size, dtype=torch.float32)
            
            dist.all_to_all_single(output_tensor, input_tensor)
            expected = 1024 * sum(range(1, self.task_world_size + 1))
            if not torch.allclose(output_tensor.sum(), torch.tensor(expected, dtype=torch.float32)):
                cunit.message = f"AlltoAll result mismatch. Expected {expected}, got {output_tensor.sum().item()}"
                cunit.result = CHECK_POD_COMMUNICATE_ERROR
            
            logger.info("AlltoAll check completed")
        except Exception as e:
            cunit.message = "AlltoAll communication failed"
            cunit.result = CHECK_POD_COMMUNICATE_ERROR
            logger.error("AlltoAll check failed: %s", e)
        finally:
            if cunit.message:
                self.checker_value.message.append(cunit)
            if dist.is_initialized():
                dist.destroy_process_group()

    @staticmethod
    def _allreduce_worker(
        local_rank: int,
        shared_list: list,
        master_addr: str,
        master_port: str,
        gpus_per_pod: int
    ) -> None:
        try:
            logger.info(f"Initializing NCCL for local_rank {local_rank}")
            init_method = f'tcp://{master_addr}:{master_port}'
            
            try:
                config.load_incluster_config()
            except Exception as e:
                logger.warning(f"Kubernetes init in subprocess failed: {e}")

            dist.init_process_group(
                backend="nccl",
                init_method=init_method,
                rank=local_rank,
                world_size=gpus_per_pod,
                timeout=COMM_TIMEOUT
            )
            torch.cuda.set_device(local_rank)
            logger.info(f"NCCL initialized (local rank {local_rank}/{gpus_per_pod})")

            tensor = torch.ones(1024, device=f'cuda:{local_rank}', dtype=torch.float32) * (local_rank + 1)
            dist.all_reduce(tensor, op=dist.ReduceOp.SUM)
            
            expected = sum(range(1, gpus_per_pod + 1)) * 1024
            actual = int(tensor.sum().item())
            if actual != expected:
                error_msg = f"GPU {local_rank} AllReduce error: Expected {expected}, got {actual}"
                shared_list.append(error_msg)
                logger.error(error_msg)
            else:
                logger.info(f"GPU {local_rank} AllReduce check passed")

        except Exception as e:
            error_msg = f"GPU {local_rank} AllReduce failed: {str(e)}"
            shared_list.append(error_msg)
            logger.error(error_msg)
        finally:
            if dist.is_initialized():
                dist.destroy_process_group()

    def check_allreduce(self) -> None:
        """根据GPU数量启动多进程检查"""
        cunit = CheckerUnit(check_type="gpu_allreduce_check")     
        if self.gpus_per_pod == 0:
            logger.info(f"No CUDA devices available, Skip AllReduce check")
            return
        
        logger.info(f"Checker the number of Gpus requested by a pod is {self.gpus_per_pod}")
        actual_device_num = torch.cuda.device_count()
        logger.info(f"Checker the number of Gpus actually available is {actual_device_num}")
        if actual_device_num != self.gpus_per_pod:
            logger.info(f"GPU num is inconsistent, Skip AllReduce check")
            cunit.message =f"The number of Gpus requested by a pod is {self.gpus_per_pod} and the number of Gpus actually available is {actual_device_num}"
            cunit.result = CHECK_GPU_COMMUNICATE_ERROR
            self.checker_value.message.append(cunit)
            return 
        
        try:
            manager = mp.Manager()
            shared_list = manager.list()
            
            logger.info(f"Starting AllReduce checks with {self.gpus_per_pod} GPUs")
            
            ctx = mp.get_context('spawn')
            with ctx.Pool(processes=self.gpus_per_pod) as pool:
                results = []
                for rank in range(self.gpus_per_pod):
                    res = pool.apply_async(
                        self._allreduce_worker,
                        args=(
                            rank,
                            shared_list,
                            self.local_addr,
                            self.local_port,
                            self.gpus_per_pod
                        )
                    )
                    results.append(res)
                
                for res in results:
                    res.get()

            if len(shared_list) > 0:
                cunit.message = " | ".join(shared_list)
                cunit.result = CHECK_GPU_COMMUNICATE_ERROR
                self.checker_value.message.append(cunit)
            else:
                logger.info("All GPU AllReduce checks passed")
                
        except Exception as e:
            cunit.message = f"AllReduce check failed: {str(e)}"
            cunit.result = CHECK_GPU_COMMUNICATE_ERROR
            self.checker_value.message.append(cunit)
            logger.error("AllReduce check failed: %s", e)

    def get_pod(self) -> Optional[client.V1Pod]:
        """获取并缓存Pod信息"""
        if self._pod is not None:
            return self._pod
            
        try:
            self._pod = self.client.read_namespaced_pod(
                name=self.pod_name,
                namespace=self.namespace
            )
            logger.debug("Successfully fetched pod info")
            return self._pod
        except client.exceptions.ApiException as e:
            logger.error("Failed to get pod: %s", e)
            return None
    def update_pod_annotation(self) -> None:
        pod = self.get_pod()
        if not pod:
            return

        try:
            annotations = pod.metadata.annotations or {}
            annotations[self.checker_key] = json.dumps(self.checker_value.to_dict())
            
            patch = client.V1Pod(
                metadata=client.V1ObjectMeta(
                    annotations=annotations
                )
            )
            self.client.patch_namespaced_pod(
                name=self.pod_name,
                namespace=self.namespace,
                body=patch
            )
            logger.info("Successfully updated pod annotations")
        except Exception as e:
            logger.error("Failed to update annotations: %s", e)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description='Kubernetes Pod Resource Checker',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    args = parser.parse_args()

    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
 
    faulthandler.enable() # 捕获段错误异常

    try:
        logger.info("Starting all checks")
        checker = Checker()
        checker.check_alltoall()
        checker.check_allreduce()  # 自动根据GPU数量进行检查
        checker.update_pod_annotation()
        logger.info("All checks completed")
    except Exception as e:
        logger.critical("Unhandled exception in checker: %s", e)