#!/bin/bash

set -x

# 检查参数数量
if [ $# -ne 2 ]; then
    echo "用法: $0 <master_pod_name> <namespace>"
    exit 1
fi

sleep 5

master_pod=$1
namespace=$2

# 获取 master Pod IP
pod_ip=$(python3 get_pod_ip.py "$master_pod" "$namespace")
retry=0
while [ -z "$pod_ip" ]; do
    sleep 5
    pod_ip=$(python3 get_pod_ip.py "$master_pod" "$namespace")
    retry=$((retry+1))
    if [ $retry -ge 10 ]; then
        echo "Pod ${namespace}/${master_pod} IP获取失败"
        exit 0
    fi
    echo "Pod ${namespace}/${master_pod} IP获取失败, 重试中..."
done

echo "Pod ${namespace}/${master_pod} IP: ${pod_ip}"

# 执行环境检测脚本
export MASTER_ADDR=${pod_ip}
export MASTER_PORT=21000

python3 /app/checker_v2.py

exit 0