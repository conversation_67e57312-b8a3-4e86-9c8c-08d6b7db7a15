import os
import torch
import logging
from kubernetes import client, config
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Iterator, Mapping, Any
import torch.distributed as dist
import time
import socket
from contextlib import contextmanager
import json
import argparse
import datetime

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 常量定义
CHECK_STORAGE_FILE_ERROR = "StorageCheckFailed"
CHECK_GPU_COMPUTE_ERROR = "ComputeGeemFailed"
CHECK_GPU_COMMUNICATE_ERROR = "GPUDistributedCommunicationFailed"
DEFAULT_NAMESPACE = "default"
COMM_TIMEOUT = datetime.timedelta(seconds=30)  # 通信超时时间


class Timer(Mapping[str, float]):
    """带最大时间记录的性能计时器"""
    def __init__(self) -> None:
        self._times: Dict[str, List[float]] = {}

    @contextmanager
    def __call__(self, name: str) -> Iterator[None]:
        start = time.perf_counter()
        try:
            yield
        finally:
            end = time.perf_counter()
            elapsed_ms = 1000 * (end - start)
            self._times.setdefault(name, []).append(elapsed_ms)
            logger.debug(f"Timer '{name}' took {elapsed_ms:.2f}ms")

    def __getitem__(self, name: str) -> float:
        times = self._times.get(name, [])
        return max(times) if times else 0.0

    def __iter__(self) -> Iterator[str]:
        return iter(self._times)

    def __len__(self) -> int:
        return len(self._times)


@dataclass
class CheckerUnit:
    """单个检查项的结果容器"""
    check_type: str
    result: Optional[str] = None
    message: Optional[str] = None


@dataclass
class CheckerResp:
    """检查结果汇总数据结构"""
    message: List[CheckerUnit] = field(default_factory=list)

    def to_dict(self) -> List[CheckerUnit]:
        return [
                {"checkType": unit.check_type, "result": unit.result, "message": unit.message}
                for unit in self.message if unit.result is not None
            ]


class Checker:
    """资源检查器主类"""
    def __init__(self):
        try:
            config.load_incluster_config()
            self.client = client.CoreV1Api()
        except Exception as e:
            logger.critical("Failed to initialize Kubernetes client: %s", e)
            raise

        self.pod_name = socket.gethostname()
        self.namespace = os.environ.get('NAMESPACE', DEFAULT_NAMESPACE)
        self.checker_key = "system.hero.ai/node-health-check-result"  
        self.IBName = "intel.com/infiniband_rdma_netdevs"
        self._pod = None
        self.checker_value = CheckerResp()
        self.world_size = int(os.environ.get('NNODES', '1')) * int(os.environ.get('ACC_DEVICE_NUM', '1'))
        logger.info("nccl word size: %d", self.world_size)
        try:
            if self.existed_ib_resource():
                self.enable_ib()
            
            self.protocol = "nccl" if torch.cuda.is_available() else "gloo"
            self._init_distributed(self.protocol)
        except Exception as e:
            logger.error("Initialization failed: %s", e)

    def _init_distributed(self, protocol: str) -> None:
        """初始化分布式环境"""
        logger.info("Initializing distributed backend: %s", protocol)
        try:
            dist.init_process_group(
                backend=protocol,
                init_method='env://',
                timeout=COMM_TIMEOUT  # 添加超时参数
            )
        except Exception as e:
            logger.error("Distributed init failed: %s", e)

    def get_pod(self) -> Optional[client.V1Pod]:
        """获取并缓存Pod信息"""
        if self._pod is not None:
            return self._pod
            
        try:
            self._pod = self.client.read_namespaced_pod(
                name=self.pod_name,
                namespace=self.namespace
            )
            logger.debug("Successfully fetched pod info")
            return self._pod
        except client.exceptions.ApiException as e:
            logger.error("Failed to get pod: %s", e)
            return None

    def check_geem_compute(self) -> None:
        """GPU计算能力检查"""
        if not torch.cuda.is_available():
            logger.warning("CUDA not available, skipping compute check")
            return

        logger.info("Starting GPU compute checks")
        cunit = CheckerUnit(check_type="compute_geem")
        try:
            size = (4096, 4096)  # 16MB per tensor
            a = torch.full(size, 3.0, device='cuda',dtype=torch.float32)
            b = torch.full(size, 2.0, device='cuda',dtype=torch.float32)
            
            add_result = a + b
            mul_result = a * b
            matmul_result = torch.mm(a, b.T)  # 矩阵乘法
            
            if not torch.all(add_result == 5.0):
                cunit.message = "Addition result incorrect"
            elif not torch.all(mul_result == 6.0):
                cunit.message = "Multiplication result incorrect"
            elif not torch.allclose(matmul_result, torch.full((4096,4096), 3*2*4096, device='cuda',dtype=torch.float32)):
                cunit.message = "Matrix multiplication result incorrect"
            
            if cunit.message:
                cunit.result= CHECK_GPU_COMPUTE_ERROR
                self.checker_value.message.append(cunit)
                logger.warning("GPU compute issue: %s", cunit.message)
            
            del a, b, add_result, mul_result, matmul_result
            torch.cuda.empty_cache()
        except Exception as e:
            logger.error("GPU compute check failed: %s", e)
            self.checker_value.message.append(cunit)

    def update_pod_annotation(self) -> None:
        """更新Pod注解"""
        pod = self.get_pod()
        if not pod:
            return

        try:
            annotations = pod.metadata.annotations or {}
            annotations[self.checker_key] = json.dumps(self.checker_value.to_dict())
            
            patch = client.V1Pod(
                metadata=client.V1ObjectMeta(
                    annotations=annotations
                )
            )
            self.client.patch_namespaced_pod(
                name=self.pod_name,
                namespace=self.namespace,
                body=patch
            )
            logger.info("Successfully updated pod annotations")
        except Exception as e:
            logger.error("Failed to update annotations: %s", e)

    def existed_ib_resource(self) -> bool:
        """检查是否配置了IB资源"""
        pod = self.get_pod()
        if not pod or not pod.spec:
            return False

        try:
            for container in pod.spec.containers:
                requests = getattr(container.resources, 'requests', None)
                if requests and self.IBName in requests:
                    logger.info("Detected IB resource request")
                    return True
            return False
        except AttributeError as e:
            logger.warning("Error checking IB resources: %s", e)
            return False

    def check_all_reduce(self, loops: int = 5) -> None:
        """分布式通信检查"""
        if not dist.is_initialized():
            logger.warning("Distributed not initialized, skipping all-reduce check")
            return

        logger.info("Starting all-reduce communication checks")
        cunit = CheckerUnit(check_type="communicate_check")
        try:
            timer = Timer()
            buffer = torch.ones(1024 * 1024, device='cuda')  # 1MB数据
            expected = buffer * self.world_size
            for i in range(loops):
                with timer(f"all_reduce_{i}"):
                    dist.all_reduce(buffer, op=dist.ReduceOp.SUM)
                expected = torch.full_like(buffer, self.world_size *(self.world_size**i))
                if not torch.allclose(buffer, expected):
                    cunit.message = f"AllReduce result mismatch at loop {i}"
                    break  
                expected = expected * self.world_size
            
            avg_time = sum(timer["all_reduce_" + str(i)] for i in range(loops)) / loops
            logger.info(f"AllReduce average time: {avg_time:.2f}ms")
            
            if not cunit.message:
                logger.info("All-reduce checks passed")
        except Exception as e:
            cunit.message = f"Communication error: {self.protocol} communicated failed by AllReduce "
            logger.error("All-reduce check failed: %s", e)
        finally:
            if cunit.message:
                cunit.result = CHECK_GPU_COMMUNICATE_ERROR
                self.checker_value.message.append(cunit)

    def checker(self) -> None:
        """执行所有检查流程"""
        try:
            logger.info("Starting resource checks")
            
            # 执行检查项
            self.check_geem_compute()
            self.check_all_reduce()
            
            # 更新注解
            self.update_pod_annotation()
            
            logger.info("Resource checks completed")
        except Exception as e:
            logger.critical("Unhandled exception in checker: %s", e)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description='Kubernetes Pod Resource Checker',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    args = parser.parse_args()

    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")

    try:
        checker = Checker()
        checker.checker()
    except Exception as e:
        logger.critical("Checker failed: %s", e)