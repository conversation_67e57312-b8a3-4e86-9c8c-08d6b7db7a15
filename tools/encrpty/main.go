package main

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha512"
	"encoding/base64"
	"fmt"
	"log"
	"regexp"
	"strings"

	"github.com/spf13/cobra"
	"golang.org/x/crypto/pbkdf2"
)

var password = "123#lei@nao@ai@#123"

var rootCmd = &cobra.Command{
	Use:   "crpty",
	Short: `crpty is a CLI tool for nacos`,
	Run: func(cmd *cobra.Command, args []string) {
		// 默认操作或显示帮助信息
		fmt.Println("Welcome to crpty CLI. Use 'bita --help' for available commands.")
	},
	RunE: runCmd,
}

func runCmd(cmd *cobra.Command, args []string) error {
	isEncrpty, _ := cmd.Flags().GetBool("encrpty")
	//s, _ := cmd.Flags().GetString("string")
	password, _ := cmd.Flags().GetString("password")
	//fmt.Println("xxxxxxxxx", value, value1, args[0])
	s := args[0]
	if isEncrpty {

		text, err := Encrypt(s, password)
		if err != nil {
			return err
		}

		ss := "ENC(" + text + ")"
		fmt.Printf("密文：%s\n", ss)
		return nil
	}

	if strings.HasPrefix(s, "ENC(") && strings.HasSuffix(s, ")") {
		text, err := decrypt(s[4:len(s)-1], password)
		if err != nil {
			return err
		}

		fmt.Printf("明文：%s", text)

	}

	return nil
}

func init() {
	rootCmd.Flags().BoolP("encrpty", "e", false, "encrpty a string")
	// rootCmd.Flags().StringP("string", "s", "", "crpty string")
	rootCmd.Flags().StringP("password", "p", "123#lei@nao@ai@#123", "crpty string")
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		log.Fatalf("Error: %v", err)
	}
}

func Encrypt(message, password string) (string, error) {
	encryptor := newPBEWithHMACSHA512AndAES_256(newConfig(
		setPassword(password),
		setSaltGenerator(RandomSaltGenerator{}),
		setIvGenerator(RandomIvGenerator{}),
	))

	return encryptor.Encrypt(message)
}

func decrypt(encode, password string) (string, error) {
	decryptor := newPBEWithHMACSHA512AndAES_256(newConfig(
		setPassword(password),
		setSaltGenerator(RandomSaltGenerator{}),
		setIvGenerator(RandomIvGenerator{}),
	))

	return decryptor.Decrypt(encode)
}

type EncryptorConfig struct {
	Password      string
	SaltGenerator Generator
	IvGenerator   IvGenerator
}

type PBEWithHMACSHA512AndAES_256 struct {
	config EncryptorConfig
}

func newPBEWithHMACSHA512AndAES_256(config EncryptorConfig) *PBEWithHMACSHA512AndAES_256 {
	return &PBEWithHMACSHA512AndAES_256{
		config: config,
	}
}

func (enc *PBEWithHMACSHA512AndAES_256) Encrypt(message string) (string, error) {
	saltGenerator := enc.config.SaltGenerator
	ivGenerator := enc.config.IvGenerator
	password := enc.config.Password
	algorithmBlockSize := 16
	keyObtentionIterations := 1000

	salt, err := saltGenerator.GenerateSalt(algorithmBlockSize)
	if err != nil {
		return "", err
	}
	iv, err := ivGenerator.GenerateIv(algorithmBlockSize)
	if err != nil {
		return "", err
	}

	dk := pbkdf2.Key([]byte(password), salt, keyObtentionIterations, 32, sha512.New)
	encText, err := aes256Encrypt([]byte(message), dk, iv)
	if err != nil {
		return "", err
	}
	result := encText
	if ivGenerator.IncludePlainIvInEncryptionResults() {
		result = append(iv, result...)
	}
	if saltGenerator.IncludePlainSaltInEncryptionResults() {
		result = append(salt, result...)
	}
	//执行Base64编码
	encodeString := base64.StdEncoding.EncodeToString(result)
	return encodeString, nil
}

func (enc *PBEWithHMACSHA512AndAES_256) Decrypt(message string) (string, error) {
	saltGenerator := enc.config.SaltGenerator
	ivGenerator := enc.config.IvGenerator
	password := enc.config.Password
	algorithmBlockSize := 16
	keyObtentionIterations := 1000

	//Base64解码
	encrypted, err := base64.StdEncoding.DecodeString(message)
	if err != nil {
		return "", err
	}
	var salt []byte
	var iv []byte
	if saltGenerator.IncludePlainSaltInEncryptionResults() {
		salt = encrypted[:algorithmBlockSize]
		encrypted = encrypted[algorithmBlockSize:]
	}
	if ivGenerator.IncludePlainIvInEncryptionResults() {
		iv = encrypted[:algorithmBlockSize]
		encrypted = encrypted[algorithmBlockSize:]
	}
	dk := pbkdf2.Key([]byte(password), salt, keyObtentionIterations, 32, sha512.New)
	text, err := aes256Decrypt(encrypted, dk, iv)
	if err != nil {
		return "", err
	}
	p := regexp.MustCompile(`[\x01-\x08]`)
	return p.ReplaceAllString(string(text), ""), nil
}

func aes256Encrypt(origData, key, iv []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	origData = pKCS5Padding(origData, blockSize)
	encrypted := make([]byte, len(origData))
	blockMode := cipher.NewCBCEncrypter(block, iv)
	blockMode.CryptBlocks(encrypted, origData)
	return encrypted, nil
}

func aes256Decrypt(encrypted, key, iv []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(encrypted))
	blockMode.CryptBlocks(origData, encrypted)
	origData = pKCS5UnPadding(origData)
	return origData, nil
}

func pKCS5Padding(cipherText []byte, blockSize int) []byte {
	padding := blockSize - len(cipherText)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(cipherText, padText...)
}

func pKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	unPadding := int(origData[length-1])
	return origData[:(length - unPadding)]
}

func newConfig(configList ...JasConfig) EncryptorConfig {
	encryptorConfig := EncryptorConfig{}
	for _, c := range configList {
		c(&encryptorConfig)
	}
	return encryptorConfig
}

type JasConfig func(*EncryptorConfig)

func setPassword(password string) JasConfig {
	return func(encryptorConfig *EncryptorConfig) {
		encryptorConfig.Password = password
	}
}

func setSaltGenerator(generator Generator) JasConfig {
	return func(encryptorConfig *EncryptorConfig) {
		encryptorConfig.SaltGenerator = generator
	}
}

func setIvGenerator(generator IvGenerator) JasConfig {
	return func(encryptorConfig *EncryptorConfig) {
		encryptorConfig.IvGenerator = generator
	}
}

type Generator interface {
	GenerateSalt(lengthBytes int) ([]byte, error)

	IncludePlainSaltInEncryptionResults() bool
}

type RandomSaltGenerator struct {
}

func (g RandomSaltGenerator) GenerateSalt(lengthBytes int) ([]byte, error) {
	salt := make([]byte, lengthBytes)
	_, err := rand.Read(salt)
	if err != nil {
		return nil, err
	}
	return salt, nil
}

func (g RandomSaltGenerator) IncludePlainSaltInEncryptionResults() bool {
	return true
}

type IvGenerator interface {
	GenerateIv(lengthBytes int) ([]byte, error)

	IncludePlainIvInEncryptionResults() bool
}

type RandomIvGenerator struct {
}

func (g RandomIvGenerator) GenerateIv(lengthBytes int) ([]byte, error) {
	salt := make([]byte, lengthBytes)
	_, err := rand.Read(salt)
	if err != nil {
		return nil, err
	}
	return salt, nil
}

func (g RandomIvGenerator) IncludePlainIvInEncryptionResults() bool {
	return true
}
