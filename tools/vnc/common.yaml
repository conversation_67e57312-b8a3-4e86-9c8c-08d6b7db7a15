apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novnc-web
  namespace: hero-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - backend:
          service:
            name: novnc-web
            port:
              number: 6081
        path: /desktop/(.*)
        pathType: ImplementationSpecific
  - http:
      paths:
      - backend:
          service:
            name: novnc-web
            port:
              number: 6081
        path: /websockify
        pathType: ImplementationSpecific
---
apiVersion: v1            
kind: Service             
metadata:             
  name: novnc-web       
  namespace: hero-system
spec:
  selector:
    app: novnc-web
  type: ClusterIP                 
  ports:              
  - protocol: TCP
    port: 6081
    targetPort: 6081
    name: websockify
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: novnc-web
  name: novnc-web
  namespace: hero-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: novnc-web
  template:
    metadata:
      labels:
        app: novnc-web
    spec:
      containers:
      - image: registry.cnbita.com:5000/heros-stg/websockify:v1.0.0
        imagePullPolicy: Always
        name: websockify-container
        ports:
        - containerPort: 6081
          protocol: TCP
        volumeMounts:
        - mountPath: /websockify/token
          name: token-config
        - mountPath: /etc/secret
          name: cert
      initContainers:
      - env:
        - name: NACOS_SERVER_URL
          value: nacos-server-service.basic-component.svc:8848
        - name: JASYPT_ENCRYPTOR_PASSWORD
          value: <%JASYPT_ENCRYPTOR_PASSWORD%> #123#lei@nao@ai@#123
        image: registry.cnbita.com:5000/heros-stg/init-config:v1.0.0 #registry.cnbita.com:5000/heros-stg/init-config:v1.0.0
        imagePullPolicy: Always
        name: cert-init
        volumeMounts:
        - mountPath: /etc/secret
          name: cert
      restartPolicy: Always
      volumes:
      - hostPath:
          path: /user-storage/gbasic-component/gdata/vnctoken
          type: ""
        name: token-config
      - emptyDir: {}
        name: cert