apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Values.namespace | default "system"}}
  name: novnc-web
  labels:
    app: novnc-web
spec:
  selector:
    matchLabels:
      app: novnc-web
  replicas: 1
  template:
    metadata:
      labels:
        app: novnc-web
    spec:
      containers:
       - name: websockify-container
         image: registry.bitahub.com:5000/leinaoyun-tag/websockify:secret
         ports:
         - containerPort: 6081
         volumeMounts:
           - mountPath: /websockify/token
             name: token-config
           - name: secret
             mountPath: /etc/secret
             readOnly: true
         imagePullPolicy: Always
      restartPolicy: Always
      volumes:
        - name: token-config
          hostPath:
            path: /gdata/vnctoken
        - name: secret
          secret:
            defaultMode: 420
            secretName: domain-secret
---
apiVersion: v1            
kind: Service             
metadata:             
  name: novnc-web       
  namespace: {{ .Values.namespace | default "system"}}
spec:
  selector:
    app: novnc-web
  type: ClusterIP                 
  ports:              
  - protocol: TCP
    port: 6081
    targetPort: 6081
    name: websockify
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novnc-web
  namespace: {{ .Values.namespace | default "system"}} 
spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - backend:
          service:
            name: novnc-web
            port:
              number: 6081
        path: /
        pathType: ImplementationSpecific