#!/bin/bash

if [ -z "$VNCPWD" ]; then
        mypasswd="root12"
else
        mypasswd=$VNCPWD
fi
if [ -z "$SCREENWIDTH" ] || [ -z "$SCREENHEIGHT" ]; then
        screenwidth=1920
        screenheight=1080
else
        screenwidth=$SCREENWIDTH
        screenheight=$SCREENHEIGHT
fi

mkdir -p ~/.vnc
echo 'vnc pwd ==> [' $mypasswd ']'
echo 'vnc screen ==> [' "$screenwidth"x"$screenheight" ']'
echo $mypasswd | vncpasswd -f > ~/.vnc/passwd
chmod 0600 ~/.vnc/passwd

funcUbuntu(){
    echo -e '#!/bin/sh\nxrdb ~/.Xresources\nstartxfce4 &' > ~/.vnc/xstartup
    chmod +x ~/.vnc/xstartup
    rm -f /tmp/.X1-lock
    rm -f /tmp/.X11-unix/X1
    cp /etc/X11/Xresources ~/.Xresources
    systemctl set-default graphical.target
    echo -e "/usr/bin/xfce4-terminal.wrapper" | update-alternatives --config x-terminal-emulator
    vncserver -kill :1
    vncserver -localhost no :1 -geometry "$screenwidth"x"$screenheight" -depth 24
}

funcDebian(){
    rm -f /tmp/.X1-lock
    rm -f /tmp/.X11-unix
    cp /etc/X11/Xresources/x11-common ~/.Xresources
    rc-service dbus start
    systemctl set-default graphical.target
    vncserver -kill :1
    vncserver -localhost no :1 -geometry "$screenwidth"x"$screenheight" -depth 24
}

funcCentos(){
    echo -e '#!/bin/sh\nxrdb ~/.Xresources\nstartxfce4 &' > ~/.vnc/xstartup
    chmod +x ~/.vnc/xstartup
    rm -f /tmp/.X1-lock
    rm -f /tmp/.X11-unix/X1
    cp /etc/X11/Xresources ~/.Xresources
    mv /etc/xdg/autostart/xfce-polkit.desktop /etc/xdg/autostart/xfce-polkit.desktop.disabled
    systemctl set-default graphical.target
    vncserver -kill :1
    vncserver :1 -geometry "$screenwidth"x"$screenheight" -depth 24
}

funcOpenEuler(){
    echo -e '#!/bin/sh\nxrdb ~/.Xresources\nstartxfce4 &' > ~/.vnc/xstartup
    chmod +x ~/.vnc/xstartup
    rm -f /tmp/.X1-lock
    rm -f /tmp/.X11-unix/X1
    cp /etc/X11/Xresources ~/.Xresources
    mv /etc/xdg/autostart/xfce-polkit.desktop /etc/xdg/autostart/xfce-polkit.desktop.disabled
    echo 'user-session=xfce' >> /etc/lightdm/lightdm.conf.d/60-lightdm-gtk-greeter.conf
    systemctl start lightdm
    systemctl enable lightdm
    systemctl set-default graphical.target
    systemctl disable gdm
    vncserver -kill :1
    vncserver :1 -geometry "$screenwidth"x"$screenheight" -depth 24
}

if [ -f /etc/os-release ]; then
    source /etc/os-release
    if [ "${ID}" = "ubuntu" ]; then
        funcUbuntu
    elif [ "${ID}" = "debian" ]; then
        funcDebian
    elif [ "${ID}" = "centos" ]; then
        funcCentos
    elif [ "${ID}" = "openEuler" ]; then
        funcOpenEuler
    fi
fi

tail -f /dev/null
