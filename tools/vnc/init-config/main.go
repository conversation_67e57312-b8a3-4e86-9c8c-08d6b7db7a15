package main

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"syscall"

	"github.com/pkg/errors"
	"gitlab.bitahub.com/hero-os/hero-os-util/config"
)

var (
	cfg          *config.Config
	EC           = &EnvConfig{}
	err          error
	keyFileName  = "/etc/secret/tls.key"
	certFileName = "/etc/secret/tls.cert"
)

type EnvConfig struct {
	PublicCert publicCert `yaml:"PublicCert"`
}

type publicCert struct {
	Cert string `yaml:"Cert"`
	Key  string `yaml:"Key"`
}

func init() {
	cfg = config.NewConfig(config.SetConfig(
		config.SetNacosConfig(os.Getenv("NACOS_SERVER_URL"), os.Getenv("JASYPT_ENCRYPTOR_PASSWORD"), os.Getenv("INIT_MODE")),
		config.SetCommonConfig(EC),
	))

	if err := cfg.InitConfig(); err != nil {
		panic(err)
	}
}

func main() {
	if len(EC.PublicCert.Key) == 0 {
		return
	}

	if err := save(keyFileName, []byte(EC.PublicCert.Key)); err != nil {
		panic(err)
	}
	if err := save(certFileName, []byte(EC.PublicCert.Cert)); err != nil {
		panic(err)
	}
}

func saveToWriter(writer io.Writer, data []byte) error {

	_, err = writer.Write(data)
	return err
}

func save(fileName string, data []byte) (retErr error) {
	dir := filepath.Dir(fileName)
	if err := os.MkdirAll(dir, 0700); err != nil {
		return err
	}
	temp, err := os.CreateTemp(dir, filepath.Base(fileName))
	if err != nil {
		return err
	}
	err = os.Chmod(temp.Name(), 0777)
	if err != nil {
		return err
	}
	defer func() {
		temp.Close()
		if retErr != nil {
			if err := os.Remove(temp.Name()); err != nil {
				fmt.Printf("Error cleaning up temp file, err: %s", err.Error())
			}
		}
	}()

	err = saveToWriter(temp, data)
	if err != nil {
		return err
	}

	if err := temp.Close(); err != nil {
		return errors.Wrap(err, "error closing temp file")
	}

	// Handle situation where the configfile is a symlink
	cfgFile := fileName
	if f, err := os.Readlink(cfgFile); err == nil {
		cfgFile = f
	}

	// Try copying the current config file (if any) ownership and permissions
	copyFilePermissions(cfgFile, temp.Name())
	return os.Rename(temp.Name(), cfgFile)
}

func copyFilePermissions(src, dst string) {
	var (
		mode     os.FileMode = 0777
		uid, gid int
	)

	fi, err := os.Stat(src)
	if err != nil {
		return
	}
	if fi.Mode().IsRegular() {
		mode = fi.Mode()
	}
	if err := os.Chmod(dst, mode); err != nil {
		return
	}

	uid = int(fi.Sys().(*syscall.Stat_t).Uid)
	gid = int(fi.Sys().(*syscall.Stat_t).Gid)

	if uid > 0 && gid > 0 {
		_ = os.Chown(dst, uid, gid)
	}
}
