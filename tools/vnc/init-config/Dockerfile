# Build the manager binary
FROM golang:1.19 as builder
ARG TARGETOS
ARG TARGETARCH

ENV GITLAB_USERNAME=hudekai
ENV GITLAB_PASSWORD=Hukai1222

WORKDIR /workspace
ENV GITLAB_USERNAME=${GITLAB_USERNAME}
ENV GITLAB_PASSWORD=${GITLAB_PASSWORD}

# 在运行 git clone 之前设置 Git 的用户名和密码
RUN echo "machine gitlab.bitahub.com login ${GITLAB_USERNAME} password ${GITLAB_PASSWORD}" > ~/.netrc


# Copy the Go Modules manifests
COPY go.mod go.mod
COPY go.sum go.sum
RUN go env -w GOPROXY=https://goproxy.cn,direct
# cache deps before building and copying source so that we don't need to re-download as much
# and so that source changes don't invalidate our downloaded layer
RUN go mod download

# Copy the go source
COPY main.go main.go

RUN CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} go build -a -o init-config main.go

# Use distroless as minimal base image to package the manager binary
# Refer to https://github.com/GoogleContainerTools/distroless for more details
FROM debian
WORKDIR /
COPY --from=builder /workspace/init-config .

ENTRYPOINT ["/init-config"]
