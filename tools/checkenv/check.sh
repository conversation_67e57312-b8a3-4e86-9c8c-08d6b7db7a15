#!/bin/bash
container_ip=$POD_IP

EXIT_FLAG="false"

retry_count=0
max_retries=5

while  [ "$EXIT_FLAG" != "true" ]; do
    #每隔2s探测nb，探测ip，发现可用再探测其具体插件
    ping -c 1 $container_ip > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        #ping通之后最大重试3次，每次间隔5秒
        for ((i=0; i<$max_retries; i++)); do
            sleep 5
            for port in "$@" 
            do
                nc -zv -w 1 "$container_ip" "$port"
                if [ $? -eq 0 ]; then
                    echo "Port $port is open"
                    case $port in
                        22)
                            echo "Port $port is open: SSH service"
                            wget -O /dev/null ""$LOCALURL"/api/v1/plugin-status/update?jobname="$JOBNAME"&plugin=ssh&state=open"
                            ;;
                        8081)
                            echo "Port $port is open: jupyter service"
                            wget -O /dev/null ""$LOCALURL"/api/v1/plugin-status/update?jobname="$JOBNAME"&plugin=jupyter&state=open"
                            ;;
                        8083)
                            echo "Port $port is open: gotty service"
                            wget -O /dev/null ""$LOCALURL"/api/v1/plugin-status/update?jobname="$JOBNAME"&plugin=gotty&state=open"
                            ;;
                        5901)
                            echo "Port $port is open: vnc service"
                            wget -O /dev/null ""$LOCALURL"/api/v1/plugin-status/update?jobname="$JOBNAME"&plugin=vnc&state=open"
                            ;;
                    esac
                else
                    if [ $i -eq $((max_retries - 1)) ]; then
                        echo "last check"
                        case $port in
                            22)
                                echo "Port $port is close: SSH service"
                                wget -O /dev/null "$LOCALURL/api/v1/plugin-status/update?jobname=$JOBNAME&plugin=ssh&state=close"
                                ;;
                            8081)
                                echo "Port $port is close: jupyter service"
                                wget -O /dev/null "$LOCALURL/api/v1/plugin-status/update?jobname=$JOBNAME&plugin=jupyter&state=close"
                                ;;
                            8083)
                                echo "Port $port is close: gotty service"
                                wget -O /dev/null "$LOCALURL/api/v1/plugin-status/update?jobname=$JOBNAME&plugin=gotty&state=close"
                                ;;
                            5901)
                                echo "Port $port is close: vnc service"
                                wget -O /dev/null "$LOCALURL/api/v1/plugin-status/update?jobname=$JOBNAME&plugin=vnc&state=close"
                                ;;
                        esac
                    fi
                fi
            done
        done
            EXIT_FLAG="true"
            break
        fi
    sleep 2
done

nc -l 127.0.0.1 16001 -k
exit 0