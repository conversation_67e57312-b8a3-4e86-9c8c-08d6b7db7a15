package main

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"time"

	v1 "k8s.io/api/admissionregistration/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/klog"
)

func readCAFiles(caCertFile string) ([]byte, error) {
	var err error
	caCertData, err := os.ReadFile(caCertFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read cacert file (%s): %v", caCertFile, err)
	}

	// certData, err := os.ReadFile(certFile)
	// if err != nil {
	// 	return fmt.Errorf("failed to read cert file (%s): %v", certFile, err)
	// }

	// keyData, err := os.ReadFile(keyFile)
	// if err != nil {
	// 	return fmt.Errorf("failed to read key file (%s): %v", keyFile, err)
	// }

	return caCertData, nil
}

func addCaCertForWebhook(kubeClient *kubernetes.Clientset, caBundle []byte, service string) error {
	// update MutatingWebhookConfigurations
	//var mutatingWebhookUpdate = func() {}
	var mutatingWebhookName = service + "-mutating-webhook-configuration"
	var mutatingWebhook *v1.MutatingWebhookConfiguration
	webhookChanged := false
	if err := wait.Poll(time.Second, 5*time.Minute, func() (done bool, err error) {
		mutatingWebhook, err = kubeClient.AdmissionregistrationV1().MutatingWebhookConfigurations().Get(context.TODO(), mutatingWebhookName, metav1.GetOptions{})
		if err != nil {
			if apierrors.IsNotFound(err) {
				klog.Errorln(err)
				return false, nil
			}
			return false, fmt.Errorf("failed to get mutating webhook %v", err)
		}
		return true, nil
	}); err != nil {
		return fmt.Errorf("failed to get mutating webhook %v", err)
	}

	for index := 0; index < len(mutatingWebhook.Webhooks); index++ {
		if mutatingWebhook.Webhooks[index].ClientConfig.CABundle == nil ||
			!bytes.Equal(mutatingWebhook.Webhooks[index].ClientConfig.CABundle, caBundle) {
			mutatingWebhook.Webhooks[index].ClientConfig.CABundle = caBundle
			webhookChanged = true
		}
	}
	if webhookChanged {
		if _, err := kubeClient.AdmissionregistrationV1().MutatingWebhookConfigurations().Update(context.TODO(), mutatingWebhook, metav1.UpdateOptions{}); err != nil {
			return fmt.Errorf("failed to update mutating admission webhooks %v %v", mutatingWebhookName, err)
		}
	}

	// update ValidatingWebhookConfigurations
	var validatingWebhookName = service + "-validating-webhook-configuration"
	var validatingWebhook *v1.ValidatingWebhookConfiguration
	webhookvalChanged := false
	if err := wait.Poll(time.Second, 5*time.Minute, func() (done bool, err error) {
		validatingWebhook, err = kubeClient.AdmissionregistrationV1().ValidatingWebhookConfigurations().Get(context.TODO(), validatingWebhookName, metav1.GetOptions{})
		if err != nil {
			if apierrors.IsNotFound(err) {
				klog.Errorln(err)
				return false, nil
			}
			return false, fmt.Errorf("failed to get validating webhook %v", err)
		}
		return true, nil
	}); err != nil {
		return fmt.Errorf("failed to get validating webhook %v", err)
	}

	for index := 0; index < len(validatingWebhook.Webhooks); index++ {
		if validatingWebhook.Webhooks[index].ClientConfig.CABundle == nil ||
			!bytes.Equal(validatingWebhook.Webhooks[index].ClientConfig.CABundle, caBundle) {
			validatingWebhook.Webhooks[index].ClientConfig.CABundle = caBundle
			webhookvalChanged = true
		}
	}
	if webhookvalChanged {
		if _, err := kubeClient.AdmissionregistrationV1().ValidatingWebhookConfigurations().Update(context.TODO(), validatingWebhook, metav1.UpdateOptions{}); err != nil {
			return fmt.Errorf("failed to update validating admission webhooks %v %v", validatingWebhookName, err)
		}
	}

	return nil
}

func newClusterClient() *kubernetes.Clientset {
	var config *rest.Config
	config, err := rest.InClusterConfig()
	if err != nil {
		klog.Errorf("k8s build config errror:%s", err.Error())
	}

	// 本地环境配置
	// var kubeconfig *string
	// if home := homedir.HomeDir(); home != "" {
	// 	kubeconfig = flag.String("kubeconfig", filepath.Join(home, ".kube", "config"), "(可选) kubeconfig 文件的绝对路径")
	// } else {
	// 	kubeconfig = flag.String("kubeconfig", "", "kubeconfig 文件的绝对路径")
	// }
	// flag.Parse()
	// 使用 KubeConfig 文件创建集群配置 Config 对象
	// config, err := clientcmd.BuildConfigFromFlags("", *kubeconfig)
	// if err != nil {
	// 	klog.Errorf("k8s build config errror:%s", err.Error())
	// }

	return kubernetes.NewForConfigOrDie(config)
}

func main() {
	cacert, err := readCAFiles(os.Getenv("CA_CERT_FILE"))
	if err != nil {
		panic(err)
	}

	client := newClusterClient()

	if err := addCaCertForWebhook(client, cacert, os.Getenv("SERVICENAME")); err != nil {
		panic(err)
	}
}
