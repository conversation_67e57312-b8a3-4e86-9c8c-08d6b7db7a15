# Build the manager binary
FROM registry.cnbita.com:5000/golangci/golang:1.20 as builder
ARG TARGETOS
ARG TARGETARCH

WORKDIR /workspace
# Copy the Go Modules manifests
COPY go.mod go.mod
COPY go.sum go.sum
RUN go env -w GOPROXY=https://goproxy.cn,direct
# cache deps before building and copying source so that we don't need to re-download as much
# and so that source changes don't invalidate our downloaded layer
RUN go mod download

# Copy the go source
COPY main.go main.go
RUN CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} go build -a -o init-ca main.go


FROM registry.cnbita.com:5000/golangci/alpine:3.14
WORKDIR /
COPY --from=builder /workspace/init-ca .
#COPY init-ca /usr/bin/init-ca

CMD ["/init-ca"]