# 第一阶段：构建阶段
FROM registry.cnbita.com:5000/pythonci/python:3.9-slim  as builder

ARG TARGETOS
ARG TARGETARCH

# 设置工作目录
WORKDIR /app

# 安装必要的构建依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    libglib2.0-0 \
    libnss3 \
    libgconf-2-4 \
    libfontconfig1 \
    && pip install pyinstaller \
    && rm -rf /var/lib/apt/lists/*

# 将项目代码复制到容器中
COPY . /app
 
# 安装依赖并使用 PyInstaller 构建二进制文件
#RUN pip install -r requirements.txt && CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH}  pyinstaller --onefile start.py --name squash-image
RUN cd docker-squash \
    && pip install -r requirements.txt \
    && CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} pyinstaller --onefile docker_squash/cli.py --name squash-image \
    && mv ./dist/squash-image ../ \
    && cd ..

RUN cd tools \
    && CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} pyinstaller --onefile count.py --name countsl \
    && mv ./dist/countsl ../ \
    && cd ..


FROM registry.cnbita.com:5000/pythonci/python:3.9-slim

RUN apt-get update && apt-get install -y wget

WORKDIR /imagebuild


# 拷贝你的脚本或工具到镜像中
COPY  --from=builder /app/build/shell/*  /imagebuild/
RUN chmod +x /imagebuild/commit-container.sh \
    && chmod +x /imagebuild/build-dockerfile.sh \
    && chmod +x /imagebuild/load-tar.sh \
    && chmod +x /imagebuild/pull-public-image.sh \
    && chmod +x /imagebuild/pull-private-image.sh \
    && chmod +x /imagebuild/build-urldockerfile.sh

# 从构建阶段复制二进制文件到运行阶段
COPY --from=builder /app/countsl /imagebuild/countsl
COPY --from=builder /app/squash-image /imagebuild/squash-image
COPY --from=builder /app/config/default-squash.cfg  /imagebuild/default-squash.cfg 

 
 

