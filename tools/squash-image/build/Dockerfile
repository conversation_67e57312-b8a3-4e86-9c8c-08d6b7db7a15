# 第一阶段：构建阶段
FROM registry.cnbita.com:5000/pythonci/python:3.9-slim as builder

ARG TARGETOS
ARG TARGETARCH

# 设置工作目录
WORKDIR /app

COPY . /app

RUN apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    libglib2.0-0 \
    libnss3 \
    libgconf-2-4 \
    libfontconfig1 \
    && pip install pyinstaller \
    && rm -rf /var/lib/apt/lists/* \
    && cd docker-squash \
    && pip install -r requirements.txt \
    && CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} pyinstaller --onefile docker_squash/cli.py --name squash-image \
    && mv ./dist/squash-image /app/build/shell/ \
    && cd ../tools \
    && CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} pyinstaller --onefile count.py --name countsl \
    && mv ./dist/countsl /app/build/shell/ \
    && cd .. \
    && cp ./config/default-squash.cfg /app/build/shell/ 


# 第二阶段：运行阶段
FROM registry.cnbita.com:5000/pythonci/python:3.9-slim

RUN apt-get update && apt-get install -y wget

WORKDIR /imagebuild

COPY --from=builder /app/build/shell/* /imagebuild/

RUN chmod -R +x /imagebuild/

