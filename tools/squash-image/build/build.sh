#!/bin/sh
# 检查命令行参数数量
if [ "$#" -eq 1 ]; then
    version="$1"
elif [ "$#" -eq 0 ]; then
    version="latest"
else
    echo "Usage: $0 [<version>]"
    exit 1
fi

basedir=`cd $(dirname $0); pwd -P`
echo ${basedir}

# 构建 Docker 镜像
docker build -t registry.cnbita.com:5000/leinaoyun/imagebuildtool-squash:"$version" -f ${basedir}/Dockerfile . --load
# docker build -t imagebuildtool:"$version"-amd64 -f ${basedir}/Dockerfile  .

# 推送 Docker 镜像到仓库
docker push registry.cnbita.com:5000/leinaoyun/imagebuildtool-squash:"$version"
