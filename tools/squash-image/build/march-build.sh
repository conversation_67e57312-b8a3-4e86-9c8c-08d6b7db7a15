#!/bin/sh

# 检查命令行参数数量
if [ "$#" -eq 1 ]; then
    version="$1"
elif [ "$#" -eq 0 ]; then
    version="latest"
else
    echo "Usage: $0 [<version>]"
    exit 1
fi

# 获取脚本所在目录的绝对路径
basedir=$(cd $(dirname $0); pwd -P)
echo "Base directory: ${basedir}"


# 创建一个 Docker Buildx 构建实例（如果没有的话）

docker buildx build --platform linux/amd64,linux/arm64 -t registry.cnbita.com:5000/leinaoyun/imagebuildtool-squash:"$version" -f ${basedir}/Dockerfile . --push


echo "Multi-architecture image successfully built and pushed."
