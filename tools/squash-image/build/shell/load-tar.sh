#!/bin/sh
set -e  # 遇到错误立即退出
set -x
username=$USERNAME
password=$PASSWORD
repourl=$IMAGEREPO                                      
TIME=$IMAGEBUILDMAXTIME
echo "$password" | docker login $repourl --username "$username" --password-stdin

tarfile=$TAR
imageurl=$IMAGEURL
#docker rmi -f $imageurl
# 获取 docker load 命令的输出作为字符串
output=$(docker load -i $tarfile 2>&1)

# 判断输出是 Loaded image 还是 Loaded image ID
if echo "$output" | grep -q "Loaded image:"; then
    # 提取镜像名称（repository:tag）
    imagename=$(echo "$output" | grep -oP 'Loaded image:\s+\K[^ ]+')
elif echo "$output" | grep -q "Loaded image ID:"; then
    # 提取镜像 ID（sha256:...）
    imagename=$(echo "$output" | grep -oP 'Loaded image ID:\s+\K[^ ]+')
fi
#imagename=$(docker load -i $tarfile -q | grep -o "Loaded image: .*" | cut -d' ' -f3)

docker tag $imagename $imageurl
docker push $imageurl
docker rmi -f $imageurl
docker logout
