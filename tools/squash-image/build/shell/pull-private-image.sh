#!/bin/sh
set -e  # 遇到错误立即退出
set -x
username=$USERNAME
password=$PASSWORD
repourl=$IMAGEREPO                                      
TIME=$IMAGEBUILDMAXTIME
imageurl=$IMAGEURL

privateuser=$PRIVATEUSER
privatepasswd=$PRIVATEPASSWD
privaterepourl=$PRIVATEIMAGEREPO
privateimage=$PRIVATEIMAGE

echo "$privatepasswd" | docker login $privaterepourl --username "$privateuser" --password-stdin
docker pull $privateimage
docker tag  $privateimage  $imageurl
#docker logout $privaterepourl

echo "$password" | docker login $repourl --username "$username" --password-stdin
docker push $imageurl
docker rmi -f $imageurl 
