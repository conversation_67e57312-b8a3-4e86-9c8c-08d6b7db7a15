#!/bin/sh
set -e  # 遇到错误立即退出
set -x
username=$USERNAME
password=$PASSWORD
repourl=$IMAGEREPO                                      
TIME=$IMAGEBUILDMAXTIME
echo "$password" | docker login $repourl --username "$username" --password-stdin

imageurl=$IMAGEURL
publicimage=$PUBLICIMAGE
#docker rmi -f $imageurl
docker pull $publicimage
docker tag  $publicimage  $imageurl 
docker push $imageurl
docker rmi -f $imageurl 
docker logout
