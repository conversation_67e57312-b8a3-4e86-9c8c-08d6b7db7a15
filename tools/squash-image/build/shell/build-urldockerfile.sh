#!/bin/sh
set -e  # 脚本遇到错误时退出
set -x  # 打印执行的命令

username=$USERNAME
password=$PASSWORD
repourl=$IMAGEREPO

# 检查环境变量 IMAGEBUILDMAXTIME 是否被设置，如果没有设置，则默认为 30m
if [ -z "$IMAGEBUILDMAXTIME" ]; then
    TIME="30m"
else
    TIME=$IMAGEBUILDMAXTIME
fi

# Docker 登录
echo "$password" | docker login $repourl --username "$username" --password-stdin || {
    echo "Docker 登录失败"
    exit 1
}

# 创建临时目录并下载 Dockerfile
mkdir -p /tmp/dir
dockerfile=/tmp/dir/Dockerfile
imageurl=$IMAGEURL

wget --no-check-certificate -O "$dockerfile" "$DOCKERFILE" || {
    echo "下载 Dockerfile 失败"
    exit 1
}

# 构建 Docker 镜像，设置超时
timeout $TIME docker build -t "$imageurl" -f "$dockerfile" "$(dirname "$dockerfile")" || {
    echo "Docker 镜像构建失败"
    exit 1
}

# 推送 Docker 镜像
docker push "$imageurl" || {
    echo "Docker 镜像推送失败"
    exit 1
}

# Docker 登出
docker logout
