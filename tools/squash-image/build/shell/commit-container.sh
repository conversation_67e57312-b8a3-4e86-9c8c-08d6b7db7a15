#!/bin/bash
set -e  # 遇到错误立即退出
set -x
username=$USERNAME
password=$PASSWORD
repourl=$IMAGEREPO                                      
TIME=$IMAGEBUILDMAXTIME
echo "$password" | docker login $repourl --username "$username" --password-stdin

container_id=$CONTAINER_ID
imageurl=$IMAGEURL
#docker rmi -f $imageurl
docker commit -a 'admin' -m 'build img' $container_id $imageurl 


# 定义变量
mountPaths=$MOUNTPATHS     
if [ -n "$mountPaths" ]; then
    timestamp=$(date +%s)  # 使用时间戳
    container_name="usercontainer_$timestamp"  

    # 运行容器
    docker run -itd -e NVIDIA_VISIBLE_DEVICES --name $container_name $imageurl sh -c "sleep 5432"


    queue=($(echo "$mountPaths" | tr '|' '\n'))

    # 遍历
    while [ ${#queue[@]} -gt 0 ]; do

        path=${queue[0]}
        
        queue=("${queue[@]:1}")
        
        if docker exec "$container_name" [ -d "$path" ] && docker exec "$container_name" [ -z "$(docker exec "$container_name" ls -A "$path")" ]; then
            echo "正在删除空目录: $path"
            if ! docker exec "$container_name" rm -rf "$path"; then
                echo "删除目录 $path 失败。"
            fi
        fi

        parent_dir=$(dirname "$path")
        
        if [ "$parent_dir" != "/" ] && [ "$parent_dir" != "." ]; then
            queue+=("$parent_dir")
        fi
    done
    
    docker commit  -a 'admin' -m 'build img' $container_name $imageurl

    docker stop $container_name
    docker rm $container_name
fi

squashLayerNum=`./countsl -c default-squash.cfg $imageurl`

if [ -n "$squashLayerNum" ] && [ "$squashLayerNum" -ne 0 ]; then
    ./squash-image -t $imageurl -f $squashLayerNum -m 'build img' --load-image true  $imageurl || true
fi

docker push $imageurl
docker logout
