#!/bin/sh
set -e  # 遇到错误立即退出
set -x  # 打印执行的命令

username=$USERNAME
password=$PASSWORD
repourl=$IMAGEREPO

# 检查环境变量 IMAGEBUILDMAXTIME 是否被设置，如果没有设置，则默认为 30m
if [ -z "$IMAGEBUILDMAXTIME" ]; then
    TIME="30m"
else
    TIME=$IMAGEBUILDMAXTIME
fi

# Docker 登录
echo "$password" | docker login $repourl --username "$username" --password-stdin || {
    echo "Docker 登录失败"
    exit 1
}

dockerfile=$DOCKERFILE
imageurl=$IMAGEURL

# 检查 Dockerfile 是否存在
if [ ! -f "$dockerfile" ]; then
    echo "Dockerfile $dockerfile 不存在"
    exit 1
fi

# 构建 Docker 镜像，设置超时时间
timeout "$TIME" docker build -t "$imageurl" -f "$dockerfile" "$(dirname "$dockerfile")" || {
    echo "Docker 镜像构建失败"
    exit 1
}

# 推送 Docker 镜像
docker push "$imageurl" || {
    echo "Docker 镜像推送失败"
    exit 1
}

# Docker 登出
docker logout
