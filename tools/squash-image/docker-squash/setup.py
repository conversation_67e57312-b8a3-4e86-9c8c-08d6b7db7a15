#!/usr/bin/python

import codecs

from setuptools import find_packages, setup

from docker_squash.version import version

with open("requirements.txt") as f:
    requirements = f.read().splitlines()

setup(
    name="docker-squash",
    version=version,
    packages=find_packages(exclude=["tests"]),
    url="https://github.com/goldmann/docker-squash",
    download_url="https://github.com/goldmann/docker-squash/archive/%s.tar.gz"
    % version,
    author="<PERSON><PERSON>",
    author_email="<EMAIL>",
    description="Docker layer squashing tool",
    license="MIT",
    keywords="docker",
    long_description=codecs.open("README.rst", encoding="utf8").read(),
    entry_points={
        "console_scripts": ["docker-squash=docker_squash.cli:run"],
    },
    tests_require=["mock"],
    install_requires=requirements,
)
