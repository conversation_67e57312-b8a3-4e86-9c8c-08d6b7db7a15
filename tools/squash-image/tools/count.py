# -*- coding: utf-8 -*-

import argparse
import logging
import sys
import os
import common

# Source: http://stackoverflow.com/questions/1383254/logging-streamhandler-and-standard-streams
class SingleLevelFilter(logging.Filter):
    def __init__(self, passlevel, reject):
        self.passlevel = passlevel
        self.reject = reject

    def filter(self, record):
        if self.reject:
            return record.levelno != self.passlevel
        else:
            return record.levelno == self.passlevel


class MyParser(argparse.ArgumentParser):
    # noinspection PyMethodMayBeStatic
    def str2bool(self, v: str) -> bool:
        if isinstance(v, bool):
            return v
        if v.lower() in ("yes", "true", "t", "y", "1"):
            return True
        elif v.lower() in ("no", "false", "f", "n", "0"):
            return False
        else:
            raise argparse.ArgumentTypeError("Boolean value expected.")

    def error(self, message):
        self.print_help()
        sys.stderr.write("\nError: %s\n" % message)
        sys.exit(2)


class CLI(object):
    def __init__(self):
        handler_out = logging.StreamHandler(sys.stdout)
        handler_err = logging.StreamHandler(sys.stderr)

        handler_out.addFilter(SingleLevelFilter(logging.INFO, False))
        handler_err.addFilter(SingleLevelFilter(logging.INFO, True))

        self.log = logging.getLogger()
        formatter = logging.Formatter(
            "%(asctime)s %(filename)s:%(lineno)-10s %(levelname)-5s %(message)s"
        )

        handler_out.setFormatter(formatter)
        handler_err.setFormatter(formatter)

        self.log.addHandler(handler_out)
        self.log.addHandler(handler_err)

    def run(self):
        parser = MyParser(description="Count image layers to squash")
        
        parser.add_argument("image", help="Image to be squashed")
        
        parser.add_argument(
            "-c",
            "--squash-cfg",
            default="default-squash.cfg",
            help="Comma-separated list of keys to filter.",
        )
        
        
        args = parser.parse_args()
            
        self.log.info("use squash-cfg ==> "+  args.squash_cfg )
        squashkeys = read_file_lines(args.squash_cfg)
        self.log.info("squash keys nums : "+str(len(squashkeys)))
        
        self.docker= common.docker_client(self.log)
        
        self.log.info("squash keys : ["+" ** ".join(squashkeys)+"]")
        
        count =0
        for i, layer in  enumerate(self.docker.history(args.image)): #dict_keys(['Comment', 'Created', 'CreatedBy', 'Id', 'Size', 'Tags'])
            created_by = layer["CreatedBy"]
            comment = layer["Comment"]
            check_flag = False
            if created_by != "":
                for  k in squashkeys:
                    if k in created_by:
                        check_flag =True
                        self.last_created_by = created_by
                        break
                    
            if comment == "build img" :
                check_flag =True            
            elif created_by=="" and comment=="":
                check_flag =True
                
            if check_flag and count ==i:
                count +=1
            else:
                break
        
        print(count)
            
        
         
            
def read_file_lines(file_path):
    """
    读取文件的每一行并返回一个字符串列表
    """
    if not os.path.exists(file_path):
        # 如果文件不存在，返回空列表
        return []
    
    lines = []
    try:
        with open(file_path, 'r') as file:
            lines = file.readlines()
            # 去掉每行的换行符
            lines = [line.rstrip('\n') for line in lines]
    except Exception as e:
        # 处理其他错误，打印错误信息
        print(f"Error reading file: {e}")
        return None
    
    return lines


def run():
    cli = CLI()
    cli.run()


if __name__ == "__main__":
    run()
