#FROM golang:1.19 as builder
FROM registry.cnbita.com:5000/golangci/golang:1.20 as builder

WORKDIR /work

RUN go env -w GOPROXY=https://goproxy.cn,direct && go env -w CGO_ENABLED=0
COPY go.mod ./
RUN go mod download

COPY . ./
RUN go build -o initconfig main.go


#FROM debian
FROM registry.cnbita.com:5000/golangci/alpine:3.14
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
WORKDIR /config
RUN chmod 777 -R /config

COPY --from=builder /work/initconfig ./
COPY ./arm64/supervisord ./

RUN chmod +x ./supervisord  
