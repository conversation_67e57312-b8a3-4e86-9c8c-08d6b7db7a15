package main

import (
	"bufio"
	"flag"
	"fmt"
	"os"
)

var (
	ssh        = flag.Bool("ssh", false, "")
	gotty      = flag.Bool("web-terminal", false, "")
	vscode     = flag.Bool("vscode", false, "")
	jupyterlab = flag.Bool("jupyter", false, "")
	vnc        = flag.Bool("vnc", false, "")
)

var (
	sshCmd        = "service ssh start"
	gottyCmd      = "/app/gotty/gotty --address 0.0.0.0 --port 8083 --ws-origin '.*' --permit-write --reconnect /bin/bash"
	vscodeCmd     = "/home/<USER>/code-server --auth none --bind-addr 0.0.0.0:8082"
	jupyterlabCmd = "jupyter lab --no-browser --ip=0.0.0.0 --allow-root --notebook-dir='/' --port=8081 --NotebookApp.token='' --LabApp.base_url="
	vncCmd        = "bash /app/vnc/vncstart.sh"
)

func init() {
	flag.Parse()
}

func writeConfigFile() {
	filePath := "/config/supervisord.ini"
	file, err := os.OpenFile(filePath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0666)
	if err != nil {
		fmt.Println("Open file failed!", err)
	}
	defer file.Close()
	writer := bufio.NewWriter(file)
	if *ssh {
		writePlugin(writer, "ssh", sshCmd)
	}

	if *gotty {
		writePlugin(writer, "gotty", gottyCmd)
	}

	if *jupyterlab {
		if len(os.Getenv("JUPYTER_BASE_URL")) > 0 {
			jupyterlabCmd = jupyterlabCmd + os.Getenv("JUPYTER_BASE_URL")
		}
		writePlugin(writer, "jupyterlab", jupyterlabCmd)
	}

	if *vscode {
		writePlugin(writer, "vscode", vscodeCmd)
	}

	if *vnc {
		writePlugin(writer, "vnc", vncCmd)
	}

	writer.Flush()
}

func writePlugin(writer *bufio.Writer, plugin, cmd string) {
	programStr := fmt.Sprintf("[program:%s]\n", plugin)
	commandStr := fmt.Sprintf("command=%s\n", cmd)
	//logfileStr := fmt.Sprintf("stdout_logfile=/var/log/%ss.log\n", plugin)
	logfileStr := "stdout_logfile=/dev/stdout\n" //fmt.Sprintf("stdout_logfile=/var/log/%ss.log\n", plugin)
	errStr := "stderr_logfile=/dev/stderr\n"
	writer.WriteString(programStr)
	writer.WriteString(commandStr)
	writer.WriteString(logfileStr)
	writer.WriteString(errStr)
	writer.WriteString("stdout_logfile_maxbytes=100MB\n")
	writer.WriteString("stdout_logfile_backups=3\n")
}

func main() {
	writeConfigFile()
}
