# 全量的配置见： https://github.com/golangci/golangci-lint/blob/master/.golangci.reference.yml

# 定义运行时配置
run:
  # 并发检查器数量
  concurrency: 4
  # 退出状态码
  issues-exit-code: 1
  # 分析超时，防止长时间运行
  timeout: 5m
  # 包含测试文件
  tests: false


#默认配置
#deadcode: 发现没有使用的代码
#errcheck: 用于检查 go 程序中有 error 返回的函数，却没有做判断检查
#gosimple: 检测代码是否可以简化
#govet (vet, vetshadow): 检查 go 源代码并报告可疑结构，例如 Printf 调用，其参数与格式字符串不一致
#ineffassign: 检测是否有未使用的代码、变量、常量、类型、结构体、函数、函数参数等
#staticcheck: 提供了巨多的静态检查，检查 bug，分析性能等
#structcheck:发现未使用的结构体字段
#typecheck: 对 go 代码进行解析和类型检查
#unused: 检查未使用的常量，变量，函数和类型
#varcheck: 查找未使用的全局变量和常量
#bodyclose: 对 HTTP 响应是否 close 成功检测
#dupl: 代码克隆监测工具
#gochecknoglobals: 检查 go 代码中是否存在全局变量
#goimports: 做所有 gofmt 做的事. 此外还检查未使用的导入
#golint: 打印出 go 代码的格式错误
#gofmt: 检测代码是否都已经格式化, 默认情况下使用 -s 来检查代码是否简化
# 启用的 lint 工具
linters:
  disable-all: true
  enable:
#    - gofmt        # 代码格式化
#    - golint       # 代码规范检查
    - goimports    # 导入包管理
    - staticcheck  # 静态检查
    - ineffassign   # 检查无效的赋值
    - cyclop       # 计算圈复杂度
    - nestif       # 检查复杂的嵌套 if 语句
    - unused     # 检查未使用的变量
    - unconvert     # 检查不必要的类型转换


# 各 lint 工具的具体设置
linters-settings:
  exclude:
    - ^vendor/  # 排除 vendor 目录
    - ^tests/   # 排除 tests 目录
    - ^generated/  # 排除 generated 目录
  cyclop:
    # 函数最大复杂度=(函数本身是1 + 1 * ('if', 'for', 'case', 'select', '&&', '||'))
    # 1 - 10 程序简单，风险小
    # 11 - 20 更复杂，中等风险
    # 21 - 50 复杂、高风险
    # 50 不可测试的代码，非常高的风险
    max-complexity: 15
    skip-tests: true     # 忽略测试文件的复杂度

  nestif:
    min-complexity: 10  # 检查复杂度大于 5 的嵌套 if 语句

  goimports:
    local-prefixes: ""  # 替换为你的本地导入路径前缀

  gocyclo:
    min-complexity: 15  # 检查复杂度大于 15 的函数

  staticcheck:
    checks: ["all"]     # 启用所有检查
