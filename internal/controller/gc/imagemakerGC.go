package gc

import (
	"context"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type ImageMakerGarbageCollector struct {
	client       client.Client
	recycletime  int
	delayTime    int
	imagemakers  systemv1alpha1.ImageMakerList
	isRecycle    bool
	filterLabels map[string]string
}

func NewImageMakerGarbageCollector(client client.Client, resourceConfig config.ResourceRecycle) *ImageMakerGarbageCollector {
	return &ImageMakerGarbageCollector{
		client:       client,
		recycletime:  resourceConfig.Resource.Imagemaker.RecycletimeHour,
		delayTime:    resourceConfig.Resource.Imagemaker.DelayTimeHour,
		isRecycle:    resourceConfig.IsRecycle,
		filterLabels: resourceConfig.FilterLabels,
	}
}

func (imgc *ImageMakerGarbageCollector) GarbageCollector() {
	if !imgc.isRecycle {
		return
	}
	ticker := time.NewTicker(time.Duration(imgc.recycletime) * time.Hour)
	for range ticker.C {
		err := imgc.client.List(context.TODO(), &imgc.imagemakers)
		if err != nil {
			klog.Error(err, "get ImageMakers list failed.")
		}
		for _, im := range imgc.imagemakers.Items {

			for k, v := range imgc.filterLabels {
				if val, exists := im.Labels[k]; exists && val == v {
					continue
				}
			}

			if im.Status.StoppedTime.Add(time.Duration(imgc.delayTime) * time.Hour).After(time.Now()) {
				continue
			}
			if im.Status.State.Phase == systemv1alpha1.IMSucceeded ||
				im.Status.State.Phase == systemv1alpha1.IMFailed ||
				im.Status.State.Phase == systemv1alpha1.IMStopped {
				err := imgc.client.Delete(context.TODO(), &im)
				if err != nil {
					klog.Errorf("delete ImageMaker <%s> failed.", im.Name)
				}
			}
		}
	}
}
