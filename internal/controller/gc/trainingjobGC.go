package gc

import (
	"context"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type TrainingjobGarbageCollector struct {
	client       client.Client
	recycletime  int
	delayTime    int
	trainingjobs systemv1alpha1.TrainingJobList
	isRecycle    bool
	filterLabels map[string]string
}

func NewTrainingjobGarbageCollector(client client.Client, resourceConfig config.ResourceRecycle) *TrainingjobGarbageCollector {
	return &TrainingjobGarbageCollector{
		client:       client,
		recycletime:  resourceConfig.Resource.Trainingjob.RecycletimeHour,
		delayTime:    resourceConfig.Resource.Trainingjob.DelayTimeHour,
		isRecycle:    resourceConfig.IsRecycle,
		filterLabels: resourceConfig.FilterLabels,
	}
}

func (tjgc *TrainingjobGarbageCollector) GarbageCollector() {
	if !tjgc.isRecycle {
		return
	}
	ticker := time.NewTicker(time.Duration(tjgc.recycletime) * time.Hour)
	for range ticker.C {
		err := tjgc.client.List(context.TODO(), &tjgc.trainingjobs)
		if err != nil {
			klog.Error(err, "get TrainingJobs list failed.")
			return
		}
		for _, tj := range tjgc.trainingjobs.Items {

			for k, v := range tjgc.filterLabels {
				if val, exists := tj.Labels[k]; exists && val == v {
					continue
				}
			}

			if len(tj.Status.Conditions) == 0 || tj.Status.Conditions[len(tj.Status.Conditions)-1].LastTransitionTime.Add(time.Duration(tjgc.delayTime)*time.Hour).After(time.Now()) {
				continue
			}
			if tj.Status.State.Phase == systemv1alpha1.Failed ||
				tj.Status.State.Phase == systemv1alpha1.Completed ||
				tj.Status.State.Phase == systemv1alpha1.Stopped {
				err := tjgc.client.Delete(context.TODO(), &tj)
				if err != nil {
					klog.Errorf("delete TrainingJob <%s> failed.", tj.Name)
				}
			}
		}
	}
}
