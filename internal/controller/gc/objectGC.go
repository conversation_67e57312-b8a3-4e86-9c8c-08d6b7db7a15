package gc

import (
	"sync"
)

type GarbageCollector interface {
	GarbageCollector()
}

// func LoadGCConfig() {
// 	viper.SetConfigFile("/etc/recycleconfig/config.yaml")
// 	if err := viper.ReadInConfig(); err != nil {
// 		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
// 			klog.Error(err, "config file not found")
// 		}
// 		klog.Error(err, "config file was found but another error was produced")
// 	}
// }

type ObjectGarbageCollector struct {
	resources sync.Map
}

func (m *ObjectGarbageCollector) Register(name string, g GarbageCollector) {
	m.resources.Store(name, g)
}

func (m *ObjectGarbageCollector) StartGC() {
	m.resources.Range(func(key, value interface{}) bool {
		go value.(GarbageCollector).GarbageCollector()
		return true
	})
}
