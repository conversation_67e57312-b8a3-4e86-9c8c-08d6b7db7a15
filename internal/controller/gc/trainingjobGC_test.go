package gc

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"testing"
)

func TestNewTrainingjobGarbageCollector(t *testing.T) {
	// 设置scheme
	s := scheme.Scheme
	_ = systemv1alpha1.AddToScheme(s)

	// 创建假的客户端
	k8sFakeClient := fake.NewClientBuilder().WithScheme(s).Build()

	// 创建资源配置
	resourceConfig := config.ResourceRecycle{
		IsRecycle: true,
		Resource: struct {
			SyncAction struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Syncaction"`
			Trainingjob struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Trainingjob"`
			Notebook struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Notebook"`
			Imagemaker struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Imagemaker"`
			Tensorboard struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Tensorboard"`
		}{
			Trainingjob: struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			}{
				RecycletimeHour: 1,
				DelayTimeHour:   2,
			},
		},
	}

	tjgc := NewTrainingjobGarbageCollector(k8sFakeClient, resourceConfig)

	// 验证字段是否被正确设置
	if tjgc.client != k8sFakeClient {
		t.Errorf("expected client to be set, got %v", tjgc.client)
	}
	if tjgc.recycletime != resourceConfig.Resource.Trainingjob.RecycletimeHour {
		t.Errorf("expected recycletime to be %d, got %d", resourceConfig.Resource.Trainingjob.RecycletimeHour, tjgc.recycletime)
	}
	if tjgc.delayTime != resourceConfig.Resource.Trainingjob.DelayTimeHour {
		t.Errorf("expected delayTime to be %d, got %d", resourceConfig.Resource.Trainingjob.DelayTimeHour, tjgc.delayTime)
	}
	if tjgc.isRecycle != resourceConfig.IsRecycle {
		t.Errorf("expected isRecycle to be %v, got %v", resourceConfig.IsRecycle, tjgc.isRecycle)
	}
}
