package gc

import (
	"context"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type SyncactionGarbageCollector struct {
	syncactions  systemv1alpha1.SyncActionList
	isRecycle    bool
	filterLabels map[string]string
	recycletime  int
	delayTime    int
	client       client.Client
}

func NewSyncactionGarbageCollector(client client.Client, resourceConfig config.ResourceRecycle) *SyncactionGarbageCollector {
	return &SyncactionGarbageCollector{
		client:       client,
		recycletime:  resourceConfig.Resource.SyncAction.RecycletimeHour,
		delayTime:    resourceConfig.Resource.SyncAction.DelayTimeHour,
		isRecycle:    resourceConfig.IsRecycle,
		filterLabels: resourceConfig.FilterLabels,
	}
}

func (sjgc *SyncactionGarbageCollector) GarbageCollector() {
	if !sjgc.isRecycle {
		return
	}
	ticker := time.NewTicker(time.Duration(sjgc.recycletime) * time.Hour)
	for range ticker.C {
		err := sjgc.client.List(context.TODO(), &sjgc.syncactions)
		if err != nil {
			klog.Error(err, "get SyncActions list failed.")
			return
		}
		for _, sa := range sjgc.syncactions.Items {
			for k, v := range sjgc.filterLabels {
				if val, exists := sa.Labels[k]; exists && val == v {
					continue
				}
			}

			if sa.CreationTimestamp.Add(time.Duration(sjgc.delayTime) * time.Hour).After(time.Now()) {
				continue
			}

			if sa.Status.State.Phase == systemv1alpha1.SyncSuccess || sa.Status.State.Phase == systemv1alpha1.SyncFailed {
				err := sjgc.client.Delete(context.TODO(), &sa)
				if err != nil {
					klog.Errorf("delete TrainingJob <%s> failed.", sa.Name)
				}
			}
		}
	}
}
