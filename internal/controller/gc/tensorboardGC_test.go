package gc

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"testing"
)

func TestNewTensorboardGarbageCollector(t *testing.T) {
	// 设置scheme
	s := scheme.Scheme
	_ = systemv1alpha1.AddToScheme(s)

	// 创建假的客户端
	k8sFakeClient := fake.NewClientBuilder().WithScheme(s).Build()

	// 创建资源配置
	resourceConfig := config.ResourceRecycle{
		IsRecycle: true,
		Resource: struct {
			SyncAction struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Syncaction"`
			Trainingjob struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Trainingjob"`
			Notebook struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Notebook"`
			Imagemaker struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Imagemaker"`
			Tensorboard struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Tensorboard"`
		}{
			Tensorboard: struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			}{
				RecycletimeHour: 1,
				DelayTimeHour:   2,
			},
		},
	}

	tbgc := NewTensorboardGarbageCollector(k8sFakeClient, resourceConfig)

	// 验证字段是否被正确设置
	if tbgc.client != k8sFakeClient {
		t.Errorf("expected client to be set, got %v", tbgc.client)
	}
	if tbgc.recycletime != resourceConfig.Resource.Tensorboard.RecycletimeHour {
		t.Errorf("expected recycletime to be %d, got %d", resourceConfig.Resource.Tensorboard.RecycletimeHour, tbgc.recycletime)
	}
	if tbgc.delayTime != resourceConfig.Resource.Tensorboard.DelayTimeHour {
		t.Errorf("expected delayTime to be %d, got %d", resourceConfig.Resource.Tensorboard.DelayTimeHour, tbgc.delayTime)
	}
	if tbgc.isRecycle != resourceConfig.IsRecycle {
		t.Errorf("expected isRecycle to be %v, got %v", resourceConfig.IsRecycle, tbgc.isRecycle)
	}
}
