package gc

import (
	"testing"
	"time"

	"k8s.io/klog/v2"
)

// MockGarbageCollector 是一个模拟的垃圾收集器实现，用于测试
type MockGarbageCollector struct {
	name string
}

func (m *MockGarbageCollector) GarbageCollector() {
	// 模拟一些工作
	time.Sleep(100 * time.Millisecond)
	klog.Infof("Garbage collection executed for %s", m.name)
}

func TestObjectGarbageCollector(t *testing.T) {
	ogc := &ObjectGarbageCollector{}

	// 注册模拟的垃圾收集器
	mockGC1 := &MockGarbageCollector{name: "Mock1"}
	mockGC2 := &MockGarbageCollector{name: "Mock2"}

	ogc.Register("mock1", mockGC1)
	ogc.Register("mock2", mockGC2)

	// 启动垃圾收集器
	ogc.StartGC()

	// 等待足够的时间以确保所有 goroutine 完成
	time.Sleep(200 * time.Millisecond)

	// 验证是否注册成功
	var count int
	ogc.resources.Range(func(key, value interface{}) bool {
		count++ // 增加计数
		return true
	})

	// 验证注册的数量
	if count != 2 {
		t.<PERSON><PERSON><PERSON>("expected 2 registered garbage collectors, got %d", count)
	}
}
