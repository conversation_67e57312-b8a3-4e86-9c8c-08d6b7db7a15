package gc

import (
	"context"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type TensorboardGarbageCollector struct {
	client       client.Client
	recycletime  int
	delayTime    int
	tensorboards systemv1alpha1.TensorboardList
	isRecycle    bool
	filterLabels map[string]string
}

func NewTensorboardGarbageCollector(client client.Client, resourceConfig config.ResourceRecycle) *TensorboardGarbageCollector {
	return &TensorboardGarbageCollector{
		client:       client,
		recycletime:  resourceConfig.Resource.Tensorboard.RecycletimeHour,
		delayTime:    resourceConfig.Resource.Tensorboard.DelayTimeHour,
		isRecycle:    resourceConfig.IsRecycle,
		filterLabels: resourceConfig.FilterLabels,
	}
}

func (tbgc *TensorboardGarbageCollector) GarbageCollector() {
	if !tbgc.isRecycle {
		return
	}
	ticker := time.NewTicker(time.Duration(tbgc.recycletime) * time.Hour)
	for range ticker.C {
		err := tbgc.client.List(context.TODO(), &tbgc.tensorboards)
		if err != nil {
			klog.Error(err, "get tensorboard list failed.")
			return
		}
		for _, tb := range tbgc.tensorboards.Items {

			for k, v := range tbgc.filterLabels {
				if val, exists := tb.Labels[k]; exists && val == v {
					continue
				}
			}

			if tb.Status.StoppedTime.Add(time.Duration(tbgc.delayTime) * time.Hour).After(time.Now()) {
				continue
			}
			if tb.Status.State.Phase != systemv1alpha1.TBStopped {
				continue
			}
			err := tbgc.client.Delete(context.TODO(), &tb)
			if err != nil {
				klog.Errorf("delete Tensorboard <%s> failed.", tb.Name)
			}
		}
	}
}
