package gc

import (
	"context"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type NotebookGarbageCollector struct {
	client       client.Client
	recycletime  int
	delayTime    int
	notebooks    systemv1alpha1.NotebookList
	isRecycle    bool
	filterLabels map[string]string
}

func NewNotebookGarbageCollector(client client.Client, resourceConfig config.ResourceRecycle) *NotebookGarbageCollector {
	return &NotebookGarbageCollector{
		client:       client,
		recycletime:  resourceConfig.Resource.Notebook.RecycletimeHour,
		delayTime:    resourceConfig.Resource.Notebook.DelayTimeHour,
		isRecycle:    resourceConfig.IsRecycle,
		filterLabels: resourceConfig.FilterLabels,
	}
}

func (nbgc *NotebookGarbageCollector) GarbageCollector() {
	if !nbgc.isRecycle {
		return
	}
	ticker := time.NewTicker(time.Duration(nbgc.recycletime) * time.Hour)
	for range ticker.C {
		err := nbgc.client.List(context.TODO(), &nbgc.notebooks)
		if err != nil {
			klog.Error(err, "get Notebook list failed.")
			return
		}
		for _, nb := range nbgc.notebooks.Items {

			for k, v := range nbgc.filterLabels {
				if val, exists := nb.Labels[k]; exists && val == v {
					continue
				}
			}

			if len(nb.Status.Conditions) == 0 || nb.Status.Conditions[len(nb.Status.Conditions)-1].LastTransitionTime.Add(time.Duration(nbgc.delayTime)*time.Hour).After(time.Now()) {
				continue
			}
			if nb.Status.State == systemv1alpha1.NotebookStateStartFailed || nb.Status.State == systemv1alpha1.NotebookStateStopped {
				err := nbgc.client.Delete(context.TODO(), &nb)
				if err != nil {
					klog.Errorf("delete Notebook <%s> failed.", nb.Name)
				}
			}
		}
	}
}
