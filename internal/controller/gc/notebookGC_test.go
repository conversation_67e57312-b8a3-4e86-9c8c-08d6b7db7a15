package gc

import (
	"hero.ai/hero-controllers/internal/controller/config"
	"k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"testing"
)

func TestNewNotebookGarbageCollector(t *testing.T) {
	// 创建一个假的客户端
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme.Scheme).Build()

	// 模拟配置
	resourceConfig := config.ResourceRecycle{
		IsRecycle: true,
		Resource: struct {
			SyncAction struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Syncaction"`
			Trainingjob struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Trainingjob"`
			Notebook struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Notebook"`
			Imagemaker struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Imagemaker"`
			Tensorboard struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			} `yaml:"Tensorboard"`
		}{
			Notebook: struct {
				RecycletimeHour int `yaml:"RecycletimeHour"`
				DelayTimeHour   int `yaml:"DelayTimeHour"`
			}{
				RecycletimeHour: 1,
				DelayTimeHour:   2,
			},
		},
	}

	// 创建NotebookGarbageCollector实例
	nbgc := NewNotebookGarbageCollector(k8sFakeClient, resourceConfig)

	// 验证字段初始化
	if nbgc.client != k8sFakeClient {
		t.Errorf("expected client to be initialized")
	}
	if nbgc.recycletime != 1 {
		t.Errorf("expected recycletime to be 1, got %d", nbgc.recycletime)
	}
	if nbgc.delayTime != 2 {
		t.Errorf("expected delayTime to be 2, got %d", nbgc.delayTime)
	}
	if nbgc.isRecycle != true {
		t.Errorf("expected isRecycle to be true")
	}
}
