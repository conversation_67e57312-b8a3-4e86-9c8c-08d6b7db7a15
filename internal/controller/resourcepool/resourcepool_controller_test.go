package resourcepool

import (
	"context"
	"hero.ai/hero-controllers/internal/controller/resourcepool/state"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func TestResourcePoolReconciler_Reconcile(t *testing.T) {
	// 创建测试用的假客户端
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// 创建一个 ResourcePool 对象进行测试
	resourcePool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:       "test-resourcepool",
			Finalizers: []string{myFinalizerName},
		},
		Status: systemv1alpha1.ResourcePoolStatus{
			Nodes: []string{"node1"},
		},
	}

	// 将 ResourcePool 对象添加到假客户端
	err := r.Create(context.TODO(), resourcePool)
	assert.NoError(t, err)

	// Set the deletion timestamp to simulate deletion
	now := metav1.Now()
	resourcePool.DeletionTimestamp = &now

	// Update the ResourcePool object in the fake client
	err = r.Update(context.TODO(), resourcePool)
	assert.NoError(t, err)

	// 测试 Reconcile
	req := ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name: "test-resourcepool",
		},
	}

	// Mock implementation for CreateResourcePool
	state.CreateResourcePool = func(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool) error {
		if resourcePool == nil {
			resourcepool = resourcePool
		}

		return nil
	}

	result, err := r.Reconcile(context.TODO(), req)
	assert.NoError(t, err)
	assert.Equal(t, ctrl.Result{}, result)

	resPool := systemv1alpha1.ResourcePool{}
	errGet := r.Get(context.TODO(), req.NamespacedName, &resPool)
	assert.Error(t, errGet)
}

func TestResourcePoolReconciler_DeleteResourcePool(t *testing.T) {
	// Create a fake client for testing
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a ResourcePool object for testing
	resourcePool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:       "test-resourcepool",
			Finalizers: []string{myFinalizerName},
		},
		Status: systemv1alpha1.ResourcePoolStatus{
			Nodes: []string{"node1"},
		},
	}

	// Add the ResourcePool object to the fake client
	err := r.Create(context.TODO(), resourcePool)
	assert.NoError(t, err)

	// Set the deletion timestamp to simulate deletion
	now := metav1.Now()
	resourcePool.DeletionTimestamp = &now

	// Call the deleteResourcePool method
	err = r.deleteResourcePool(context.TODO(), resourcePool)
	assert.NoError(t, err)

	// Verify if the finalizer has been removed
	assert.NotContains(t, resourcePool.ObjectMeta.Finalizers, myFinalizerName)

	// Verify ResourcePool is still in the client because fake client does not simulate deletion automatically
	err = r.Get(context.TODO(), types.NamespacedName{Name: "test-resourcepool"}, resourcePool)
	assert.NoError(t, err) // The ResourcePool should still exist in the fake client
}

func TestResourcePoolReconciler_Initialize(t *testing.T) {
	r := &ResourcePoolReconciler{}
	r.Initialize()

	assert.NotNil(t, r.nodeResMap)
	assert.NotNil(t, r.nodeLabelMap)
	assert.NotNil(t, r.jobMap)
	assert.NotNil(t, r.notebookMap)
	assert.NotNil(t, r.podMap)
}
