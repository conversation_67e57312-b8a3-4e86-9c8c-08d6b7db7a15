package resourcepool

import (
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/util/workqueue"
	"sigs.k8s.io/controller-runtime/pkg/event"
)

func TestNodeEnqueueRequestForExtendRes_Create(t *testing.T) {
	nodeName := "test-node"
	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   nodeName,
			Labels: map[string]string{systemv1alpha1.NodeLabelKey: "special-node"},
		},
		Status: v1.NodeStatus{
			Capacity: v1.ResourceList{
				v1.ResourceCPU:    resource.MustParse("4"),
				v1.ResourceMemory: resource.MustParse("16Gi"),
			},
		},
	}

	e := &nodeEnqueueRequestForExtendRes{
		nodeResMap:   make(map[string]*v1.ResourceList),
		nodeLabelMap: make(map[string]map[string]struct{}),
		nodeMutex:    &sync.RWMutex{},
	}

	e.Create(event.CreateEvent{Object: node}, workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter()))

	e.nodeMutex.RLock()
	defer e.nodeMutex.RUnlock()

	// 确认节点资源已添加
	res, exists := e.nodeResMap[nodeName]
	assert.True(t, exists)
	assert.Equal(t, resource.MustParse("4"), (*res)[v1.ResourceCPU])
	assert.Equal(t, resource.MustParse("16Gi"), (*res)[v1.ResourceMemory])

	// 确认节点标签已添加
	labelMap, exists := e.nodeLabelMap["special-node"]
	assert.True(t, exists)
	assert.Contains(t, labelMap, nodeName)
}

func TestNodeEnqueueRequestForExtendRes_Update(t *testing.T) {
	nodeName := "test-node"
	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   nodeName,
			Labels: map[string]string{systemv1alpha1.NodeLabelKey: "special-node"},
		},
		Status: v1.NodeStatus{
			Capacity: v1.ResourceList{
				v1.ResourceCPU:    resource.MustParse("4"),
				v1.ResourceMemory: resource.MustParse("16Gi"),
			},
		},
	}

	e := &nodeEnqueueRequestForExtendRes{
		nodeResMap:   make(map[string]*v1.ResourceList),
		nodeLabelMap: make(map[string]map[string]struct{}),
		nodeMutex:    &sync.RWMutex{},
	}

	e.Create(event.CreateEvent{Object: node}, workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter()))

	// 更新节点资源
	node.Status.Capacity[v1.ResourceCPU] = resource.MustParse("8")
	e.Update(event.UpdateEvent{ObjectNew: node, ObjectOld: node}, workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter()))

	e.nodeMutex.RLock()
	defer e.nodeMutex.RUnlock()

	// 确认节点资源已更新
	res, exists := e.nodeResMap[nodeName]
	assert.True(t, exists)
	assert.Equal(t, resource.MustParse("8"), (*res)[v1.ResourceCPU])
	assert.Equal(t, resource.MustParse("16Gi"), (*res)[v1.ResourceMemory])
}

func TestNodeEnqueueRequestForExtendRes_Delete(t *testing.T) {
	nodeName := "test-node"
	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   nodeName,
			Labels: map[string]string{systemv1alpha1.NodeLabelKey: "special-node"},
		},
	}

	e := &nodeEnqueueRequestForExtendRes{
		nodeResMap:   make(map[string]*v1.ResourceList),
		nodeLabelMap: make(map[string]map[string]struct{}),
		nodeMutex:    &sync.RWMutex{},
	}

	// 模拟创建节点
	e.Create(event.CreateEvent{Object: node}, workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter()))

	// 删除节点，源码代码直接返回，删除没有起效果
	e.Delete(event.DeleteEvent{Object: node}, workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter()))

	e.nodeMutex.RLock()
	defer e.nodeMutex.RUnlock()

	// 因删除没有起效果，确认节点还在缓存中
	_, exists := e.nodeResMap[nodeName]
	assert.True(t, exists)
}

func TestNodeEnqueueRequestForExtendRes_Generic(t *testing.T) {
	e := &nodeEnqueueRequestForExtendRes{
		nodeResMap:   make(map[string]*v1.ResourceList),
		nodeLabelMap: make(map[string]map[string]struct{}),
		nodeMutex:    &sync.RWMutex{},
	}

	// 测试 GenericEvent
	e.Generic(event.GenericEvent{Object: nil}, workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter()))
	// 预期无错误，无需进一步检查
}
