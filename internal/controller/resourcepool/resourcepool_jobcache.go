package resourcepool

import (
	"sync"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

type jobRequestForExtendRes struct {
	jobMap   map[string]map[string]struct{}
	jobMutex *sync.RWMutex
}

// 转换，需要换成Resourcepool添加到队列,作业事件需要Reconcile
func (e *jobRequestForExtendRes) Create(evt event.CreateEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "CreateEvent received with no metadata", "event", evt)
		return
	}

	if _, found := evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]; !found {
		return
	}

	e.jobMutex.Lock()
	defer e.jobMutex.Unlock()
	if _, ok := e.jobMap[evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]]; !ok {
		e.jobMap[evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]] = map[string]struct{}{}
	}

	e.jobMap[evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]][evt.Object.GetNamespace()+"/"+evt.Object.GetName()] = struct{}{}
	q.Add(reconcile.Request{NamespacedName: types.NamespacedName{
		Name: evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey],
	}})
}

// Update implements EventHandler.
func (e *jobRequestForExtendRes) Update(evt event.UpdateEvent, q workqueue.RateLimitingInterface) {
	e.Create(event.CreateEvent{
		Object: evt.ObjectNew,
	}, q)
}

// Delete implements EventHandler.
func (e *jobRequestForExtendRes) Delete(evt event.DeleteEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "DeleteEvent received with no metadata", "event", evt)
		return
	}

	if _, found := evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]; !found {
		return
	}

	e.jobMutex.Lock()
	defer e.jobMutex.Unlock()
	if _, ok := e.jobMap[evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]]; ok {
		delete(e.jobMap[evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]], evt.Object.GetNamespace()+"/"+evt.Object.GetName())
	}

	q.Add(reconcile.Request{NamespacedName: types.NamespacedName{
		Name: evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey],
	}})
}

// Generic implements EventHandler.
func (e *jobRequestForExtendRes) Generic(evt event.GenericEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "GenericEvent received with no metadata", "event", evt)
		return
	}
}
