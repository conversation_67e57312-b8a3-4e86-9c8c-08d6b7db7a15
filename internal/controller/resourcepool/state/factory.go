package state

import (
	"context"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

type State interface {
	Execute(ctx context.Context) error
}

type ActionFn func(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool) error

var (
	SyncResourcePool   ActionFn
	CreateResourcePool ActionFn
)

func NewState(resourcepool *systemv1alpha1.ResourcePool) State {
	switch resourcepool.Status.State {
	case "":
		return &creatingState{
			resourcepool: resourcepool,
		}
	default:
		return &openState{
			resourcepool: resourcepool,
		}
	}
}
