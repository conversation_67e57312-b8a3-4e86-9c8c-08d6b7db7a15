package resourcepool

import (
	"context"
	"k8s.io/apimachinery/pkg/api/resource"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/resourcepool/api"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	commandvc "volcano.sh/apis/pkg/apis/bus/v1alpha1"
	schedV1beat1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

func TestCreateResourcePool(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = schedV1beat1.AddToScheme(scheme)
	_ = commandvc.AddToScheme(scheme)

	// Initialize fake client
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-pool",
		},
		Spec: systemv1alpha1.ResourcePoolSpec{
			Description: "test-resource-pool",
		},
	}
	err := k8sFakeClient.Create(context.TODO(), resourcepool)
	assert.NoError(t, err)

	// 模拟创建 Queue
	queue := &schedV1beat1.Queue{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Queue",
			APIVersion: "scheduling.volcano.sh/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: resourcepool.Name,
			// Namespace 不设置，确保是集群范围
		},
		Spec: schedV1beat1.QueueSpec{
			Capability: api.EmptyResource().ResourceList(), // 确保能力为空
		},
	}
	// 手动创建 Queue 并添加到 k8sFakeClient 中
	err = k8sFakeClient.Create(context.TODO(), queue)
	assert.NoError(t, err)

	// Test creating a resource pool
	err = reconciler.createResourcePool(context.TODO(), resourcepool)
	assert.NoError(t, err)

	// Check if the resource pool was created
	fetchedPool := &systemv1alpha1.ResourcePool{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: resourcepool.Name, Namespace: resourcepool.Namespace}, fetchedPool)
	assert.NoError(t, err)

	// 检查状态和 finalizer
	assert.Equal(t, fetchedPool.Status.State, systemv1alpha1.ResourcePoolStateOpen)
	assert.Contains(t, fetchedPool.ObjectMeta.Finalizers, myFinalizerName)

	// 再次获取资源池，验证 Queue 是否正确关联
	fetchedQueue := &schedV1beat1.Queue{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: queue.Name}, fetchedQueue)
	assert.NoError(t, err)
	assert.Equal(t, fetchedQueue.Name, queue.Name)
}

func TestUpdateResourcePoolStatus(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	// Initialize fake client
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-pool",
		},
		Spec: systemv1alpha1.ResourcePoolSpec{
			Description: "test-resource-pool",
		},
	}
	err := k8sFakeClient.Create(context.TODO(), resourcepool)
	assert.NoError(t, err)

	_ = reconciler.createResourcePool(context.TODO(), resourcepool)

	// Update resource pool status
	resourcepool.Status.State = systemv1alpha1.ResourcePoolStateClosed
	err = reconciler.UpdateResourcePoolStatus(context.TODO(), resourcepool)
	assert.NoError(t, err)

	// Verify the status update
	fetchedPool := &systemv1alpha1.ResourcePool{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: resourcepool.Name, Namespace: resourcepool.Namespace}, fetchedPool)
	assert.NoError(t, err)
	assert.Equal(t, fetchedPool.Status.State, systemv1alpha1.ResourcePoolStateClosed)
}

func TestCalcJobResource(t *testing.T) {
	reconciler := &ResourcePoolReconciler{}

	// Create a sample job with multiple tasks
	job := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{
					Replicas: 2,
					Resource: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("500m"),
						v1.ResourceMemory: resource.MustParse("512Mi"),
					},
				},
				{
					Replicas: 1,
					Resource: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("1000m"),
						v1.ResourceMemory: resource.MustParse("1Gi"),
					},
				},
			},
		},
	}

	// Calculate job resources
	resource := reconciler.calcJobResource(job)
	assert.Equal(t, 2000.0, resource.MilliCPU, "MilliCPU should be equal to 2000 milliCPU")
	assert.Equal(t, 2.147483648e+09, resource.Memory, "Memory should be equal to 2 GiB")
}

func TestCalcPodTotalResource(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = v1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	pod := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
		},
		Spec: v1.PodSpec{
			Containers: []v1.Container{
				{
					Name: "test-container",
					Resources: v1.ResourceRequirements{
						Requests: v1.ResourceList{
							v1.ResourceCPU:    resource.MustParse("500m"),
							v1.ResourceMemory: resource.MustParse("256Mi"),
						},
					},
				},
			},
			NodeName: "test-node",
		},
		Status: v1.PodStatus{
			Phase: v1.PodRunning,
		},
	}

	// Create the pod
	err := k8sFakeClient.Create(context.TODO(), pod)
	assert.NoError(t, err)

	pods := []string{"default/test-pod"}
	totalResource := reconciler.calcPodTotalResource(context.TODO(), pods)

	assert.NotEmpty(t, totalResource)
	assert.True(t, totalResource[pod.Spec.NodeName][v1.ResourceCPU].Equal(resource.MustParse("500m")))
	assert.True(t, totalResource[pod.Spec.NodeName][v1.ResourceMemory].Equal(resource.MustParse("256Mi")))
}

func TestGetJobs(t *testing.T) {
	reconciler := &ResourcePoolReconciler{
		jobMap: make(map[string]map[string]struct{}),
	}

	reconciler.jobMap["test-pool"] = map[string]struct{}{
		"default/job1": {},
		"default/job2": {},
	}

	jobs := reconciler.getJobs("test-pool")
	assert.ElementsMatch(t, jobs, []string{"default/job1", "default/job2"})
}

func TestGetPods(t *testing.T) {
	reconciler := &ResourcePoolReconciler{
		podMap: make(map[string]map[string]struct{}),
	}

	reconciler.podMap["test-pool"] = map[string]struct{}{
		"default/pod1": {},
		"default/pod2": {},
	}

	pods := reconciler.getPods("test-pool")
	assert.ElementsMatch(t, pods, []string{"default/pod1", "default/pod2"})
}

func TestGetNotebookJobs(t *testing.T) {
	reconciler := &ResourcePoolReconciler{
		notebookMap: make(map[string]map[string]struct{}),
	}

	reconciler.notebookMap["test-pool"] = map[string]struct{}{
		"default/notebook1": {},
		"default/notebook2": {},
	}

	notebooks := reconciler.getNotebookJobs("test-pool")
	assert.ElementsMatch(t, notebooks, []string{"default/notebook1", "default/notebook2"})
}

func TestGetQueueCapResource(t *testing.T) {
	reconciler := &ResourcePoolReconciler{
		nodeResMap: make(map[string]*v1.ResourceList),
	}

	reconciler.nodeResMap["node1"] = &v1.ResourceList{
		v1.ResourceCPU:    resource.MustParse("2"),
		v1.ResourceMemory: resource.MustParse("4Gi"),
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		Status: systemv1alpha1.ResourcePoolStatus{
			Nodes: []string{"node1"},
		},
	}

	totalResource, resourceMap := reconciler.getQueueCapResource(resourcepool)
	assert.Equal(t, 2000.0, totalResource.MilliCPU, "MilliCPU should be equal to 2 milliCPU")
	assert.True(t, resourceMap["node1"][v1.ResourceMemory].Equal(resource.MustParse("4Gi")))
}

func TestDeleteResourcePool(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:       "test-pool",
			Namespace:  "default",
			Finalizers: []string{myFinalizerName},
		},
	}

	_ = reconciler.createResourcePool(context.TODO(), resourcepool)

	// Delete the resource pool
	err := reconciler.deleteResourcePool(context.TODO(), resourcepool)
	assert.NoError(t, err)

	// Verify the resource pool was deleted
	fetchedPool := &systemv1alpha1.ResourcePool{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: resourcepool.Name, Namespace: resourcepool.Namespace}, fetchedPool)
	assert.Error(t, err)
}

func TestSyncResourcePool(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = schedV1beat1.AddToScheme(scheme)
	_ = commandvc.AddToScheme(scheme)
	_ = v1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-pool",
		},
		Spec: systemv1alpha1.ResourcePoolSpec{
			Description: "test-resource-pool",
		},
		Status: systemv1alpha1.ResourcePoolStatus{
			State: systemv1alpha1.ResourcePoolStateOpen,
			Nodes: []string{"test-node"},
		},
	}

	err := k8sFakeClient.Create(context.TODO(), resourcepool)
	assert.NoError(t, err)

	nodeRes := api.NewResource(v1.ResourceList{
		v1.ResourceCPU:    resource.MustParse("2"),
		v1.ResourceMemory: resource.MustParse("4Gi"),
	})
	resList := nodeRes.ResourceList()
	reconciler.nodeResMap = make(map[string]*v1.ResourceList)
	reconciler.nodeResMap["test-node"] = &resList

	queue := &schedV1beat1.Queue{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Queue",
			APIVersion: "scheduling.volcano.sh/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: resourcepool.Name,
		},
		Spec: schedV1beat1.QueueSpec{
			Capability: nodeRes.ResourceList(),
		},
		Status: schedV1beat1.QueueStatus{
			State: schedV1beat1.QueueStateOpen,
		},
	}
	err = k8sFakeClient.Create(context.TODO(), queue)
	assert.NoError(t, err)

	err = reconciler.syncResourcePool(context.TODO(), resourcepool)
	assert.NoError(t, err)

	fetchedPool := &systemv1alpha1.ResourcePool{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: resourcepool.Name}, fetchedPool)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.ResourcePoolStateOpen, fetchedPool.Status.State)
}

func TestInitResourcePool(t *testing.T) {
	reconciler := &ResourcePoolReconciler{}

	resourcepool := &systemv1alpha1.ResourcePool{
		Status: systemv1alpha1.ResourcePoolStatus{
			Nodes: []string{"test-node"},
		},
	}

	nodeRes := api.NewResource(v1.ResourceList{
		v1.ResourceCPU:    resource.MustParse("2"),
		v1.ResourceMemory: resource.MustParse("4Gi"),
	})

	status := reconciler.initResourcePool(resourcepool, nodeRes)

	assert.Equal(t, nodeRes.ResourceList(), status.Idle)
}

func TestUpdateResourcePool(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-pool",
		},
		Spec: systemv1alpha1.ResourcePoolSpec{
			Description: "test-resource-pool",
		},
	}

	err := k8sFakeClient.Create(context.TODO(), resourcepool)
	assert.NoError(t, err)

	err = reconciler.UpdateResourcePool(context.TODO(), resourcepool)
	assert.NoError(t, err)

	fetchedPool := &systemv1alpha1.ResourcePool{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: resourcepool.Name}, fetchedPool)
	assert.NoError(t, err)
	assert.Equal(t, resourcepool.ObjectMeta.Finalizers, fetchedPool.ObjectMeta.Finalizers)
}

func TestCreateOrUpdateQueue(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = schedV1beat1.AddToScheme(scheme)
	_ = commandvc.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-pool",
		},
		Spec: systemv1alpha1.ResourcePoolSpec{
			Description: "test-resource-pool",
		},
		Status: systemv1alpha1.ResourcePoolStatus{
			Nodes: []string{"test-node"},
		},
	}

	err := k8sFakeClient.Create(context.TODO(), resourcepool)
	assert.NoError(t, err)

	nodeRes := api.NewResource(v1.ResourceList{
		v1.ResourceCPU:    resource.MustParse("2"),
		v1.ResourceMemory: resource.MustParse("4Gi"),
	})
	resList := nodeRes.ResourceList()
	reconciler.nodeResMap = make(map[string]*v1.ResourceList)
	reconciler.nodeResMap["test-node"] = &resList

	queue, err := reconciler.createOrUpdateQueue(context.TODO(), resourcepool, nodeRes)
	assert.NoError(t, err)
	assert.Equal(t, resourcepool.Name, queue.Name)
	assert.Equal(t, nodeRes.ResourceList(), queue.Spec.Capability)
}

func TestUpdateQueue(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = schedV1beat1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	queue := &schedV1beat1.Queue{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Queue",
			APIVersion: "scheduling.volcano.sh/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-queue",
		},
		Spec: schedV1beat1.QueueSpec{
			Capability: api.NewResource(v1.ResourceList{
				v1.ResourceCPU:    resource.MustParse("2"),
				v1.ResourceMemory: resource.MustParse("4Gi"),
			}).ResourceList(),
		},
	}

	err := reconciler.Client.Create(context.TODO(), queue)
	assert.NoError(t, err)

	err = reconciler.UpdateQueue(context.TODO(), queue)
	assert.NoError(t, err)

	fetchedQueue := &schedV1beat1.Queue{}
	err = reconciler.Client.Get(context.TODO(), types.NamespacedName{Name: queue.Name}, fetchedQueue)
	assert.NoError(t, err)
	assert.Equal(t, resource.MustParse("2"), *fetchedQueue.Spec.Capability.Cpu())
	assert.Equal(t, resource.MustParse("4Gi"), *fetchedQueue.Spec.Capability.Memory())
}

func TestNodeTaints(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = v1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client:       k8sFakeClient,
		Scheme:       scheme,
		nodeMutex:    sync.RWMutex{},
		nodeLabelMap: make(map[string]map[string]struct{}),
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:       "test-pool",
			Finalizers: []string{myFinalizerName},
		},
		Status: systemv1alpha1.ResourcePoolStatus{
			Nodes: []string{"test-node"},
		},
	}

	err := k8sFakeClient.Create(context.TODO(), resourcepool)
	assert.NoError(t, err)

	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "test-node",
			Labels: map[string]string{},
		},
	}

	err = k8sFakeClient.Create(context.TODO(), node)
	assert.NoError(t, err)

	err = reconciler.nodeTaints(context.TODO(), resourcepool, []string{}, []string{"test-node"})
	assert.NoError(t, err)

	fetchedNode := &v1.Node{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: node.Name}, fetchedNode)
	assert.NoError(t, err)
	_, ok := fetchedNode.Labels[systemv1alpha1.NodeLabelKey]
	assert.False(t, ok)
}

func TestUpdateNodeLabels(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = v1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "test-node",
			Labels: map[string]string{},
		},
	}

	err := k8sFakeClient.Create(context.TODO(), node)
	assert.NoError(t, err)

	err = reconciler.updateNodeLabels(context.TODO(), node)
	assert.NoError(t, err)

	fetchedNode := &v1.Node{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: node.Name}, fetchedNode)
	assert.NoError(t, err)
	if fetchedNode.Labels == nil {
		fetchedNode.Labels = map[string]string{}
	}
	assert.Equal(t, node.Labels, fetchedNode.Labels)
}

func TestGetDiffNodes(t *testing.T) {
	reconciler := &ResourcePoolReconciler{
		nodeMutex:    sync.RWMutex{},
		nodeLabelMap: make(map[string]map[string]struct{}),
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		Status: systemv1alpha1.ResourcePoolStatus{
			Nodes: []string{"node1", "node2"},
		},
	}

	reconciler.nodeLabelMap[resourcepool.Name] = map[string]struct{}{
		"node2": {},
		"node3": {},
	}

	addNodes, delNodes := reconciler.getDiffNodes(resourcepool)
	assert.ElementsMatch(t, addNodes, []string{"node1"})
	assert.ElementsMatch(t, delNodes, []string{"node3"})
}

func TestGetNodeCache(t *testing.T) {
	reconciler := &ResourcePoolReconciler{
		nodeMutex:    sync.RWMutex{},
		nodeLabelMap: make(map[string]map[string]struct{}),
	}

	resourcepool := &systemv1alpha1.ResourcePool{}

	reconciler.nodeLabelMap[resourcepool.Name] = map[string]struct{}{
		"node1": {},
		"node2": {},
	}

	nodes := reconciler.getNodeCache(resourcepool)
	assert.ElementsMatch(t, nodes, []string{"node1", "node2"})
}

func TestCloseQueue(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = schedV1beat1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &ResourcePoolReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	resourcepool := &systemv1alpha1.ResourcePool{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-pool",
		},
	}

	err := k8sFakeClient.Create(context.TODO(), resourcepool)
	assert.NoError(t, err)

	queue := &schedV1beat1.Queue{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Queue",
			APIVersion: "scheduling.volcano.sh/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: resourcepool.Name,
		},
	}

	err = k8sFakeClient.Create(context.TODO(), queue)
	assert.NoError(t, err)

	err = reconciler.closeQueue(context.TODO(), resourcepool, queue, string(commandvc.CloseQueueAction))
	assert.NoError(t, err)
}
