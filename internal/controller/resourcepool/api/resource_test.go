package api

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

func TestEmptyResource(t *testing.T) {
	r := EmptyResource()
	assert.Equal(t, 0.0, r.<PERSON>)
	assert.Equal(t, 0.0, r.Memory)
	assert.Empty(t, r.ScalarResources)
}

func TestNewResource(t *testing.T) {
	rl := v1.ResourceList{
		v1.ResourceCPU:    resource.MustParse("1000m"),
		v1.ResourceMemory: resource.MustParse("1Gi"),
	}
	r := NewResource(rl)
	assert.Equal(t, 1000.0, r.Milli<PERSON>U)
	assert.Equal(t, float64(1<<30), r.Memory)
}

func TestResFloat642Quantity(t *testing.T) {
	quantity := ResFloat642Quantity(v1.ResourceCPU, 500.0)
	assert.Equal(t, int64(500), quantity.MilliValue())

	quantity = ResFloat642Quantity(v1.ResourceMemory, 1024.0)
	assert.Equal(t, int64(1024), quantity.Value())
}

func TestResQuantity2Float64(t *testing.T) {
	quantity := resource.NewMilliQuantity(500, resource.DecimalSI)
	val := ResQuantity2Float64(v1.ResourceCPU, *quantity)
	assert.Equal(t, 500.0, val)

	quantity = resource.NewQuantity(1024, resource.BinarySI)
	val = ResQuantity2Float64(v1.ResourceMemory, *quantity)
	assert.Equal(t, 1024.0, val)
}

func TestClone(t *testing.T) {
	r := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	clone := r.Clone()
	assert.Equal(t, r, clone)
	assert.NotSame(t, r, clone)
}

func TestString(t *testing.T) {
	r := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	expected := "cpu 1000.00, memory 1024.00, customResource 5.00"
	assert.Equal(t, expected, r.String())
}

func TestGet(t *testing.T) {
	r := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}

	assert.Equal(t, 1000.0, r.Get(v1.ResourceCPU))
	assert.Equal(t, 1024.0, r.Get(v1.ResourceMemory))
	assert.Equal(t, 5.0, r.Get("customResource"))
	assert.Equal(t, 0.0, r.Get("unknownResource"))
}

func TestIsEmpty(t *testing.T) {
	r := EmptyResource()
	assert.True(t, r.IsEmpty())

	r.MilliCPU = 1000
	assert.False(t, r.IsEmpty())
}

func TestAdd(t *testing.T) {
	r1 := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	r2 := &Resource{
		MilliCPU: 2000,
		Memory:   2048,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 10.0,
		},
	}
	r1.Add(r2)
	assert.Equal(t, 3000.0, r1.MilliCPU)
	assert.Equal(t, 3072.0, r1.Memory)
	assert.Equal(t, 15.0, r1.ScalarResources["customResource"])
}

func TestSub(t *testing.T) {
	r1 := &Resource{
		MilliCPU: 3000,
		Memory:   3072,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 15.0,
		},
	}
	r2 := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	r1.Sub(r2)
	assert.Equal(t, 2000.0, r1.MilliCPU)
	assert.Equal(t, 2048.0, r1.Memory)
	assert.Equal(t, 10.0, r1.ScalarResources["customResource"])
}

func TestMulti(t *testing.T) {
	r := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	r.Multi(2.0)
	assert.Equal(t, 2000.0, r.MilliCPU)
	assert.Equal(t, 2048.0, r.Memory)
	assert.Equal(t, 10.0, r.ScalarResources["customResource"])
}

func TestSetMaxResource(t *testing.T) {
	r1 := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	r2 := &Resource{
		MilliCPU: 2000,
		Memory:   512,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 10.0,
		},
	}
	r1.SetMaxResource(r2)
	assert.Equal(t, 2000.0, r1.MilliCPU)
	assert.Equal(t, 1024.0, r1.Memory)
	assert.Equal(t, 10.0, r1.ScalarResources["customResource"])
}

func TestLess(t *testing.T) {
	r1 := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
	}
	r2 := &Resource{
		MilliCPU: 2000,
		Memory:   2048,
	}
	assert.True(t, r1.Less(r2, Zero))
	assert.False(t, r2.Less(r1, Zero))
}

func TestEqual(t *testing.T) {
	r1 := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
	}
	r2 := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
	}
	assert.True(t, r1.Equal(r2, Zero))

	r2.Memory = 2048
	assert.False(t, r1.Equal(r2, Zero))
}

// TestLessEqual tests if a Resource is less than or equal to another
func TestLessEqual(t *testing.T) {
	r1 := &Resource{MilliCPU: 1000, Memory: 2048}
	r2 := &Resource{MilliCPU: 1000, Memory: 2048}
	r3 := &Resource{MilliCPU: 2000, Memory: 4096}

	if !r1.LessEqual(r2, Zero) {
		t.Errorf("expected r1 to be less than or equal to r2")
	}
	if !r1.LessEqual(r3, Zero) {
		t.Errorf("expected r1 to be less than or equal to r3")
	}
	if r3.LessEqual(r1, Zero) {
		t.Errorf("expected r3 to be greater than r1")
	}
}

// TestFitDelta tests calculating the delta between two Resource objects
func TestFitDelta(t *testing.T) {
	r1 := &Resource{MilliCPU: 1000, Memory: 2048}
	r2 := &Resource{MilliCPU: 2000, Memory: 4096}

	delta := r1.FitDelta(r2)
	if delta.MilliCPU != -1000.0999999999999 {
		t.Errorf("expected delta CPU to be -1000-minResource, got %f", delta.MilliCPU)
	}
	if delta.Memory != -2048.1000000000004 {
		t.Errorf("expected delta Memory to be -2048-minResource, got %f", delta.Memory)
	}
}

func TestResourceNames(t *testing.T) {
	r := &Resource{
		MilliCPU: 1000.5,
		Memory:   2048.5,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	names := r.ResourceNames()
	expectedNames := ResourceNameList{v1.ResourceCPU, v1.ResourceMemory, "customResource"}
	if !reflect.DeepEqual(names, expectedNames) {
		t.Errorf("Expected %v, got %v", expectedNames, names)
	}
}

func TestSubRes(t *testing.T) {
	r1 := &Resource{
		MilliCPU: 2000,
		Memory:   2048,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 10.0,
		},
	}
	r2 := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	res, err := r1.SubRes(r2)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	expected := &Resource{
		MilliCPU: 1000,
		Memory:   1024,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	if !reflect.DeepEqual(res, expected) {
		t.Errorf("Expected %v, got %v", expected, res)
	}
}

func TestLessEqualWithResourcesName(t *testing.T) {
	r1 := &Resource{MilliCPU: 1000, Memory: 2048}
	r2 := &Resource{MilliCPU: 1000, Memory: 2048}
	ok, resources := r1.LessEqualWithResourcesName(r2, Zero)
	if !ok || len(resources) > 0 {
		t.Errorf("Expected true and empty resources list, got false or non-empty list")
	}
}

func TestLessPartly(t *testing.T) {
	r1 := &Resource{MilliCPU: 1000, Memory: 2048}
	r2 := &Resource{MilliCPU: 1500, Memory: 1500}
	if !r1.LessPartly(r2, Zero) {
		t.Errorf("Expected true for LessPartly")
	}
	r3 := &Resource{MilliCPU: 1000, Memory: 2048}
	if r1.LessPartly(r3, Zero) {
		t.Errorf("Expected false for LessPartly")
	}
}

func TestLessEqualPartly(t *testing.T) {
	r1 := &Resource{MilliCPU: 1000, Memory: 2048}
	r2 := &Resource{MilliCPU: 1000, Memory: 2048}
	if !r1.LessEqualPartly(r2, Zero) {
		t.Errorf("Expected true for LessEqualPartly")
	}
	r3 := &Resource{MilliCPU: 1500, Memory: 1500}
	if !r1.LessEqualPartly(r3, Zero) {
		t.Errorf("Expected true for LessEqualPartly")
	}
}

func TestDiff(t *testing.T) {
	r1 := &Resource{MilliCPU: 1000, Memory: 2048, ScalarResources: map[v1.ResourceName]float64{"customResource": 5.0}}
	r2 := &Resource{MilliCPU: 500, Memory: 1024, ScalarResources: map[v1.ResourceName]float64{"customResource": 3.0}}
	increasedVal, decreasedVal := r1.Diff(r2, Zero)
	expectedIncreased := &Resource{MilliCPU: 500, Memory: 1024, ScalarResources: map[v1.ResourceName]float64{"customResource": 2.0}}
	expectedDecreased := EmptyResource()
	if !reflect.DeepEqual(increasedVal, expectedIncreased) {
		t.Errorf("Expected increased value %v, got %v", expectedIncreased, increasedVal)
	}
	if reflect.DeepEqual(decreasedVal, expectedDecreased) {
		t.Errorf("Expected decreased value %v, got %v", expectedDecreased, decreasedVal)
	}
}

func TestAddScalar(t *testing.T) {
	r := &Resource{ScalarResources: map[v1.ResourceName]float64{"customResource": 5.0}}
	r.AddScalar("customResource", 3.0)
	if r.ScalarResources["customResource"] != 8.0 {
		t.Errorf("Expected customResource to be 8.0, got %f", r.ScalarResources["customResource"])
	}
}

func TestSetScalar(t *testing.T) {
	r := &Resource{ScalarResources: map[v1.ResourceName]float64{"customResource": 5.0}}
	r.SetScalar("customResource", 10.0)
	if r.ScalarResources["customResource"] != 10.0 {
		t.Errorf("Expected customResource to be 10.0, got %f", r.ScalarResources["customResource"])
	}
}

func TestIsScalarResourceName(t *testing.T) {
	extendedResourceName := v1.ResourceName("customResource")
	r := &Resource{ScalarResources: map[v1.ResourceName]float64{"requests.customResource": 5.0}}
	if r.IsScalarResourceName(extendedResourceName) {
		t.Errorf("Expected true for extended resource name")
	}
	standardResourceName := v1.ResourceCPU
	if r.IsScalarResourceName(standardResourceName) {
		t.Errorf("Expected false for standard resource name")
	}
}

func TestMinDimensionResource(t *testing.T) {
	r1 := &Resource{MilliCPU: 1000, Memory: 2048, ScalarResources: map[v1.ResourceName]float64{"customResource": 5.0}}
	r2 := &Resource{MilliCPU: 500, Memory: 1500}
	r1.MinDimensionResource(r2, Zero)
	expected := &Resource{MilliCPU: 500, Memory: 1500, ScalarResources: map[v1.ResourceName]float64{"customResource": 0.0}}
	if !reflect.DeepEqual(r1, expected) {
		t.Errorf("Expected %v, got %v", expected, r1)
	}
}

func TestSetDefaultValue(t *testing.T) {
	r := &Resource{}
	leftResource := &Resource{ScalarResources: map[v1.ResourceName]float64{"leftResource": 5.0}}
	rightResource := &Resource{ScalarResources: map[v1.ResourceName]float64{"rightResource": 3.0}}
	r.setDefaultValue(leftResource, rightResource, Zero)
	if _, ok := leftResource.ScalarResources["rightResource"]; !ok {
		t.Errorf("Left resource should have rightResource with default value")
	}
	if _, ok := rightResource.ScalarResources["leftResource"]; !ok {
		t.Errorf("Right resource should have leftResource with default value")
	}
}

func TestContains(t *testing.T) {
	r := ResourceNameList{v1.ResourceCPU, v1.ResourceMemory}
	rr := ResourceNameList{v1.ResourceCPU}
	if !r.Contains(rr) {
		t.Errorf("Expected true for Contains")
	}
	rr = ResourceNameList{v1.ResourceCPU, v1.ResourceMemory, "customResource"}
	if r.Contains(rr) {
		t.Errorf("Expected false for Contains")
	}
}

func TestResourceList(t *testing.T) {
	r := &Resource{
		MilliCPU: 1000,
		Memory:   2048,
		ScalarResources: map[v1.ResourceName]float64{
			"customResource": 5.0,
		},
	}
	rl := r.ResourceList()
	expectedRL := v1.ResourceList{
		v1.ResourceCPU:    *resource.NewMilliQuantity(1000, resource.BinarySI),
		v1.ResourceMemory: *resource.NewQuantity(2048, resource.BinarySI),
		"customResource":  *resource.NewMilliQuantity(5, resource.BinarySI),
	}
	if !reflect.DeepEqual(rl, expectedRL) {
		t.Errorf("Expected %v, got %v", expectedRL, rl)
	}
}
