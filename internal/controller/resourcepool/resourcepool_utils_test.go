package resourcepool

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestContainsString(t *testing.T) {
	tests := []struct {
		slice   []string
		element string
		expect  bool
	}{
		{[]string{"apple", "banana", "cherry"}, "banana", true},
		{[]string{"apple", "banana", "cherry"}, "orange", false},
		{[]string{}, "any", false},
		{[]string{"single"}, "single", true},
	}

	for _, test := range tests {
		t.Run(test.element, func(t *testing.T) {
			result := containsString(test.slice, test.element)
			assert.Equal(t, test.expect, result)
		})
	}
}

func TestRemoveString(t *testing.T) {
	tests := []struct {
		slice   []string
		element string
		expect  []string
	}{
		{[]string{"apple", "banana", "cherry"}, "banana", []string{"apple", "cherry"}},
		{[]string{"apple", "banana", "cherry"}, "orange", []string{"apple", "banana", "cherry"}},
	}

	for _, test := range tests {
		t.Run(test.element, func(t *testing.T) {
			result := removeString(test.slice, test.element)
			assert.Equal(t, test.expect, result)
		})
	}
}
