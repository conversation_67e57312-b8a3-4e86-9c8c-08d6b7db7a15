/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package resourcepool

import (
	"context"
	"sync"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/resourcepool/state"
	v1 "k8s.io/api/core/v1"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/source"
	schedV1beat1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

// ResourcePoolReconciler reconciles a ResourcePool object
type ResourcePoolReconciler struct { //nolint
	client.Client
	Scheme        *runtime.Scheme
	nodeResMap    map[string]*v1.ResourceList
	nodeLabelMap  map[string]map[string]struct{}
	jobMap        map[string]map[string]struct{}
	notebookMap   map[string]map[string]struct{}
	podMap        map[string]map[string]struct{}
	nodeMutex     sync.RWMutex
	podMutex      sync.RWMutex
	trainjobMutex sync.RWMutex
	notebookMutex sync.RWMutex
}

//+kubebuilder:rbac:groups=system.hero.ai,resources=resourcepools,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=resourcepools/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=resourcepools/finalizers,verbs=update
//+kubebuilder:rbac:groups=core,resources=pods;events;nodes,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=bus.volcano.sh,resources=commands,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=scheduling.volcano.sh;scheduling.incubator.k8s.io,resources=queues,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=scheduling.volcano.sh;scheduling.incubator.k8s.io,resources=queues/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=trainingjobs,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=notebooks,verbs=get;list;watch;create;update;patch;delete

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the ResourcePool object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.14.4/pkg/reconcile
func (r *ResourcePoolReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)

	// TODO(user): your logic here
	var resourcePool = systemv1alpha1.ResourcePool{}
	err := r.Get(ctx, req.NamespacedName, &resourcePool)
	if err != nil {
		klog.Errorf("ResourcePool <%s> is terminating, skip management process.", req.NamespacedName)
		return ctrl.Result{}, nil
	}

	//删除resourcepool
	if err := r.deleteResourcePool(ctx, &resourcePool); err != nil {
		return ctrl.Result{}, err
	}

	st := state.NewState(&resourcePool)
	if st == nil {
		klog.Errorf("Invalid state <%s> of ResourcePool <%s>", resourcePool.Status.State, req.NamespacedName)
		return ctrl.Result{}, nil
	}

	if err := st.Execute(ctx); err != nil {
		return ctrl.Result{}, err
	}

	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *ResourcePoolReconciler) SetupWithManager(mgr ctrl.Manager) error {
	r.Initialize()
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.ResourcePool{}).
		Owns(&schedV1beat1.Queue{}).
		Watches(&source.Kind{Type: &v1.Node{}}, &nodeEnqueueRequestForExtendRes{nodeResMap: r.nodeResMap, nodeLabelMap: r.nodeLabelMap, nodeMutex: &r.nodeMutex}).
		//Watches(&source.Kind{Type: &systemv1alpha1.TrainingJob{}}, &jobRequestForExtendRes{jobMap: r.jobMap, jobMutex: &r.trainjobMutex}).
		//Watches(&source.Kind{Type: &systemv1alpha1.Notebook{}}, &jobRequestForExtendRes{jobMap: r.notebookMap, jobMutex: &r.notebookMutex}).
		Watches(&source.Kind{Type: &v1.Pod{}}, &jobRequestForExtendRes{jobMap: r.podMap, jobMutex: &r.podMutex}).
		Complete(r)
}

func (r *ResourcePoolReconciler) Initialize() {
	state.SyncResourcePool = r.syncResourcePool
	state.CreateResourcePool = r.createResourcePool
	r.nodeResMap = make(map[string]*v1.ResourceList)
	r.nodeLabelMap = make(map[string]map[string]struct{})
	r.jobMap = make(map[string]map[string]struct{})
	r.notebookMap = make(map[string]map[string]struct{})
	r.podMap = make(map[string]map[string]struct{})
}
