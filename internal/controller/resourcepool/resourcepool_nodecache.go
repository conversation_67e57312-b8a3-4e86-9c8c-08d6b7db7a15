package resourcepool

import (
	"sync"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/event"
)

type nodeEnqueueRequestForExtendRes struct {
	nodeResMap   map[string]*v1.ResourceList
	nodeLabelMap map[string]map[string]struct{}
	nodeMutex    *sync.RWMutex
}

// addnode 不需要Reconcile
func (e *nodeEnqueueRequestForExtendRes) Create(evt event.CreateEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "CreateEvent received with no metadata", "event", evt)
		return
	}

	if _, ok := evt.Object.(*v1.Node); !ok {
		return
	}

	e.nodeMutex.Lock()
	defer e.nodeMutex.Unlock()
	e.nodeResMap[evt.Object.GetName()] = &evt.Object.(*v1.Node).Status.Capacity

	//1、node labels必须有标签才是专属资源节点
	//2、node labels如果没有标签，必须删除原缓存到该node
	if _, found := evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]; found {
		if _, found := e.nodeLabelMap[evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]]; !found {
			e.nodeLabelMap[evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]] = map[string]struct{}{}
		}
		e.nodeLabelMap[evt.Object.GetLabels()[systemv1alpha1.NodeLabelKey]][evt.Object.GetName()] = struct{}{}
	} else {
		for index := range e.nodeLabelMap {
			for node := range e.nodeLabelMap[index] {
				if node == evt.Object.GetName() {
					delete(e.nodeLabelMap[index], node)
				}
			}
		}
	}
}

// 转换，需要换成resourcepool添加到队列，更新节点需要考虑节点资源变化
// Update implements EventHandler.
func (e *nodeEnqueueRequestForExtendRes) Update(evt event.UpdateEvent, q workqueue.RateLimitingInterface) {
	if _, ok := evt.ObjectNew.(*v1.Node); !ok {
		return
	}

	e.Create(event.CreateEvent{Object: evt.ObjectNew}, q)
}

// 删除节点不能改变绑定关系
// Delete implements EventHandler.
func (e *nodeEnqueueRequestForExtendRes) Delete(evt event.DeleteEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "DeleteEvent received with no metadata", "event", evt)
		return
	}
}

// Generic implements EventHandler.
func (e *nodeEnqueueRequestForExtendRes) Generic(evt event.GenericEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "GenericEvent received with no metadata", "event", evt)
		return
	}
}
