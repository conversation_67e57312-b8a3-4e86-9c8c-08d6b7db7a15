package resourcepool

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/client-go/util/workqueue"
	"sigs.k8s.io/controller-runtime/pkg/event"
)

func TestJobRequestForExtendRes_Create(t *testing.T) {
	q := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	jobReq := &jobRequestForExtendRes{
		jobMap:   make(map[string]map[string]struct{}),
		jobMutex: &sync.RWMutex{},
	}

	// Creating a mock object
	obj := &unstructured.Unstructured{}
	obj.SetNamespace("default")
	obj.SetName("test-job")
	obj.SetLabels(map[string]string{systemv1alpha1.NodeLabelKey: "node1"})

	evt := event.CreateEvent{Object: obj}

	// Call Create method
	jobReq.Create(evt, q)

	// Validate jobMap
	jobReq.jobMutex.RLock()
	defer jobReq.jobMutex.RUnlock()
	assert.NotNil(t, jobReq.jobMap["node1"])
	assert.NotNil(t, jobReq.jobMap["node1"]["default/test-job"])
	assert.Equal(t, 1, len(jobReq.jobMap["node1"]))
}

func TestJobRequestForExtendRes_Update(t *testing.T) {
	q := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	jobReq := &jobRequestForExtendRes{
		jobMap:   make(map[string]map[string]struct{}),
		jobMutex: &sync.RWMutex{},
	}

	// Creating a mock object
	obj := &unstructured.Unstructured{}
	obj.SetNamespace("default")
	obj.SetName("test-job")
	obj.SetLabels(map[string]string{systemv1alpha1.NodeLabelKey: "node1"})

	// Initial creation
	evtCreate := event.CreateEvent{Object: obj}
	jobReq.Create(evtCreate, q)

	// Updating the object
	obj.SetName("test-job-updated")
	evtUpdate := event.UpdateEvent{ObjectOld: obj, ObjectNew: obj}
	jobReq.Update(evtUpdate, q)

	// Validate jobMap
	jobReq.jobMutex.RLock()
	defer jobReq.jobMutex.RUnlock()
	assert.Equal(t, 2, len(jobReq.jobMap["node1"]))
	assert.NotNil(t, jobReq.jobMap["node1"]["default/test-job-updated"])
}

func TestJobRequestForExtendRes_Delete(t *testing.T) {
	q := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	jobReq := &jobRequestForExtendRes{
		jobMap:   make(map[string]map[string]struct{}),
		jobMutex: &sync.RWMutex{},
	}

	// Creating a mock object
	obj := &unstructured.Unstructured{}
	obj.SetNamespace("default")
	obj.SetName("test-job")
	obj.SetLabels(map[string]string{systemv1alpha1.NodeLabelKey: "node1"})

	// Initial creation
	evtCreate := event.CreateEvent{Object: obj}
	jobReq.Create(evtCreate, q)

	// Deleting the object
	evtDelete := event.DeleteEvent{Object: obj}
	jobReq.Delete(evtDelete, q)

	// Validate jobMap
	jobReq.jobMutex.RLock()
	defer jobReq.jobMutex.RUnlock()
	assert.Empty(t, jobReq.jobMap["node1"])
}

func TestJobRequestForExtendRes_Generic(t *testing.T) {
	q := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	jobReq := &jobRequestForExtendRes{
		jobMap:   make(map[string]map[string]struct{}),
		jobMutex: &sync.RWMutex{},
	}

	// Creating a mock object
	obj := &unstructured.Unstructured{}
	obj.SetNamespace("default")
	obj.SetName("test-job")

	evt := event.GenericEvent{Object: obj}

	// Call Generic method
	jobReq.Generic(evt, q)

	// No state change expected
	jobReq.jobMutex.RLock()
	defer jobReq.jobMutex.RUnlock()
	assert.Empty(t, jobReq.jobMap)
}
