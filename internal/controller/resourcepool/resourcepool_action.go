//nolint:all
package resourcepool

import (
	"context"
	"strings"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/resourcepool/api"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	commandvc "volcano.sh/apis/pkg/apis/bus/v1alpha1"
	"volcano.sh/apis/pkg/apis/helpers"
	schedV1beat1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

var (
	stopAnnotation  = "command.system.hero.ai"
	stopAction      = "stop"
	openAction      = "open"
	myFinalizerName = "system.hero.ai/finalizer"
)

// resourcepool 创建不带节点，create queue & update state==open
func (r *ResourcePoolReconciler) createResourcePool(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool) (err error) {
	_, err = r.createOrUpdateQueue(ctx, resourcepool, api.EmptyResource())
	if err != nil {
		return err
	}

	if !containsString(resourcepool.ObjectMeta.Finalizers, myFinalizerName) {
		resourcepool.ObjectMeta.Finalizers = append(resourcepool.ObjectMeta.Finalizers, myFinalizerName)
	}
	if err := r.UpdateResourcePool(ctx, resourcepool); err != nil {
		return err
	}

	resourcepool.Status.State = systemv1alpha1.ResourcePoolStateOpen
	return r.UpdateResourcePoolStatus(ctx, resourcepool)
}

// status node资源更新资源池使用情况，更新rp nodes
func (r *ResourcePoolReconciler) syncResourcePool(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool) (err error) {
	nodeRes, nodeResMap := r.getQueueCapResource(resourcepool)
	resourcepool.Status.Capabilities = nodeResMap

	queue, _ := r.createOrUpdateQueue(ctx, resourcepool, nodeRes)

	//禁用Queue仍需更新status
	defer func() error {
		_, ok := resourcepool.Annotations[stopAnnotation]
		if ok && resourcepool.Status.State != systemv1alpha1.ResourcePoolStateClosing &&
			resourcepool.Annotations[stopAnnotation] == stopAction && resourcepool.Status.State != systemv1alpha1.ResourcePoolStateClosed {
			err = r.closeQueue(ctx, resourcepool, queue, string(commandvc.CloseQueueAction))
			if err != nil {
				return err
			}

			resourcepool.Status.State = systemv1alpha1.ResourcePoolStateClosing
			return r.UpdateResourcePoolStatus(ctx, resourcepool)
		}

		if ok && resourcepool.Annotations[stopAnnotation] == openAction && resourcepool.Status.State == systemv1alpha1.ResourcePoolStateClosed {
			err := r.closeQueue(ctx, resourcepool, queue, string(commandvc.OpenQueueAction))
			if err != nil {
				return err
			}
		}
		return nil
	}()
	klog.Infof("resourcepoool info: %v, nodeRes: %v", resourcepool.Status.Nodes, *nodeRes)
	resourcepoolStatus := r.initResourcePool(resourcepool, nodeRes)
	//不需要给节点再打标签
	// addnodes, delnodes := r.getDiffNodes(resourcepool)
	// if len(addnodes) > 0 || len(delnodes) > 0 {
	// 	err = r.nodeTaints(ctx, resourcepool, addnodes, delnodes)
	// 	if err != nil {
	// 		return err
	// 	}
	// }

	//计算被占用的资源池作业任务
	jobs := r.getJobs(resourcepool.Name)
	notebooks := r.getNotebookJobs(resourcepool.Name)
	for _, jobKey := range jobs {
		var job systemv1alpha1.TrainingJob
		err := r.Client.Get(ctx, types.NamespacedName{
			Namespace: strings.Split(jobKey, "/")[0],
			Name:      strings.Split(jobKey, "/")[1],
		}, &job)

		if err != nil {
			klog.Errorf("ResourcePool get job %s failed", jobKey)
			continue
		}

		switch job.Status.State.Phase {
		case systemv1alpha1.Pending, systemv1alpha1.Queuing, systemv1alpha1.Stopping, systemv1alpha1.Running:
			jobTotalResource := r.calcJobResource(&job)
			idle, _ := nodeRes.SubRes(jobTotalResource)
			if idle != nil {
				resourcepoolStatus.Idle = idle.ResourceList()
			}
		}
	}

	for _, jobKey := range notebooks {
		var job systemv1alpha1.Notebook
		err := r.Client.Get(ctx, types.NamespacedName{
			Namespace: strings.Split(jobKey, "/")[0],
			Name:      strings.Split(jobKey, "/")[1],
		}, &job)

		if err != nil {
			klog.Errorf("ResourcePool get notebookjob %s failed", jobKey)
			continue
		}

		switch job.Status.State {
		case systemv1alpha1.NotebookStatePending, systemv1alpha1.NotebookStateStartUp, systemv1alpha1.NotebookStateRunning, systemv1alpha1.NotebookStateStopping:
			jobTotalResource := api.NewResource(job.Spec.Resource)
			idle, _ := nodeRes.SubRes(jobTotalResource)
			if idle != nil {
				resourcepoolStatus.Idle = idle.ResourceList()
			}
		}
	}

	//idle应该是减去pod占用的资源
	pods := r.getPods(resourcepool.Name)
	podResourceMap := r.calcPodTotalResource(ctx, pods)
	resourcepoolStatus.Allocated = podResourceMap
	resourcepool.Status.Idle = resourcepoolStatus.Idle
	resourcepool.Status.Allocated = resourcepoolStatus.Allocated

	if queue.Status.State == schedV1beat1.QueueStateOpen {
		resourcepool.Status.State = systemv1alpha1.ResourcePoolStateOpen
	} else if queue.Status.State == schedV1beat1.QueueStateClosing {
		resourcepool.Status.State = systemv1alpha1.ResourcePoolStateClosing
	} else if queue.Status.State == schedV1beat1.QueueStateUnknown {
		resourcepool.Status.State = systemv1alpha1.ResourcePoolStateFailed
	} else if queue.Status.State == schedV1beat1.QueueStateClosed {
		resourcepool.Status.State = systemv1alpha1.ResourcePoolStateClosed
	}

	return r.UpdateResourcePoolStatus(ctx, resourcepool)
}

func (r *ResourcePoolReconciler) initResourcePool(resourcepool *systemv1alpha1.ResourcePool, nodeResource *api.Resource) *systemv1alpha1.ResourcePoolStatus {
	resourcepool.Status.Idle = nodeResource.ResourceList()

	return &resourcepool.Status
}

func (r *ResourcePoolReconciler) calcJobResource(job *systemv1alpha1.TrainingJob) *api.Resource {
	var jobResource = api.EmptyResource()
	for _, v := range job.Spec.Tasks {
		for i := 0; i < int(v.Replicas); i++ {
			jobResource.Add(api.NewResource(v.Resource))
		}
	}
	return jobResource
}

func (r *ResourcePoolReconciler) calcPodTotalResource(ctx context.Context, pods []string) map[string]v1.ResourceList {
	var podResource = make(map[string]v1.ResourceList)

	for _, podName := range pods {
		var pod v1.Pod
		err := r.Client.Get(ctx, types.NamespacedName{
			Namespace: strings.Split(podName, "/")[0],
			Name:      strings.Split(podName, "/")[1],
		}, &pod)
		if err != nil {
			klog.Errorf("ResourcePool get pod %s failed", podName)
			continue
		}

		if pod.Status.Phase != v1.PodPending && pod.Status.Phase != v1.PodRunning {
			continue
		}

		if len(pod.Spec.NodeName) == 0 {
			continue
		}

		if _, found := podResource[pod.Spec.NodeName]; !found {
			podResource[pod.Spec.NodeName] = api.EmptyResource().ResourceList()
		}
		for _, container := range pod.Spec.Containers {
			podResource[pod.Spec.NodeName] = api.NewResource(podResource[pod.Spec.NodeName]).Add(api.NewResource(container.Resources.Requests)).ResourceList()
		}
	}

	return podResource
}

func (r *ResourcePoolReconciler) getJobs(key string) []string {
	jobs := []string{}
	r.trainjobMutex.RLock()
	defer r.trainjobMutex.RUnlock()
	if _, found := r.jobMap[key]; !found {
		return jobs
	}

	for job := range r.jobMap[key] {
		jobs = append(jobs, job)
	}

	return jobs
}

func (r *ResourcePoolReconciler) UpdateResourcePoolStatus(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		rp := &systemv1alpha1.ResourcePool{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: resourcepool.Name}, rp); err != nil {
			return
		}
		rp.Status = resourcepool.Status
		return r.Status().Update(ctx, rp)
	})
}

func (r *ResourcePoolReconciler) UpdateResourcePool(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		rp := &systemv1alpha1.ResourcePool{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: resourcepool.Name}, rp); err != nil {
			return
		}
		rp.Finalizers = resourcepool.ObjectMeta.Finalizers

		return r.Update(ctx, rp)
	})
}

func (r *ResourcePoolReconciler) getNotebookJobs(key string) []string {
	jobs := []string{}
	r.notebookMutex.RLock()
	defer r.notebookMutex.RUnlock()
	if _, found := r.notebookMap[key]; !found {
		return jobs
	}

	for job := range r.notebookMap[key] {
		jobs = append(jobs, job)
	}

	return jobs
}

func (r *ResourcePoolReconciler) getPods(key string) []string {
	pods := []string{}
	r.podMutex.RLock()
	defer r.podMutex.RUnlock()
	if _, found := r.podMap[key]; !found {
		return pods
	}

	for job := range r.podMap[key] {
		pods = append(pods, job)
	}

	return pods
}

func (r *ResourcePoolReconciler) getQueueCapResource(resourcepool *systemv1alpha1.ResourcePool) (*api.Resource, map[string]v1.ResourceList) {
	res := api.EmptyResource()
	resmap := make(map[string]v1.ResourceList)
	r.nodeMutex.RLock()
	defer r.nodeMutex.RUnlock()
	for _, nodeId := range resourcepool.Status.Nodes {
		if _, found := r.nodeResMap[nodeId]; !found {
			klog.Errorf("ResourcePool %s get node %s not found", resourcepool.Name, nodeId)
			continue
		}
		resmap[nodeId] = api.NewResource(*r.nodeResMap[nodeId]).ResourceList()
		nodeRes := api.NewResource(*r.nodeResMap[nodeId])
		res = res.Add(nodeRes)
	}

	return res, resmap
}

func (r *ResourcePoolReconciler) createOrUpdateQueue(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool, res *api.Resource) (*schedV1beat1.Queue, error) {
	var queue schedV1beat1.Queue
	err := r.Client.Get(ctx, types.NamespacedName{
		Name: resourcepool.Name,
	}, &queue)
	if err != nil {
		if apierrors.IsNotFound(err) {
			queue = schedV1beat1.Queue{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Queue",
					APIVersion: "scheduling.volcano.sh/v1beta1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name: resourcepool.Name,
				},
				Spec: schedV1beat1.QueueSpec{
					Capability: res.ResourceList(),
				},
			}

			if err := controllerutil.SetControllerReference(resourcepool, &queue, r.Scheme); err != nil {
				return &queue, err
			}

			return &queue, r.Create(ctx, &queue)
		}
		return &queue, err
	}

	if res.Equal(api.NewResource(queue.Spec.Capability), api.Zero) {
		return &queue, nil
	}

	queue.Spec.Capability = res.ResourceList()
	return &queue, r.UpdateQueue(ctx, &queue)
}

func (r *ResourcePoolReconciler) UpdateQueue(ctx context.Context, q *schedV1beat1.Queue) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		queue := &schedV1beat1.Queue{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: q.Name}, queue); err != nil {
			return
		}

		queue.Spec = q.Spec

		return r.Update(ctx, queue)
	})
}

func (r *ResourcePoolReconciler) deleteResourcePool(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool) error {
	if !resourcepool.DeletionTimestamp.IsZero() {

		if containsString(resourcepool.ObjectMeta.Finalizers, myFinalizerName) {
			if len(resourcepool.Status.Nodes) > 0 {
				if err := r.nodeTaints(ctx, resourcepool, []string{}, resourcepool.Status.Nodes); err != nil {
					return err
				}
			}

			resourcepool.ObjectMeta.Finalizers = removeString(resourcepool.ObjectMeta.Finalizers, myFinalizerName)
			return r.UpdateResourcePool(context.Background(), resourcepool)
		}
	}

	return nil
}

func (r *ResourcePoolReconciler) nodeTaints(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool, addNodes, delNodes []string) error {
	var add = func(labels map[string]string) {
		labels[systemv1alpha1.NodeLabelKey] = resourcepool.Name
	}

	var del = func(labels map[string]string) {
		delete(labels, systemv1alpha1.NodeLabelKey)
		r.nodeMutex.Lock()
		defer r.nodeMutex.Unlock()
		delete(r.nodeLabelMap[resourcepool.Name], systemv1alpha1.NodeLabelKey)
	}

	var updateNode = func(nodes []string, fn func(map[string]string)) error {
		for _, nodeName := range nodes {
			var node v1.Node
			err := r.Client.Get(ctx, types.NamespacedName{
				Name: nodeName,
			}, &node)
			if err != nil {
				if apierrors.IsNotFound(err) {
					continue
				}
				return err
			}

			labels := node.GetLabels()
			fn(labels)
			node.SetLabels(labels)

			if err := r.updateNodeLabels(ctx, &node); err != nil {
				return err
			}

		}
		return nil
	}

	if err := updateNode(addNodes, add); err != nil {
		return err
	}

	return updateNode(delNodes, del)
}

func (r *ResourcePoolReconciler) updateNodeLabels(ctx context.Context, node *v1.Node) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		srcnode := &v1.Node{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: node.Name}, srcnode); err != nil {
			return
		}

		srcnode.Labels = node.Labels

		return r.Update(ctx, srcnode)
	})
}

func (r *ResourcePoolReconciler) getDiffNodes(resourcepool *systemv1alpha1.ResourcePool) (addNodes, delNodes []string) {
	var getDiffs = func(oldNodes, newNodes []string) []string {
		diffNodes := []string{}
		tempMap := map[string]struct{}{}
		for _, node := range oldNodes {
			tempMap[node] = struct{}{}
		}

		for _, node := range newNodes {
			if _, found := tempMap[node]; !found {
				diffNodes = append(diffNodes, node)
			}
		}

		return diffNodes
	}

	r.nodeMutex.RLock()
	defer r.nodeMutex.RUnlock()
	if _, found := r.nodeLabelMap[resourcepool.Name]; !found {
		return resourcepool.Status.Nodes, delNodes
	}

	addNodes = getDiffs(r.getNodeCache(resourcepool), resourcepool.Status.Nodes)
	delNodes = getDiffs(resourcepool.Status.Nodes, r.getNodeCache(resourcepool))
	return
}

func (r *ResourcePoolReconciler) getNodeCache(resourcepool *systemv1alpha1.ResourcePool) []string {
	nodes := []string{}
	r.nodeMutex.RLock()
	defer r.nodeMutex.RUnlock()
	for nodeId := range r.nodeLabelMap[resourcepool.Name] {
		nodes = append(nodes, nodeId)
	}

	return nodes
}

func (r *ResourcePoolReconciler) closeQueue(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool, queue *schedV1beat1.Queue, action string) error {
	var bus commandvc.Command
	err := r.Get(ctx, types.NamespacedName{
		Name: resourcepool.Name,
	}, &bus)

	if !apierrors.IsNotFound(err) {
		return nil
	}

	bus = commandvc.Command{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Command",
			APIVersion: "bus.volcano.sh/v1alpha1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourcepool.Name,
			Namespace: "default",
		},
		Action:       action,
		TargetObject: metav1.NewControllerRef(queue, helpers.V1beta1QueueKind),
	}

	if err := controllerutil.SetControllerReference(resourcepool, &bus, r.Scheme); err != nil {
		return err
	}

	return r.Create(ctx, &bus)
}
