package controller

import (
	"time"

	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/predicate"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

func NewtensorboardPredicate() predicate.Predicate {
	return &tensorboardPredicate{LastDeleteTime: make(map[string]time.Time)}
}

type tensorboardPredicate struct {
	LastDeleteTime map[string]time.Time
}

func (c *tensorboardPredicate) Create(e event.CreateEvent) bool {
	return true

}

func (c *tensorboardPredicate) Update(e event.UpdateEvent) bool {
	_, ok1 := e.ObjectOld.(*systemv1alpha1.Tensorboard)
	_, ok2 := e.ObjectNew.(*systemv1alpha1.Tensorboard)
	if ok1 && ok2 {
		return true
	}
	oldpod, ok1 := e.ObjectOld.(*corev1.Pod)
	newpod, ok2 := e.ObjectNew.(*corev1.Pod)
	if ok1 && ok2 {
		return oldpod.Status.Phase != newpod.Status.Phase
	}
	return false

}

func (c *tensorboardPredicate) Delete(e event.DeleteEvent) bool {
	uid := string(e.Object.GetUID())
	lastDeleteTime, exists := c.LastDeleteTime[uid]
	if exists && time.Since(lastDeleteTime) < time.Second {
		return false
	}
	c.LastDeleteTime[uid] = time.Now()
	return true
}

func (c *tensorboardPredicate) Generic(e event.GenericEvent) bool {
	return true
}
