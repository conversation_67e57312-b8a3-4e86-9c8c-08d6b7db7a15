/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"path/filepath"
	"time"

	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/tools/record"
	"k8s.io/utils/pointer"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"hero.ai/hero-controllers/internal/controller/job"
	vc "hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
)

// TensorboardReconciler reconciles a Tensorboard object
type TensorboardReconciler struct {
	client.Client
	Scheme        *runtime.Scheme
	Recorder      record.EventRecorder
	IngressConfig config.IngressConfig
	ServerConfig  config.ServerConfig
	// Timers   sync.Map
}

const (
	Finalizer               = "system.hero.ai/finalizer"
	FromLabel               = "system.hero.ai/from"
	AnnotationKeyCommand    = "command.system.hero.ai"
	CommandStop             = "stop"
	ExposedPort             = 6006
	ServiceTargetPort       = 80
	TensorbordkPrefix       = "tensorbord-"
	TensorBoardMountPathDir = "/tensorboard"
)

//+kubebuilder:rbac:groups=system.hero.ai,resources=tensorboards,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=tensorboards/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=tensorboards/finalizers,verbs=update
//+kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=pods/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=core,resources=services,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=services/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=core,resources=events,verbs=create;update;patch

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the TrainingJob object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.14.4/pkg/reconcile
func (r *TensorboardReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := log.FromContext(ctx)
	var tensorboard systemv1alpha1.Tensorboard
	if err := r.Get(ctx, req.NamespacedName, &tensorboard); err != nil {
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}

	// value, exists := tensorboard.Labels["debug.system.hero.ai"]
	// if exists && value == "true" {
	// 	return ctrl.Result{}, nil
	// }
	if err := r.initTensorboardStatus(ctx, &tensorboard); err != nil {
		return ctrl.Result{}, err
	}
	var podList corev1.PodList
	err := r.List(ctx, &podList, client.InNamespace(req.Namespace), client.MatchingLabels{FromLabel: TensorbordkPrefix + tensorboard.Name})
	if err != nil {
		return ctrl.Result{}, err
	}
	podlist_Length := len(podList.Items) //nolint

	if err := r.CheckTensorboard(ctx, req, &tensorboard, podList); err != nil {
		return ctrl.Result{}, err
	}

	//pod没建起来&phase!=stopping
	if podlist_Length == 0 && tensorboard.Status.State.Phase == systemv1alpha1.TBPending {
		if err = r.CreateTensorboardResources(ctx, req, &tensorboard); err != nil {
			log.Error(err, "create tensorboard resource failed")
			return ctrl.Result{}, err
		}
		return ctrl.Result{}, nil
	}

	if len(podList.Items) > 0 {
		pod := podList.Items[0]
		if err = r.SyncTensorboardPhaseIfPodRunning(ctx, &pod, &tensorboard); err != nil {
			return ctrl.Result{}, err
		}
	}
	return ctrl.Result{}, nil
}

func (r TensorboardReconciler) initTensorboardStatus(ctx context.Context, tensorboard *systemv1alpha1.Tensorboard) error {
	if tensorboard.Status.State.Phase == "" {
		tensorboard.Status.State.Phase = systemv1alpha1.TBPending
	}
	return r.Status().Update(ctx, tensorboard)
}

func (r *TensorboardReconciler) CheckTensorboard(ctx context.Context, req ctrl.Request, tensorboard *systemv1alpha1.Tensorboard, podList corev1.PodList) error {
	log := log.FromContext(ctx)

	_, ok := tensorboard.Annotations[AnnotationKeyCommand]
	if ok && tensorboard.Annotations[AnnotationKeyCommand] == CommandStop && tensorboard.Status.State.Phase != systemv1alpha1.TBStopping && tensorboard.Status.State.Phase != systemv1alpha1.TBStopped {
		return r.DeleteTensorboardResources(ctx, podList.Items, tensorboard)
	}

	//stopped状态，只有stopping---->stopped
	if len(podList.Items) == 0 && tensorboard.Status.State.Phase == systemv1alpha1.TBStopping {
		return r.SyncTensorboardPhaseIfPodStoped(ctx, tensorboard)
	}

	//running后开启定时任务
	if tensorboard.Status.State.Phase == systemv1alpha1.TBRunning {
		if tensorboard.Spec.MaxRunningTimeMinutes > 0 {
			log.Info("create MaxRunningTime Timer")
			cycleTime := -time.Since(tensorboard.Status.StartTime.Time) + time.Minute*time.Duration(tensorboard.Spec.MaxRunningTimeMinutes)

			if -time.Since(tensorboard.Status.StartTime.Time)+time.Minute*time.Duration(tensorboard.Spec.MaxRunningTimeMinutes) < 0 {
				log.Info("tensorboard runTime lower since now")
				cycleTime = time.Second
			}

			job.RegisterTimerTaskHandle(tensorboard.Name, job.TimerTaskFunc(func() {
				r.Recorder.Event(tensorboard, corev1.EventTypeNormal, "MaxRunningTimeUp", "tensorboard's maxRunningTime is up")
				var tb = &systemv1alpha1.Tensorboard{}
				if err := r.Get(context.TODO(), types.NamespacedName{
					Namespace: tensorboard.Namespace,
					Name:      tensorboard.Name,
				}, tb); err != nil {
					log.Error(err, "tensorboard %s has been deleted", tensorboard.Name)
					return
				}

				if tensorboard.Annotations == nil {
					tensorboard.Annotations = make(map[string]string)
				}

				tensorboard.Annotations[AnnotationKeyCommand] = CommandStop
				if err := r.Update(context.TODO(), tensorboard); err != nil {
					log.Error(err, "update tensorboard failed")
					return
				}

			}), cycleTime)
		}
	}

	return nil
}

func (r *TensorboardReconciler) SyncTensorboardPhaseIfPodStoped(ctx context.Context, tb *systemv1alpha1.Tensorboard) error {
	log := log.FromContext(ctx)
	r.Recorder.Event(tb, corev1.EventTypeNormal, "podStoped", "tensorboard's pod have deleted")
	tb.Status.StoppedTime = metav1.Now()
	tb.Status.State.Phase = systemv1alpha1.TBStopped
	if err := r.Status().Update(ctx, tb); err != nil {
		log.Error(err, "unable to update Tensorboard status")
		return err
	}
	return nil
}

func (r *TensorboardReconciler) SyncTensorboardPhaseIfPodRunning(ctx context.Context, pod *corev1.Pod, tb *systemv1alpha1.Tensorboard) error {
	log := log.FromContext(ctx)
	for _, cStatuses := range pod.Status.ContainerStatuses {
		if cStatuses.Name == "tensorboard" && cStatuses.Ready && tb.Status.StartTime.IsZero() {
			// time.Sleep(time.Second * 5)
			r.Recorder.Event(tb, corev1.EventTypeNormal, "PodRunning", "Pod start running")

			tbingress := &networkingv1.Ingress{}

			if err := r.Get(ctx, types.NamespacedName{Namespace: tb.Namespace, Name: TensorbordkPrefix + "ingress-" + tb.Name}, tbingress); err != nil {
				return err
			}
			if len(tbingress.Status.LoadBalancer.Ingress) != 0 {
				r.Recorder.Event(tb, corev1.EventTypeNormal, "Ingress", "Ready")
				tb.Status.StartTime = pod.ObjectMeta.CreationTimestamp
				tb.Status.State.Phase = systemv1alpha1.TBRunning
				if err := r.Status().Update(ctx, tb); err != nil {
					log.Error(err, "unable to update Tensorboard Phase")
					return err
				}
			}

		}
	}
	return nil
}

func (r *TensorboardReconciler) SyncTensorboardPhaseIfPodStoping(ctx context.Context, tb *systemv1alpha1.Tensorboard) error {
	log := log.FromContext(ctx)
	tb.Status.State.Phase = systemv1alpha1.TBStopping
	if err := r.Status().Update(ctx, tb); err != nil {
		log.Error(err, "unable to update Tensorboard status")
		return err
	}
	return nil
}

func (r *TensorboardReconciler) GetTensorboardURL(ctx context.Context, tb *systemv1alpha1.Tensorboard) error {
	log := log.FromContext(ctx)
	tb.Status.TensorboardUrl = r.IngressConfig.Domain + "/tensorbord_" + tb.Name + "/"
	if err := r.Status().Update(ctx, tb); err != nil {
		log.Error(err, "unable to update Tensorboard URL")
		return err
	}
	return nil
}

func (r *TensorboardReconciler) DeleteTensorboardResources(ctx context.Context, pods []corev1.Pod, tb *systemv1alpha1.Tensorboard) error {
	if len(pods) > 0 {
		if err := r.Delete(ctx, &pods[0]); err != nil {
			return client.IgnoreNotFound(err)
		}
	}
	if err := r.SyncTensorboardPhaseIfPodStoping(ctx, tb); err != nil {
		return err
	}
	return nil
}

func (r *TensorboardReconciler) CreateTensorboardResources(ctx context.Context, req ctrl.Request, tb *systemv1alpha1.Tensorboard) error {
	log := log.FromContext(ctx)

	pod, err := r.CreatePod(ctx, tb)
	if err != nil {
		log.Error(err, "create pod failed : ")
		return err
	}
	r.Recorder.Event(tb, corev1.EventTypeNormal, "PodCreated", "pod is created, waiting for pod running")

	if err = r.CreateService(ctx, tb, pod); err != nil {
		log.Error(err, "create service failed")
		return err
	}

	r.Recorder.Event(tb, corev1.EventTypeNormal, "ServiceCreated", "service is created, waiting for pod running")

	if err = r.CreateIngress(ctx, tb, pod); err != nil {
		log.Error(err, "create ingress failed")
		return err
	}

	r.Recorder.Event(tb, corev1.EventTypeNormal, "IngressCreated", "ingress is created, waiting for pod running")

	return nil
}

func (r *TensorboardReconciler) CreateService(ctx context.Context, tb *systemv1alpha1.Tensorboard, pod *corev1.Pod) error {
	log := log.FromContext(ctx)
	var service corev1.Service
	if err := r.Get(ctx, types.NamespacedName{Name: TensorbordkPrefix + "service-" + tb.Name, Namespace: tb.Namespace}, &service); err == nil {
		if err := r.Delete(ctx, &service); err != nil {
			log.Error(err, "delete service error")
			return err
		}
	}
	service = corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      TensorbordkPrefix + "service-" + tb.Name,
			Namespace: tb.Namespace,
		},
		Spec: corev1.ServiceSpec{
			Ports: []corev1.ServicePort{
				{
					Protocol:   corev1.ProtocolTCP,
					Port:       ServiceTargetPort,
					TargetPort: intstr.FromInt(ExposedPort),
				},
			},
			Selector: map[string]string{
				FromLabel: TensorbordkPrefix + tb.Name,
			},
			Type: corev1.ServiceTypeClusterIP,
		},
	}
	log.Info("set reference")
	if err := controllerutil.SetControllerReference(pod, &service, r.Scheme); err != nil {
		log.Error(err, "SetControllerReference error")
		return err
	}

	log.Info("start create service")
	if err := r.Create(ctx, &service); err != nil {
		log.Error(err, "create service error")
		return err
	}
	log.Info("create service success")
	return nil

}

func (r *TensorboardReconciler) CreateIngress(ctx context.Context, tb *systemv1alpha1.Tensorboard, pod *corev1.Pod) error {
	log := log.FromContext(ctx)
	ingressClass := "nginx"
	PathType := networkingv1.PathTypeImplementationSpecific

	ingress := &networkingv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      TensorbordkPrefix + "ingress-" + tb.Name,
			Namespace: tb.Namespace,
			Annotations: map[string]string{
				"nginx.ingress.kubernetes.io/rewrite-target": "/$1",
			},
		},
		Spec: networkingv1.IngressSpec{
			IngressClassName: &ingressClass,
			Rules: []networkingv1.IngressRule{
				{
					Host: r.IngressConfig.Host,
					IngressRuleValue: networkingv1.IngressRuleValue{
						HTTP: &networkingv1.HTTPIngressRuleValue{
							Paths: []networkingv1.HTTPIngressPath{
								{
									Path:     "/tensorbord_" + tb.Name + "/(.*)",
									PathType: &PathType,
									Backend: networkingv1.IngressBackend{
										Service: &networkingv1.IngressServiceBackend{
											Name: TensorbordkPrefix + "service-" + tb.Name,
											Port: networkingv1.ServiceBackendPort{
												Number: int32(ServiceTargetPort),
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	log.Info("set reference")
	err := controllerutil.SetControllerReference(tb, ingress, r.Scheme)
	if err != nil {
		log.Error(err, "SetControllerReference error")
		return err
	}

	if len(r.IngressConfig.TlsSecretName) != 0 {
		ingress.Spec.TLS = []networkingv1.IngressTLS{
			{
				Hosts:      []string{r.IngressConfig.Host},
				SecretName: r.IngressConfig.TlsSecretName,
			},
		}
	}

	log.Info("start create ingress")
	if err := r.Create(ctx, ingress); err != nil {
		log.Error(err, "create ingress error")
		return err
	}
	log.Info("create ingress success")

	if err := r.GetTensorboardURL(ctx, tb); err != nil {
		log.Error(err, "get ingress url error")
		return err
	}

	return nil
}

func (r *TensorboardReconciler) CreatePod(ctx context.Context, tb *systemv1alpha1.Tensorboard) (*corev1.Pod, error) {
	log := log.FromContext(ctx)

	tmpTensorboardDir := TensorBoardMountPathDir

	// tmpTensorboardDir := filepath.Join("/tmp", TensorBoardMountPathDir)
	// mergeLogsCmd := `source_dir="` + tmpTensorboardDir + `"; target_dir="` + TensorBoardMountPathDir + `"; mkdir -p "$target_dir"; arrays=($(find "$source_dir" -type f -name "events.out.tfevents.*")); ppathlist_paths=(); ppathlist_counts=(); for a in "${arrays[@]}"; do depth=$(echo "$a" | awk -F'/' '{print NF}'); username=$(echo "$a" | cut -d'/' -f4); jobname=$(echo "$a" | cut -d'/' -f5); tfname=$(basename "$a"); if [ "$depth" -lt 7 ]; then mkdir -p "$target_dir/$username/$jobname"; ln -s "$a" "$target_dir/$username/$jobname/$tfname"; else ParentPath=$(dirname "$(dirname "$a")"); found=false; for i in "${!ppathlist_paths[@]}"; do if [ "${ppathlist_paths[$i]}" = "$ParentPath" ]; then ppathlist_counts[$i]=$((ppathlist_counts[$i]+1)); found=true; break; fi; done; if [ "$found" = false ]; then ppathlist_paths+=("$ParentPath"); ppathlist_counts+=(1); fi; fi; done; for a in "${arrays[@]}"; do depth=$(echo "$a" | awk -F'/' '{print NF}'); if [ "$depth" -gt 6 ]; then username=$(echo "$a" | cut -d'/' -f4); jobname=$(echo "$a" | cut -d'/' -f5); tfname=$(basename "$a"); ParentPath=$(dirname "$(dirname "$a")"); parent=$(basename "$(dirname "$a")"); for i in "${!ppathlist_paths[@]}"; do if [ "${ppathlist_paths[$i]}" = "$ParentPath" ]; then if [ "${ppathlist_counts[$i]}" -eq 1 ]; then mkdir -p "$target_dir/$username/$jobname"; ln -s "$a" "$target_dir/$username/$jobname/$tfname"; else mkdir -p "$target_dir/$username/$jobname/$parent"; ln -s "$a" "$target_dir/$username/$jobname/$parent/$tfname"; fi; break; fi; done; fi; done`

	tensorboardCmd := "tensorboard --logdir=" + TensorBoardMountPathDir + " --host=0.0.0.0"

	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			GenerateName: TensorbordkPrefix + tb.Name + "-",
			Namespace:    tb.Namespace,
			Labels: map[string]string{
				FromLabel: TensorbordkPrefix + tb.Name,
			},
		},
		Spec: corev1.PodSpec{
			ActiveDeadlineSeconds: pointer.Int64(int64(tb.Spec.MaxRunningTimeMinutes) * 60),
			Containers: []corev1.Container{
				{
					Name:            "tensorboard",
					Image:           tb.Spec.ImageUrl, // 用指定的镜像
					ImagePullPolicy: "IfNotPresent",
					Ports: []corev1.ContainerPort{
						{
							Protocol:      corev1.ProtocolSCTP,
							ContainerPort: ExposedPort,
						},
					},
					Command: []string{"/bin/bash", "-c"},

					LivenessProbe: &corev1.Probe{
						ProbeHandler: corev1.ProbeHandler{
							HTTPGet: &corev1.HTTPGetAction{
								Path: "/",
								Port: intstr.IntOrString{IntVal: ExposedPort},
							},
						},
						InitialDelaySeconds: 5,
						TimeoutSeconds:      10,
					},
				},
			},
		},
	}
	pod.Spec.Containers[0].Args = []string{
		tensorboardCmd,
	}

	dataSourceNameList := []string{}
	for _, ds := range tb.Spec.DataSources {

		mountPath := filepath.Join(tmpTensorboardDir, ds.UserName, ds.JobName)

		pod.Spec.Containers[0].VolumeMounts = append(pod.Spec.Containers[0].VolumeMounts, corev1.VolumeMount{
			Name:      ds.DataSource.Name,
			MountPath: mountPath,
			SubPath:   ds.DataSource.VolumeSubPath,
		})

		if !IsDataSoureNameExist(dataSourceNameList, ds.DataSource.Name) {
			dataSourceNameList = append(dataSourceNameList, ds.DataSource.Name)

			pod.Spec.Volumes = append(pod.Spec.Volumes, corev1.Volume{
				Name: ds.DataSource.Name,
				VolumeSource: corev1.VolumeSource{
					PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
						ClaimName: ds.DataSource.VolumeName,
					},
				},
			})
		}

	}

	pod.Annotations = map[string]string{
		vc.StorageManagedAnnotationKey: vc.StorageManagedAnnotationValue,
	}

	// 定义首选节点的权重和条件
	// preferredSchedulingTerms := []corev1.PreferredSchedulingTerm{
	// 	{
	// 		Weight: 100,
	// 		Preference: corev1.NodeSelectorTerm{
	// 			MatchExpressions: []corev1.NodeSelectorRequirement{
	// 				{
	// 					Key:      r.ServerConfig.ServeNodeLabel,
	// 					Operator: corev1.NodeSelectorOpIn,
	// 					Values:   []string{"true"},
	// 				},
	// 			},
	// 		},
	// 	},
	// }
	// pod.Spec.Affinity = &corev1.Affinity{
	// 	NodeAffinity: &corev1.NodeAffinity{
	// 		PreferredDuringSchedulingIgnoredDuringExecution: preferredSchedulingTerms,
	// 	},
	// }
	pod.Spec.NodeSelector = map[string]string{
		r.ServerConfig.ServeNodeLabel: "true",
	}

	limits := corev1.ResourceList{
		corev1.ResourceCPU:    resource.MustParse("1"),
		corev1.ResourceMemory: resource.MustParse("1Gi"),
	}

	requests := corev1.ResourceList{
		corev1.ResourceCPU:    resource.MustParse("200m"),
		corev1.ResourceMemory: resource.MustParse("200Mi"),
	}

	if r.ServerConfig.PodResources.TensorboardResources != nil {
		limits = corev1.ResourceList{
			corev1.ResourceCPU:    resource.MustParse(r.ServerConfig.PodResources.TensorboardResources.Limits.CPU),
			corev1.ResourceMemory: resource.MustParse(r.ServerConfig.PodResources.TensorboardResources.Limits.Memory),
		}

		requests = corev1.ResourceList{
			corev1.ResourceCPU:    resource.MustParse(r.ServerConfig.PodResources.TensorboardResources.Requests.CPU),
			corev1.ResourceMemory: resource.MustParse(r.ServerConfig.PodResources.TensorboardResources.Requests.Memory),
		}

	}

	pod.Spec.Containers[0].Resources = corev1.ResourceRequirements{
		Limits:   limits,
		Requests: requests,
	}

	// tb.Spec.Resources = limits

	log.Info("set reference")
	if err := controllerutil.SetControllerReference(tb, pod, r.Scheme); err != nil {
		log.Error(err, "SetControllerReference error")
		return nil, err
	}

	log.Info("start create pod")
	if err := r.Create(ctx, pod); err != nil {
		log.Error(err, "create pod error")
		return nil, err
	}
	log.Info("create pod success")

	return pod, nil

}

// SetupWithManager sets up the controller with the Manager.
func (r *TensorboardReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.Tensorboard{}).
		Owns(&corev1.Pod{}).
		Owns(&networkingv1.Ingress{}).
		// WithEventFilter(NewtensorboardPredicate()).
		Complete(r)
}

func IsDataSoureNameExist(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}
