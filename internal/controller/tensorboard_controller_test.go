package controller

import (
	"context"
	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"testing"
)

func TestConstants(t *testing.T) {
	// Test constants
	assert.Equal(t, "system.hero.ai/finalizer", Finalizer)
	assert.Equal(t, "system.hero.ai/from", FromLabel)
	assert.Equal(t, "command.system.hero.ai", AnnotationKeyCommand)
	assert.Equal(t, "stop", CommandStop)
	assert.Equal(t, 6006, ExposedPort)
	assert.Equal(t, 80, ServiceTargetPort)
	assert.Equal(t, "tensorbord-", TensorbordkPrefix)
	assert.Equal(t, "/tensorboard", TensorBoardMountPathDir)
}

func TestReconcile(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)
	_ = networkingv1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: record.NewFakeRecorder(10),
	}
	req := ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-tensorboard",
			Namespace: "test-ns",
		},
	}
	// 测试当没有找到 Tensorboard 对象时
	_, err := r.Reconcile(context.TODO(), req)
	assert.NoError(t, err)

	// 创建一个 Tensorboard 对象并测试
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}
	err = r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	_, err = r.Reconcile(context.TODO(), req)
	assert.NoError(t, err)
}

func TestInitTensorboardStatus(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	err = r.initTensorboardStatus(context.TODO(), tensorboard)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.TBPending, tensorboard.Status.State.Phase)
}

func TestCheckTensorboard(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: record.NewFakeRecorder(10),
	}
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
			//Annotations: map[string]string{AnnotationKeyCommand: CommandStop},
		},
		Spec: systemv1alpha1.TensorboardSpec{
			MaxRunningTimeMinutes: 1,
		},
		Status: systemv1alpha1.TensorboardStatus{
			StartTime: metav1.Now(),
			State: systemv1alpha1.TensorboardState{
				Phase: systemv1alpha1.TBRunning,
			},
		},
	}
	var podList corev1.PodList
	err := r.CheckTensorboard(context.TODO(), ctrl.Request{}, tensorboard, podList)
	assert.NoError(t, err)
}

func TestSyncTensorboardPhaseIfPodStoped(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: record.NewFakeRecorder(10),
	}
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	err = r.SyncTensorboardPhaseIfPodStoped(context.TODO(), tensorboard)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.TBStopped, tensorboard.Status.State.Phase)
}

func TestSyncTensorboardPhaseIfPodRunning(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)
	_ = networkingv1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: record.NewFakeRecorder(10),
	}
	pod := &corev1.Pod{
		Status: corev1.PodStatus{
			ContainerStatuses: []corev1.ContainerStatus{
				{
					Name:  "tensorboard",
					Ready: true,
				},
			},
		},
	}
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
		Status: systemv1alpha1.TensorboardStatus{
			State: systemv1alpha1.TensorboardState{
				Phase: systemv1alpha1.TBRunning,
			},
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	ingress := &networkingv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      TensorbordkPrefix + "ingress-" + tensorboard.Name,
			Namespace: tensorboard.Namespace,
		},
	}
	err = r.Create(context.TODO(), ingress)
	assert.NoError(t, err)

	err = r.SyncTensorboardPhaseIfPodRunning(context.TODO(), pod, tensorboard)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.TBRunning, tensorboard.Status.State.Phase)
}

func TestSyncTensorboardPhaseIfPodStoping(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: record.NewFakeRecorder(10),
	}
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	err = r.SyncTensorboardPhaseIfPodStoping(context.TODO(), tensorboard)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.TBStopping, tensorboard.Status.State.Phase)
}

func TestGetTensorboardURL(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: record.NewFakeRecorder(10),
	}
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	err = r.GetTensorboardURL(context.TODO(), tensorboard)
	assert.NoError(t, err)
	assert.NotEmpty(t, tensorboard.Status.TensorboardUrl)
}

func TestDeleteTensorboardResources(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: record.NewFakeRecorder(10),
	}
	pods := []corev1.Pod{{ObjectMeta: metav1.ObjectMeta{Name: "test-pod", Namespace: "test-ns"}}}

	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
		Status: systemv1alpha1.TensorboardStatus{
			State: systemv1alpha1.TensorboardState{
				Phase: systemv1alpha1.TBStopping,
			},
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	err = r.DeleteTensorboardResources(context.TODO(), pods, tensorboard)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.TBStopping, tensorboard.Status.State.Phase)
}

func TestCreateTensorboardResources(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)
	_ = networkingv1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:       k8sFakeClient,
		Scheme:       scheme,
		Recorder:     record.NewFakeRecorder(10),
		ServerConfig: config.ServerConfig{ServeNodeLabel: "test-label"},
	}

	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
		Spec: systemv1alpha1.TensorboardSpec{
			ImageUrl: "test-image",
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	err = r.CreateTensorboardResources(context.TODO(), ctrl.Request{}, tensorboard)
	assert.NoError(t, err)

	// 验证 Pod、Service 和 Ingress 是否被创建
	// 获取所有的 Pod 对象
	podList := &corev1.PodList{}
	err = k8sFakeClient.List(context.TODO(), podList)
	assert.NoError(t, err)
	// 遍历找到对应的 Pod
	var createdPod *corev1.Pod
	for _, pod := range podList.Items {
		if pod.Labels[FromLabel] == "tensorbord-"+tensorboard.Name {
			createdPod = &pod
			break
		}
	}
	assert.NotNil(t, createdPod)

	service := &corev1.Service{}
	err = r.Get(context.TODO(), types.NamespacedName{
		Name:      TensorbordkPrefix + "service-" + tensorboard.Name,
		Namespace: tensorboard.Namespace,
	}, service)
	assert.NoError(t, err)

	ingress := &networkingv1.Ingress{}
	err = r.Get(context.TODO(), types.NamespacedName{
		Name:      TensorbordkPrefix + "ingress-" + tensorboard.Name,
		Namespace: tensorboard.Namespace,
	}, ingress)
	assert.NoError(t, err)
}

func TestCreateService(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: record.NewFakeRecorder(10),
	}

	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	pod := &corev1.Pod{ObjectMeta: metav1.ObjectMeta{Name: "test-pod", Namespace: "default"}}
	err = r.CreateService(context.TODO(), tensorboard, pod)
	assert.NoError(t, err)
}

func TestCreateIngress(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)
	_ = networkingv1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:        k8sFakeClient,
		Scheme:        scheme,
		Recorder:      record.NewFakeRecorder(10),
		IngressConfig: config.IngressConfig{Host: "test-host", TlsSecretName: "test-secret"},
	}

	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), tensorboard)
	assert.NoError(t, err)

	pod := &corev1.Pod{ObjectMeta: metav1.ObjectMeta{Name: "test-pod", Namespace: "default"}}
	err = r.CreateIngress(context.TODO(), tensorboard, pod)
	assert.NoError(t, err)
	assert.NotEmpty(t, tensorboard.Status.TensorboardUrl)
}

func TestCreatePod(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := TensorboardReconciler{
		Client:       k8sFakeClient,
		Scheme:       scheme,
		Recorder:     record.NewFakeRecorder(10),
		ServerConfig: config.ServerConfig{ServeNodeLabel: "test-label"},
	}
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
		Spec: systemv1alpha1.TensorboardSpec{
			ImageUrl: "test-image",
		},
	}

	pod, err := r.CreatePod(context.TODO(), tensorboard)
	assert.NoError(t, err)
	assert.NotNil(t, pod)
	assert.Equal(t, "tensorbord-"+tensorboard.Name+"-", pod.GenerateName)
	assert.Equal(t, "tensorboard", pod.Spec.Containers[0].Name)
	assert.Equal(t, tensorboard.Spec.ImageUrl, pod.Spec.Containers[0].Image)
	assert.Equal(t, "tensorboard --logdir=/tensorboard --host=0.0.0.0", pod.Spec.Containers[0].Args[0])
}

func TestIsDataSoureNameExist(t *testing.T) {
	slice := []string{"name1", "name2"}
	assert.False(t, IsDataSoureNameExist(slice, "name3"))
	assert.True(t, IsDataSoureNameExist(slice, "name1"))
}

//import (
//	"context"
//	"time"
//
//	. "github.com/onsi/ginkgo/v2"
//	. "github.com/onsi/gomega"
//	corev1 "k8s.io/api/core/v1"
//	networkingv1 "k8s.io/api/networking/v1"
//	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
//	"k8s.io/apimachinery/pkg/types"
//	"sigs.k8s.io/controller-runtime/pkg/client"
//
//	"hero.ai/hero-controllers/api/v1alpha1"
//)
//
//var _ = Describe("Tensorboard controller", func() {
//
//	// Define utility constants for object names and testing timeouts/durations and intervals.
//	const (
//		tensorbordName      = "test-controller-tensorbord"
//		tensorbordNamespace = "default"
//
//		timeout  = time.Second * 10
//		duration = time.Second * 10
//		interval = time.Millisecond * 250
//	)
//
//	Context("When updating Tensorboard Status", func() {
//		It("Should increase Tensorboard Status.Active count when new Jobs are created", func() {
//			By("By creating a new Tensorboard")
//			ctx := context.Background()
//			tensorbord := &v1alpha1.Tensorboard{
//				TypeMeta: metav1.TypeMeta{
//					APIVersion: "system.hero.ai/v1alpha1",
//					Kind:       "Tensorboard",
//				},
//				ObjectMeta: metav1.ObjectMeta{
//					Name:      tensorbordName,
//					Namespace: tensorbordNamespace,
//				},
//				Spec: v1alpha1.TensorboardSpec{
//					ImageUrl: "registry.bitahub.com:5000/leinaoyun/tensorboard:2.10.1-tf2.10.0-py39",
//					DataSources: []v1alpha1.DataItem{
//						{
//							JobName:  "test",
//							UserName: "test",
//							DataSource: v1alpha1.DataSource{
//								MountPath: "/data/log",
//							},
//						},
//					},
//					MaxRunningTimeMinutes: 1,
//				},
//			}
//			Expect(k8sClient.Create(ctx, tensorbord)).Should(Succeed()) // 创建tensorbord资源
//			tensorbordLookupKey := types.NamespacedName{Name: tensorbordName, Namespace: tensorbordNamespace}
//			createdTensorbord := &v1alpha1.Tensorboard{}
//
//			By("获取 tensorboard 资源")
//			Eventually(func() bool { // 在时间段中按照时间间隔不断重试参数中的函数
//				err := k8sClient.Get(ctx, tensorbordLookupKey, createdTensorbord)
//				return err == nil
//			}, timeout, interval).Should(BeTrue())
//
//			By("查看pod资源是否生成")
//			Eventually(func() bool {
//				podList := &corev1.PodList{}
//				labelSelector := &metav1.LabelSelector{
//					MatchLabels: map[string]string{FromLabel: tensorbordName},
//				}
//				selector, _ := metav1.LabelSelectorAsSelector(labelSelector)
//				_ = k8sClient.List(ctx, podList, &client.ListOptions{LabelSelector: selector})
//				return len(podList.Items) == 1
//			}, timeout, interval).Should(BeTrue())
//
//			By("查看service资源是否生成")
//			Eventually(func() bool {
//				service := &corev1.Service{}
//				err := k8sClient.Get(ctx, types.NamespacedName{Name: tensorbord.Name + "-" + TensorbordkPrefix + "service", Namespace: tensorbordNamespace}, service)
//				return err == nil
//			}, timeout, interval).Should(BeTrue())
//
//			By("查看ingress资源是否生成")
//			Eventually(func() bool {
//				ingress := &networkingv1.Ingress{}
//				err := k8sClient.Get(ctx, types.NamespacedName{Name: tensorbord.Name + "-" + TensorbordkPrefix + "ingress", Namespace: tensorbord.Namespace}, ingress)
//				return err == nil
//			}, timeout, interval).Should(BeTrue())
//
//			By("测试主动打标签删除")
//			tensorbord.Annotations = map[string]string{
//				AnnotationKeyCommand: CommandStop,
//			}
//			k8sClient.Update(ctx, tensorbord)
//			Eventually(func() bool {
//				podList := &corev1.PodList{}
//				labelSelector := &metav1.LabelSelector{
//					MatchLabels: map[string]string{"from": tensorbordName},
//				}
//				selector, _ := metav1.LabelSelectorAsSelector(labelSelector)
//				_ = k8sClient.List(ctx, podList, &client.ListOptions{LabelSelector: selector})
//				return len(podList.Items) == 1
//			}, timeout, interval).Should(BeTrue())
//
//			By("测试定时删除")
//			k8sClient.Create(ctx, tensorbord)
//			time.Sleep(time.Minute * time.Duration(tensorbord.Spec.MaxRunningTimeMinutes))
//			Eventually(func() bool {
//				podList := &corev1.PodList{}
//				labelSelector := &metav1.LabelSelector{
//					MatchLabels: map[string]string{"from": tensorbordName},
//				}
//				selector, _ := metav1.LabelSelectorAsSelector(labelSelector)
//				_ = k8sClient.List(ctx, podList, &client.ListOptions{LabelSelector: selector})
//				return len(podList.Items) == 1
//			}, timeout, interval).Should(BeTrue())
//		})
//	})
//})
