//nolint:all
package config

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"

	jobconfig "gitlab.bitahub.com/hero-os/hero-os-util/config"
)

var (
	svcDataId  = "job-controllers-config.yaml"
	secretName = "image-secret"
	namespace  = "hero-user"
)

const (
	DefaultFailedJobLatestLogs = 1000
)

// 本地调试设置的配置文件路径,也可将svcDataId视作文件位置
// var (
// 	cfgFile1 string = "/job-controllers/configs/job-controllers-config.yaml"
// )

var (
	cfg *jobconfig.Config
	SC  = &ServerConfig{}
	EC  = &EnvConfig{}
)

type ServerConfig struct {
	Namespace            string          `yaml:"Namespace"`
	ImageBuildMaxTime    string          `yaml:"ImageBuildMaxTime"`
	LocalURL             string          `yaml:"LocalURL"`
	TokenPath            string          `yaml:"TokenPath"`
	ToolImage            string          `yaml:"ToolImage"`
	LicenseURL           string          `yaml:"LicenseURL"`
	ServeNodeLabel       string          `yaml:"ServeNodeLabel"`
	PodResources         PodResources    `yaml:"PodResources"`
	HarborAPIProtocol    string          `yaml:"HarborAPIProtocol"`
	ResourceRecycle      ResourceRecycle `yaml:"ResourceRecycle"`
	InitSupervisorImage  string          `yaml:"initSupervisorImage"`
	SharedVolumeHostPath string          `yaml:"sharedVolumeHostPath"`
	GitImage             string          `yaml:"gitImage"`
	LokiURL              string          `yaml:"LokiURL"`
	ImageDelayTime       int             `yaml:"ImageDelayTime"`
	FaultTolerance       FaultTolerance  `yaml:"FaultTolerance"`
	RDMA                 RDMA            `yaml:"rdma"`
	LocalDiskHostPath    string          `yaml:"LocalDiskHostPath"`
}

type RDMA struct {
	SecondaryNetwork string   `yaml:"secondaryNetwork"`
	NCCLEnvs         []string `yaml:"ncclEnvs"`
	HCCLEnvs         []string `yaml:"hcclEnvs"`
}

func (s *ServerConfig) getNamespace() string {
	if len(s.Namespace) > 0 {
		return s.Namespace
	}

	return namespace
}

type PodResources struct {
	TensorboardResources *ResourceRequirements `yaml:"TensorboardResources"`
	ImagemakerResources  *ResourceRequirements `yaml:"ImagemakerResources"`
}

type ResourceRequirements struct {
	Limits   ResourceList `yaml:"limits"`
	Requests ResourceList `yaml:"requests"`
}

type ResourceList struct {
	CPU    string `yaml:"cpu"`
	Memory string `yaml:"memory"`
}

type ResourceRecycle struct {
	IsRecycle    bool              `yaml:"IsRecycle"`
	FilterLabels map[string]string `yaml:"FilterLabels"`
	Resource     struct {
		SyncAction struct {
			RecycletimeHour int `yaml:"RecycletimeHour"`
			DelayTimeHour   int `yaml:"DelayTimeHour"`
		} `yaml:"Syncaction"`
		Trainingjob struct {
			RecycletimeHour int `yaml:"RecycletimeHour"`
			DelayTimeHour   int `yaml:"DelayTimeHour"`
		} `yaml:"Trainingjob"`
		Notebook struct {
			RecycletimeHour int `yaml:"RecycletimeHour"`
			DelayTimeHour   int `yaml:"DelayTimeHour"`
		} `yaml:"Notebook"`
		Imagemaker struct {
			RecycletimeHour int `yaml:"RecycletimeHour"`
			DelayTimeHour   int `yaml:"DelayTimeHour"`
		} `yaml:"Imagemaker"`
		Tensorboard struct {
			RecycletimeHour int `yaml:"RecycletimeHour"`
			DelayTimeHour   int `yaml:"DelayTimeHour"`
		} `yaml:"Tensorboard"`
	} `yaml:"Resource"`
}

type EnvConfig struct {
	Ingress    IngressConfig `yaml:"Ingress"`
	Image      ImageConfig   `yaml:"Image"`
	VncBaseURL string        `yaml:"VncBaseURL"`
	RSA        RSAInfo       `yaml:"RSA"`
	MinioS3    *MinioConfig  `json:"s3,omitempty" yaml:"S3"  mapstructure:"S3"`
	MinioFS    *MinioConfig  `json:"fileSystem,omitempty" yaml:"FileSystem"  mapstructure:"FileSystem"`
	HfEndpoint string        `yaml:"Gitee_Endpoint"`
}

type MinioConfig struct {
	Url       string `yaml:"URL"`
	Https     bool   `yaml:"HTTPS"`
	AccessKey string `yaml:"ACCESS_KEY"  mapstructure:"ACCESS_KEY"`
	SecretKey string `yaml:"SECRET_KEY" mapstructure:"SECRET_KEY"`
}

type IngressConfig struct {
	Domain        string `yaml:"Domain"`
	Host          string `yaml:"Host"`
	TlsSecretName string `yaml:"TlsSecretName"`
}

type ImageConfig struct {
	ImageRepo      string `yaml:"ImageRepo"`
	Domain         string `yaml:"Domain"`
	UserName       string `yaml:"UserName"`
	Password       string `yaml:"Password"`
	MaxTaskPerNode int    `yaml:"MaxTaskPerNode"`
}

type RSAInfo struct {
	Public_Key  string `yaml:"PUBLIC_KEY"`
	Private_Key string `yaml:"PRIVATE_KEY"`
}

type FaultTolerance struct {
	// 当 trainingjob 健康检查 FailedJobRestartableCheck 的 policy 为 Advanced 时，运行失败的 job 是否可以重启的条件
	FailedJobRestart FailedJobRestart `yaml:"FailedJobRestart"`
	// 健康检查容器镜像
	HealthCheckImage string `yaml:"HealthCheckImage"`
}

type FailedJobRestart struct {
	// 如果用户容器的 exitCode 在 UnrestartableExitCodeRanges 范围内，则不能重启。
	// UnrestartableExitCodeRanges[*][0] 表示 exitCode 起始值，UnrestartableExitCodeRanges[*][1] 表示 exitCode 结束值。
	UnrestartableExitCodeRanges [][]int32 `yaml:"UnrestartableExitCodeRanges"`
	// 分析失败任务的最后 LatestLogs 条日志判断是否可以重启
	LatestLogs *int `yaml:"LatestLogs"`
	// 如果失败的任务符合任意一条 Policies 中的规则，则可以重启。优先级低于 UnrestartableExitCodeRanges 字段。
	Policies []FailedJobRestartPolicy `yaml:"Policies"`
}

type FailedJobRestartPolicy struct {
	// 运行失败的 Pod 的 exitCode，如果为空，则不需要校验 exitCode 的具体值
	ExitCode *int32 `yaml:"ExitCode"`
	// 当一行日志同时出现 LogExps 所有的字符串时，则符合重启规则
	LogExps []string `yaml:"LogExps"`
}

func CreateOrUpdateResource(client client.Client) error {
	return EC.createOrUpdateResource(client)
}

func (ecf *EnvConfig) createOrUpdateResource(client client.Client) error {
	var secret v1.Secret
	err := client.Get(context.TODO(), types.NamespacedName{
		Name:      secretName,
		Namespace: SC.Namespace,
	}, &secret)
	auth := base64Encode(fmt.Sprintf("%s:%s", EC.Image.UserName, EC.Image.Password))
	value := fmt.Sprintf(`{"auths":{"%s":{"username":"%s","password":"%s","auth":"%s"}}}`, EC.Image.Domain, EC.Image.UserName, EC.Image.Password, string(auth))
	if err != nil {
		secret = v1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      secretName,
				Namespace: SC.getNamespace(),
			},
			Data: map[string][]byte{
				v1.DockerConfigJsonKey: []byte(value),
			},
			Type: v1.SecretTypeDockerConfigJson,
		}
		client.Delete(context.TODO(), &secret)
		return client.Create(context.TODO(), &secret)
	}

	secretPara := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      secretName,
			Namespace: SC.Namespace,
		},
		Data: map[string][]byte{
			v1.DockerConfigJsonKey: []byte(value),
		},
		Type: v1.SecretTypeDockerConfigJson,
	}

	return client.Update(context.TODO(), secretPara)
}

func base64Encode(data string) []byte {
	n := base64.StdEncoding.EncodedLen(len(data))
	dst := make([]byte, n)
	base64.StdEncoding.Encode(dst, []byte(data))
	return dst
}

func init() {
	cfg = jobconfig.NewConfig(jobconfig.SetConfig(
		jobconfig.SetNacosConfig(os.Getenv("NACOS_SERVER_URL"), os.Getenv("JASYPT_ENCRYPTOR_PASSWORD"), os.Getenv("INIT_MODE")),
		jobconfig.SetConfigSource(svcDataId, SC),
		jobconfig.SetCommonConfig(EC),
	))

	if err := cfg.InitConfig(); err != nil {
		panic(err)
	}
}
