package imagemaker

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func TestHandleNewImageMaker(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, systemv1alpha1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:      k8sFakeClient,
		Scheme:      scheme,
		Recorder:    nil,
		ImageConfig: config.ImageConfig{MaxTaskPerNode: 1},
		RecordNodeTaskQueue: &RecordNodeTaskQueue{
			NodeQueue: map[string]*NodeTaskQueue{},
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{Name: "test-image", Namespace: "default"},
		Spec:       systemv1alpha1.ImageMakerSpec{Source: systemv1alpha1.Source{Type: systemv1alpha1.UrlDockerfile}},
		Status:     systemv1alpha1.ImageMakerStatus{State: systemv1alpha1.ImageMakerState{Phase: systemv1alpha1.IMPending}},
	}

	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1"}}

	require.NoError(t, r.Create(context.TODO(), imageMaker))
	require.NoError(t, r.Create(context.TODO(), node))

	// 初始化任务队列
	r.RecordNodeTaskQueue.NodeQueue["node-1"] = &NodeTaskQueue{CpuArch: "amd64", Tasks: map[string]struct{}{}}

	// 执行 `handleNewImageMaker`
	result, err := r.handleNewImageMaker(context.TODO(), imageMaker)

	// 断言：应成功选择 node-1
	assert.NoError(t, err)
	assert.False(t, result.Requeue)
}

func TestUpdateIfToolPodEnd(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, systemv1alpha1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		RecordNodeTaskQueue: &RecordNodeTaskQueue{
			NodeQueue: map[string]*NodeTaskQueue{},
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{Name: "test-image", Namespace: "default"},
		Status:     systemv1alpha1.ImageMakerStatus{NodeName: "node-1", State: systemv1alpha1.ImageMakerState{Phase: systemv1alpha1.IMPending}},
	}

	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{Name: "test-image-imagemaker", Namespace: "default"},
		Status:     corev1.PodStatus{Phase: corev1.PodSucceeded},
	}

	require.NoError(t, r.Create(context.TODO(), imageMaker))
	require.NoError(t, r.Create(context.TODO(), pod))

	// 执行更新
	err := r.updateIfToolPodEnd(context.TODO(), imageMaker)

	// 断言：状态应更新为 IMSucceeded
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.IMSucceeded, imageMaker.Status.State.Phase)
}

func TestErrorHandler(t *testing.T) {
	r := &ImageMakerReconciler{}
	conflictErr := k8serrors.NewConflict(schema.GroupResource{Group: "test", Resource: "resource"}, "test", fmt.Errorf("conflict"))

	result, err := r.errorHandler(conflictErr)
	assert.NoError(t, err)
	assert.True(t, result.Requeue)

	normalErr := fmt.Errorf("some other error")
	result, err = r.errorHandler(normalErr)
	assert.Error(t, err)
	assert.Equal(t, normalErr, err)
}

func TestUpdateStatus(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, systemv1alpha1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		RecordNodeTaskQueue: &RecordNodeTaskQueue{
			NodeQueue: map[string]*NodeTaskQueue{},
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{Name: "test-image", Namespace: "default"},
		Status:     systemv1alpha1.ImageMakerStatus{},
	}

	require.NoError(t, r.Create(context.TODO(), imageMaker))

	err := r.UpdateStatus(context.TODO(), imageMaker, systemv1alpha1.IMFailed, "node-1", "Error reason", "Error message")

	// 重新获取对象并验证状态
	updatedImageMaker := &systemv1alpha1.ImageMaker{}
	require.NoError(t, r.Get(context.TODO(), client.ObjectKey{Namespace: "default", Name: "test-image"}, updatedImageMaker))

	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.IMFailed, updatedImageMaker.Status.State.Phase)
	assert.Equal(t, "node-1", updatedImageMaker.Status.NodeName)
	assert.Equal(t, "Error reason", updatedImageMaker.Status.State.Reason)
	assert.Equal(t, "Error message", updatedImageMaker.Status.State.Message)
}

func TestImagemakerControllerReconcile(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	req := ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-imagemaker",
			Namespace: "default",
		},
	}
	result, err := r.Reconcile(context.TODO(), req)
	if err != nil {
		t.Errorf("Reconcile returned an error: %v", err)
	}
	if result != (ctrl.Result{}) {
		t.Errorf("Expected empty Result, got %v", result)
	}
}

func TestInitQueue(t *testing.T) {
	// **1. 初始化 Scheme**
	scheme := runtime.NewScheme()
	_ = corev1.AddToScheme(scheme)
	_ = systemv1alpha1.AddToScheme(scheme)

	// **2. 构造 fake Kubernetes 客户端**
	fakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	// **3. 创建 ImageMakerReconciler**
	r := &ImageMakerReconciler{
		Client: fakeClient,
	}

	// **4. 创建 Pod，模拟不同的状态**
	pod1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-imagemaker",
			Namespace: "default",
			Labels: map[string]string{
				ImagemakerLabelKey: ImageMakerLabelValue, // 确保 label 匹配
			},
		},
		Spec: corev1.PodSpec{
			NodeName: "node-1",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning, // Running 状态的 Pod
		},
	}

	pod2 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image2-imagemaker",
			Namespace: "default",
			Labels: map[string]string{
				ImagemakerLabelKey: ImageMakerLabelValue,
			},
		},
		Spec: corev1.PodSpec{
			NodeName: "node-2",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodSucceeded, // Succeeded 状态的 Pod (应被跳过)
		},
	}

	// **5. 创建 fake Pod**
	err := fakeClient.Create(context.TODO(), pod1)
	assert.NoError(t, err)
	err = fakeClient.Create(context.TODO(), pod2)
	assert.NoError(t, err)

	// **6. 运行 InitQueue**
	err = r.InitQueue(fakeClient, "default")
	assert.NoError(t, err)

	// **7. 验证 `RecordNodeTaskQueue`**
	assert.Contains(t, r.RecordNodeTaskQueue.NodeQueue, "node-1")    // 仅 Running 的 Pod 被记录
	assert.NotContains(t, r.RecordNodeTaskQueue.NodeQueue, "node-2") // Succeeded 的 Pod 应被跳过

	// **8. 额外测试：空 PodList**
	fakeEmptyClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	err = r.InitQueue(fakeEmptyClient, "default")
	assert.NoError(t, err)
	assert.Empty(t, r.RecordNodeTaskQueue.NodeQueue) // 为空时，不应有任务被添加

	// **9. 输出测试结果**
	fmt.Println("TestInitQueue passed!")
}

func TestSelectAvailableTrainingNode(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	imageMaker1 := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image",
			Namespace: "default",
		},
		Spec: systemv1alpha1.ImageMakerSpec{
			Source: systemv1alpha1.Source{Type: systemv1alpha1.UrlDockerfile},
		},
		Status: systemv1alpha1.ImageMakerStatus{
			State: systemv1alpha1.ImageMakerState{
				Phase: systemv1alpha1.IMPending,
			},
		},
	}
	node1 := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "node-1",
		},
	}

	err := r.Create(context.TODO(), imageMaker1)
	assert.NoError(t, err)

	err = r.Create(context.TODO(), node1)
	assert.NoError(t, err)

	r.RecordNodeTaskQueue = &RecordNodeTaskQueue{
		NodeQueue: map[string]*NodeTaskQueue{},
	}
	r.RecordNodeTaskQueue.NodeQueue["node-1"] = &NodeTaskQueue{
		CpuArch: "amd64",
		Tasks:   map[string]struct{}{},
	}
	nodeName, flag := r.selectAvailableTrainingNode(context.TODO(), imageMaker1, 1)

	fmt.Println("==>", nodeName, flag)
	if flag == false && nodeName != "node-1" {
		t.Errorf("Expected node: node-1, got %v", nodeName)
	}
}

func TestStopBuildImage(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
		Status: systemv1alpha1.ImageMakerStatus{
			State: systemv1alpha1.ImageMakerState{
				Phase: systemv1alpha1.IMRunning,
			},
		},
	}
	err := r.Create(context.TODO(), imageMaker)
	assert.NoError(t, err)

	err = r.StopBuildImage(context.TODO(), imageMaker)
	if err != nil {
		t.Errorf("StopBuildImage returned an error: %v", err)
	}
}

func TestConvertKubeContainerIDToDocker(t *testing.T) {
	dockerID, err := ConvertKubeContainerIDToDocker("docker://test-container-id")
	if err != nil {
		t.Errorf("convertKubeContainerIDToDocker returned an error: %v", err)
	}
	if dockerID != "test-container-id" {
		t.Errorf("Expected test-container-id, got %v", dockerID)
	}

	_, err = ConvertKubeContainerIDToDocker("invalid-container-id")
	if err == nil {
		t.Errorf("Expected an error for invalid container ID")
	}
}

func TestGetPodParam(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
		Spec: systemv1alpha1.ImageMakerSpec{
			Source: systemv1alpha1.Source{
				Type: systemv1alpha1.PublicImage,
			},
		},
	}
	err := r.Create(context.TODO(), imageMaker)
	assert.NoError(t, err)

	toolpodparam, err := r.getPodParam(context.TODO(), imageMaker)
	if err != nil {
		t.Errorf("getPodParam returned an error: %v", err)
	}
	if toolpodparam == nil {
		t.Errorf("Expected a non-nil ToolPodParam")
	}
}

func TestInitPodParam(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
	}

	toolpodparam, err := r.InitPodParam(imageMaker, context.TODO())
	if err != nil {
		t.Errorf("InitPodParam returned an error: %v", err)
	}
	if toolpodparam == nil {
		t.Errorf("Expected a non-nil ToolPodParam")
	}
}

func TestCreateToolPod(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
		Spec: systemv1alpha1.ImageMakerSpec{
			Source: systemv1alpha1.Source{
				Type: systemv1alpha1.PublicImage,
			},
		},
	}
	err := r.Create(context.TODO(), imageMaker)
	assert.NoError(t, err)

	err = r.createToolPod(context.TODO(), imageMaker, "test")
	if err != nil {
		t.Errorf("createToolPod returned an error: %v", err)
	}
}

func TestFromPublicImagePodParam(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
		Spec: systemv1alpha1.ImageMakerSpec{
			Source: systemv1alpha1.Source{
				Type:           systemv1alpha1.PublicImage,
				SourceImageUrl: "public-image-url",
			},
		},
	}
	toolpodparam := &ToolPodParam{
		Name:         "test-pod",
		NameSpace:    "default",
		ImageUrl:     "test-image-url",
		ToolImage:    "test-tool-image",
		MakeType:     systemv1alpha1.PublicImage,
		BuildMaxTime: "1h",
		ImageRePoUrl: "test-repo.com",
	}
	err := r.FromPublicImagePodParam(toolpodparam, imageMaker, context.TODO())
	if err != nil {
		t.Errorf("FromPublicImagePodParam returned an error: %v", err)
	}
	if toolpodparam.Shell != PullPublicImageShell {
		t.Errorf("Expected Shell to be %s, got %s", PullPublicImageShell, toolpodparam.Shell)
	}
	if toolpodparam.PublicImage != "public-image-url" {
		t.Errorf("Expected PublicImage to be %s, got %s", "public-image-url", toolpodparam.PublicImage)
	}
	if len(toolpodparam.Envs) != 1 {
		t.Errorf("Expected 1 env var, got %d", len(toolpodparam.Envs))
	}
}

func TestFromDockerfileUrlPodParam(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
		Spec: systemv1alpha1.ImageMakerSpec{
			Source: systemv1alpha1.Source{
				Type:          systemv1alpha1.UrlDockerfile,
				DockerfileUrl: "dockerfile-url",
			},
		},
	}
	err := r.Create(context.TODO(), imageMaker)
	assert.NoError(t, err)

	toolpodparam := &ToolPodParam{
		Name:         "test-pod",
		NameSpace:    "default",
		ImageUrl:     "test-image-url",
		ToolImage:    "test-tool-image",
		MakeType:     systemv1alpha1.UrlDockerfile,
		BuildMaxTime: "1h",
		ImageRePoUrl: "test-repo.com",
	}
	err = r.FromDockerfileUrlPodParam(toolpodparam, imageMaker, context.TODO())
	if err != nil {
		t.Errorf("FromDockerfileUrlPodParam returned an error: %v", err)
	}
	if toolpodparam.Shell != BuildUrlDockerfileShell {
		t.Errorf("Expected Shell to be %s, got %s", BuildUrlDockerfileShell, toolpodparam.Shell)
	}
	if toolpodparam.Dockerfile != "dockerfile-url" {
		t.Errorf("Expected Dockerfile to be %s, got %s", "dockerfile-url", toolpodparam.Dockerfile)
	}
	if len(toolpodparam.Envs) != 1 {
		t.Errorf("Expected 1 env var, got %d", len(toolpodparam.Envs))
	}
}

func TestFromVolumePodParam(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
		Spec: systemv1alpha1.ImageMakerSpec{
			Source: systemv1alpha1.Source{
				Type:    systemv1alpha1.Dockerfile,
				CpuArch: "x86-64",
				DataSource: systemv1alpha1.DataSource{
					VolumeName:    "test-volume-name",
					VolumeSubPath: "test-volume-subpath",
				},
			},
		},
	}
	err := r.Create(context.TODO(), imageMaker)
	assert.NoError(t, err)

	toolpodparam := &ToolPodParam{
		Name:         "test-pod",
		NameSpace:    "default",
		ImageUrl:     "test-image-url",
		ToolImage:    "test-tool-image",
		MakeType:     systemv1alpha1.Dockerfile,
		BuildMaxTime: "1h",
		ImageRePoUrl: "test-repo.com",
	}
	err = r.FromVolumePodParam(toolpodparam, imageMaker, context.TODO())
	if err != nil {
		t.Errorf("FromVolumePodParam (Dockerfile case) returned an error: %v", err)
	}
	if toolpodparam.Shell != BuildDockerfileShell {
		t.Errorf("Expected Shell to be %s, got %s", BuildDockerfileShell, toolpodparam.Shell)
	}
	if toolpodparam.Volume == nil || toolpodparam.VolumeMount == nil {
		t.Errorf("Expected non-nil Volume and VolumeMount")
	}
	if toolpodparam.Dockerfile != "/mount/filedir/test-volume-subpath" {
		t.Errorf("Expected Dockerfile to be %s, got %s", "test-volume-subpath-mount-path", toolpodparam.Dockerfile)
	}
	fmt.Println("==> ", toolpodparam.CpuArch)
	if toolpodparam.CpuArch != "amd64" {
		t.Errorf("Expected CpuArch to be %s, got %s", "test-cpu-arch", toolpodparam.CpuArch)
	}
	if len(toolpodparam.Envs) != 3 {
		t.Errorf("Expected 3 env vars, got %d", len(toolpodparam.Envs))
	}

	imageMaker.Spec.Source.Type = systemv1alpha1.TarPkg
	toolpodparam.MakeType = systemv1alpha1.TarPkg
	err = r.FromVolumePodParam(toolpodparam, imageMaker, context.TODO())
	if err != nil {
		t.Errorf("FromVolumePodParam (TarPkg case) returned an error: %v", err)
	}
	if toolpodparam.Shell != LoadTarShell {
		t.Errorf("Expected Shell to be %s, got %s", LoadTarShell, toolpodparam.Shell)
	}
	if toolpodparam.Volume == nil || toolpodparam.VolumeMount == nil {
		t.Errorf("Expected non-nil Volume and VolumeMount")
	}
	if toolpodparam.MountTar != "/mount/filedir/test-volume-subpath" {
		t.Errorf("Expected MountTar to be %s, got %s", "test-volume-subpath-mount-path", toolpodparam.MountTar)
	}
	if len(toolpodparam.Envs) != 1 {
		t.Errorf("Expected 1 env var, got %d", len(toolpodparam.Envs))
	}
}

func TestFromNotebookPodParam(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      vcjobbuilder.VcjobNameNotebookPrefix + "test-container-name-test-container-name-0",
			Namespace: "default",
		},
		Spec: corev1.PodSpec{
			NodeName: "test-node-name",
		},
		Status: corev1.PodStatus{
			ContainerStatuses: []corev1.ContainerStatus{
				{
					Name:        "main",
					ContainerID: "docker://test-container-id",
					Ready:       true,
				},
			},
		},
	}
	err := r.Create(context.TODO(), pod)
	assert.NoError(t, err)

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
		Spec: systemv1alpha1.ImageMakerSpec{
			Source: systemv1alpha1.Source{
				Type: systemv1alpha1.Container,
				Containers: systemv1alpha1.Containers{
					Name: "test-container-name",
				},
			},
		},
	}
	err = r.Create(context.TODO(), imageMaker)
	assert.NoError(t, err)

	notebook := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      imageMaker.Spec.Source.Containers.Name,
			Namespace: imageMaker.Spec.Source.Containers.Namespace,
		},
	}
	err = r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	toolpodparam := &ToolPodParam{
		Name:         "test-pod",
		NameSpace:    "default",
		ImageUrl:     "test-image-url",
		ToolImage:    "test-tool-image",
		MakeType:     systemv1alpha1.Container,
		BuildMaxTime: "1h",
		ImageRePoUrl: "test-repo.com",
	}
	err = r.FromNotebookPodParam(toolpodparam, imageMaker, context.TODO())
	if err != nil {
		t.Errorf("FromNotebookPodParam returned an error: %v", err)
	}
	if toolpodparam.ContainerId == "" {
		t.Errorf("Expected non-empty ContainerId")
	}
	if toolpodparam.NodeName == "" {
		t.Errorf("Expected non-empty NodeName")
	}
	if len(toolpodparam.Envs) != 2 {
		t.Errorf("Expected 1 env var, got %d", len(toolpodparam.Envs))
	}
}

func TestGetNotebookpod(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = corev1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &ImageMakerReconciler{
		Client:   k8sFakeClient,
		Scheme:   scheme,
		Recorder: nil,
		ServerConfig: config.ServerConfig{

			HarborAPIProtocol: "http",
			PodResources: config.PodResources{
				ImagemakerResources: &config.ResourceRequirements{
					Limits: config.ResourceList{
						CPU:    "2",
						Memory: "2Gi",
					},
					Requests: config.ResourceList{
						CPU:    "1",
						Memory: "1Gi",
					},
				},
			},
		},
		ImageConfig: config.ImageConfig{
			UserName:  "test-user",
			Password:  "test-password",
			ImageRepo: "test-repo.com",
		},
	}

	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      vcjobbuilder.VcjobNameNotebookPrefix + "test-container-name-test-container-name-0",
			Namespace: "default",
		},
		Status: corev1.PodStatus{
			ContainerStatuses: []corev1.ContainerStatus{
				{
					Name:  "tensorboard",
					Ready: true,
				},
			},
		},
	}
	err := r.Create(context.TODO(), pod)
	assert.NoError(t, err)

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-image-maker",
			Namespace: "default",
		},
		Spec: systemv1alpha1.ImageMakerSpec{
			Source: systemv1alpha1.Source{
				Type: systemv1alpha1.Container,
				Containers: systemv1alpha1.Containers{
					Name: "test-container-name",
				},
			},
		},
	}
	err = r.Create(context.TODO(), imageMaker)
	assert.NoError(t, err)

	notebookpod, err := r.GetNotebookpod(imageMaker, context.TODO())
	if err != nil {
		t.Errorf("GetNotebookpod returned an error: %v", err)
	}
	if notebookpod == nil {
		t.Errorf("Expected a non-nil notebook pod")
	}
}

func TestNormalizeCpuArch(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"", "amd64"},        // 空字符串，默认应返回 amd64
		{"x86", "amd64"},     // 包含 "x86"，应返回 amd64
		{"x86_64", "amd64"},  // 包含 "x86"，应返回 amd64
		{"amd64", "amd64"},   // 明确指定 amd64，应返回 amd64
		{"arm64", "arm64"},   // 明确指定 arm64，应返回 arm64
		{"aarch64", "arm64"}, // 不匹配 "x86"，应返回 arm64
		{"ARM", "arm64"},     // 大写的 ARM，应返回 arm64
		{"ARMv8", "arm64"},   // 其他 ARM 相关的字符串，应返回 arm64
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result := NormalizeCpuArch(test.input)
			if result != test.expected {
				t.Errorf("输入: %q, 期望: %q, 实际: %q", test.input, test.expected, result)
			}
		})
	}
}
