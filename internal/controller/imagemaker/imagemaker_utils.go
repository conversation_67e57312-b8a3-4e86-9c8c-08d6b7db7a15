package imagemaker

import (
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type TaskState string

type NodeOption string

const (
	TaskCompleted        TaskState  = "Completed"  // 完成状态
	TaskDelete           TaskState  = "TaskDelete" // 完成状态
	TaskStarting         TaskState  = "Starting"   // 空状态
	NodeAdd              NodeOption = "NodeAdd"
	NodeRemove           NodeOption = "NodeRemove"
	ImagemakerLabelKey   string     = "app"
	ImageMakerLabelValue string     = "Imagemaker"
	StorageAnnotation    string     = "leinao.ai/storage-managed"
)

type ImageRepoCredentials struct {
	ImageRepo string
	UserName  string
	Passwd    string
}

func GetToolPodTemplate(imagecfg config.ImageConfig, svrcfg config.ServerConfig, toolpodparam *ToolPodParam) *corev1.Pod {

	toolpod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      toolpodparam.Name,
			Namespace: toolpodparam.NameSpace,
			Labels: map[string]string{
				ImagemakerLabelKey:                  ImageMakerLabelValue,
				systemv1alpha1.ImagemakerNameLabels: toolpodparam.ImageMakerName,
			},
			Annotations: map[string]string{
				StorageAnnotation: "true",
			},
		},
		Spec: corev1.PodSpec{
			NodeName:       toolpodparam.NodeName,
			InitContainers: []corev1.Container{},
			Containers: []corev1.Container{
				{
					Name:            "tool-container",
					Image:           toolpodparam.ToolImage,
					ImagePullPolicy: corev1.PullAlways,
					Command:         []string{"bash", toolpodparam.Shell},
					Env: []corev1.EnvVar{

						{
							Name:  "USERNAME",
							Value: imagecfg.UserName,
						},
						{
							Name:  "PASSWORD",
							Value: imagecfg.Password,
						},
						{
							Name:  "IMAGEURL",
							Value: toolpodparam.ImageUrl,
						}, {
							Name:  "IMAGEREPO",
							Value: toolpodparam.ImageRePoUrl,
						},
					},
					VolumeMounts: []corev1.VolumeMount{
						{
							Name:      "dockersock",
							MountPath: "/var/run/docker.sock",
						}, {
							Name:      "docker",
							MountPath: "/usr/bin/docker",
						},
					},
				},
			},
			Volumes: []corev1.Volume{
				{
					Name: "dockersock",
					VolumeSource: corev1.VolumeSource{
						HostPath: &corev1.HostPathVolumeSource{
							Path: "/var/run/docker.sock",
						},
					},
				}, {
					Name: "docker",
					VolumeSource: corev1.VolumeSource{
						HostPath: &corev1.HostPathVolumeSource{
							Path: "/usr/bin/docker",
						},
					},
				},
			},
			RestartPolicy: corev1.RestartPolicyNever,
		},
	}

	if toolpodparam.Envs != nil {
		toolpod.Spec.Containers[0].Env = append(toolpod.Spec.Containers[0].Env, toolpodparam.Envs...)
	}

	if toolpodparam.Volume != nil && toolpodparam.VolumeMount != nil {
		toolpod.Spec.Containers[0].VolumeMounts = append(toolpod.Spec.Containers[0].VolumeMounts, *toolpodparam.VolumeMount)
		toolpod.Spec.Volumes = append(toolpod.Spec.Volumes, *toolpodparam.Volume)
	}

	limits := corev1.ResourceList{
		corev1.ResourceCPU:    resource.MustParse("1"),
		corev1.ResourceMemory: resource.MustParse("1Gi"),
	}

	requests := corev1.ResourceList{
		corev1.ResourceCPU:    resource.MustParse("200m"),
		corev1.ResourceMemory: resource.MustParse("200Mi"),
	}

	if svrcfg.PodResources.ImagemakerResources != nil {
		limits = corev1.ResourceList{
			corev1.ResourceCPU:    resource.MustParse(svrcfg.PodResources.ImagemakerResources.Limits.CPU),
			corev1.ResourceMemory: resource.MustParse(svrcfg.PodResources.ImagemakerResources.Limits.Memory),
		}

		requests = corev1.ResourceList{
			corev1.ResourceCPU:    resource.MustParse(svrcfg.PodResources.ImagemakerResources.Requests.CPU),
			corev1.ResourceMemory: resource.MustParse(svrcfg.PodResources.ImagemakerResources.Requests.Memory),
		}

	}

	toolpod.Spec.Containers[0].Resources = corev1.ResourceRequirements{
		Limits:   limits,
		Requests: requests,
	}

	return toolpod

}

func QueryCpuArch(imageName string, imageRepoCredentials ImageRepoCredentials) (string, error) { //nolint

	parts := strings.SplitN(imageName, "/", 3)
	// Registry address is the first part
	// registry := parts[0]
	// Namespace is the second part
	projectName := url.QueryEscape(parts[1])
	// Image name with tag is the third part
	imageNameWithTag := parts[2]

	repositoryName := imageNameWithTag
	reference := "latest"

	if strings.Contains(imageNameWithTag, ":") {
		names := strings.SplitN(imageNameWithTag, ":", 2)

		repositoryName = url.QueryEscape(names[0])

		if strings.Contains(names[0], "/") {
			repositoryName = url.QueryEscape(url.QueryEscape(names[0]))
		}
		reference = url.QueryEscape(names[1]) // e.g., "latest" or the digest
	}

	url := fmt.Sprintf("%s/api/v2.0/projects/%s/repositories/%s/artifacts/%s", imageRepoCredentials.ImageRepo, projectName, repositoryName, reference)

	return Query(url, imageRepoCredentials)

}

func Query(requestUrl string, imageRepoCredentials ImageRepoCredentials) (string, error) { //nolint

	req, err := http.NewRequest("GET", requestUrl, nil)
	if err != nil {
		return "", err
	}

	req.SetBasicAuth(imageRepoCredentials.UserName, imageRepoCredentials.Passwd)
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	re := regexp.MustCompile(`"architecture":"([^"]+)"`)

	matches := re.FindStringSubmatch(string(body))

	if len(matches) > 1 {
		// 匹配组1是我们要找的值
		return matches[1], nil
	}
	return "", errors.New("no arch")

}

// 转换成docker 能识别的container ID
func ConvertKubeContainerIDToDocker(kubeContainerID string) (string, error) {

	if len(kubeContainerID) == 0 {
		return "", errors.New("no found containerID")
	}
	if strings.HasPrefix(kubeContainerID, "docker://") {
		return strings.TrimPrefix(kubeContainerID, "docker://"), nil
	}
	return kubeContainerID, errors.New("NotSupportRuntime")
}

type RecordNodeTaskQueue struct {
	sync.Mutex
	NodeQueue map[string]*NodeTaskQueue
}

type NodeTaskQueue struct {
	sync.Mutex
	CpuArch string
	Tasks   map[string]struct{}
}

func (m *RecordNodeTaskQueue) AddTask(nodeName, taskName string) {

	if _, exists := m.NodeQueue[nodeName]; !exists {
		m.NodeUpdate(nodeName, "", NodeAdd)
	}
	m.NodeQueue[nodeName].Lock()
	defer m.NodeQueue[nodeName].Unlock()
	m.NodeQueue[nodeName].Tasks[taskName] = struct{}{}
}

func (m *RecordNodeTaskQueue) RemoveTask(nodeName, taskName string) {
	if nodeQueue, exists := m.NodeQueue[nodeName]; exists {
		nodeQueue.Lock()
		defer nodeQueue.Unlock()
		delete(nodeQueue.Tasks, taskName)
	}
}

func (m *RecordNodeTaskQueue) TaskIsExist(nodeName, taskName string) bool {

	if nodeQueue, exists := m.NodeQueue[nodeName]; exists {

		if _, exists := nodeQueue.Tasks[taskName]; exists {
			return true
		}
	}
	return false
}

func (m *RecordNodeTaskQueue) UpdateTaskState(nodeName, taskName string, state TaskState) {
	if nodeName == "" || taskName == "" {
		return
	}
	switch state {
	case TaskCompleted, TaskDelete:
		m.RemoveTask(nodeName, taskName)
		findNodeName := m.QueryNodeName(taskName)
		if findNodeName != "" {
			m.RemoveTask(findNodeName, taskName)
		}
	case TaskStarting:
		m.AddTask(nodeName, taskName)
	}
}

func (m *RecordNodeTaskQueue) QueryTaskNum(nodeName string) int {
	if _, exists := m.NodeQueue[nodeName]; exists {
		return len(m.NodeQueue[nodeName].Tasks)
	}
	return -1 // 如果节点不存在，返回-1
}

func (m *RecordNodeTaskQueue) QueryNodeName(taskName string) string {
	if taskName == "" || m.NodeQueue == nil {
		return ""

	}
	for nodeName, nodeQueue := range m.NodeQueue {
		if _, exists := nodeQueue.Tasks[taskName]; exists {
			return nodeName
		}
	}
	return ""
}

func (m *RecordNodeTaskQueue) GetNodeCpuArch(nodeName string) (string, bool) {
	if nodeQueue, exists := m.NodeQueue[nodeName]; exists {
		return nodeQueue.CpuArch, true
	}
	return "", false
}

func (m *RecordNodeTaskQueue) NodeUpdate(nodeName, CpuArch string, op NodeOption) {
	m.Lock()
	defer m.Unlock()
	if m.NodeQueue == nil {
		m.NodeQueue = make(map[string]*NodeTaskQueue)
	}
	switch op {
	case NodeRemove:
		delete(m.NodeQueue, nodeName)
	case NodeAdd:
		m.LoadOrStore(nodeName, &NodeTaskQueue{
			CpuArch: CpuArch,
			Tasks:   map[string]struct{}{},
		})
	}
}

func (m *RecordNodeTaskQueue) LoadOrStore(nodeName string, nodeQueue *NodeTaskQueue) {
	if _, exist := m.NodeQueue[nodeName]; exist {
		if m.NodeQueue[nodeName].CpuArch == "" {
			m.NodeQueue[nodeName].CpuArch = nodeQueue.CpuArch
		}
		return
	}
	m.NodeQueue[nodeName] = nodeQueue
}
