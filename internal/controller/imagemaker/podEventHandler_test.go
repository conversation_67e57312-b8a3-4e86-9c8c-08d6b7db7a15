package imagemaker

import (
	"context"
	"testing"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/util/workqueue"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/event"
)

func TestPodEventHandler_Update(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, systemv1alpha1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))

	fakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	queue := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())

	handler := &PodEventHandler{
		Client:      fakeClient,
		Scheme:      scheme,
		ImageConfig: config.ImageConfig{},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{Name: "test-image", Namespace: "default"},
		Status:     systemv1alpha1.ImageMakerStatus{State: systemv1alpha1.ImageMakerState{Phase: systemv1alpha1.IMPending}},
	}
	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
			Labels:    map[string]string{systemv1alpha1.ImagemakerNameLabels: "test-image"},
		},
		Status: corev1.PodStatus{
			Phase:   corev1.PodRunning,
			Reason:  "Running",
			Message: "Pod is running",
		},
	}

	require.NoError(t, fakeClient.Create(context.TODO(), imageMaker))
	require.NoError(t, fakeClient.Create(context.TODO(), pod))

	event := event.UpdateEvent{ObjectNew: pod}
	handler.Update(event, queue)

	updatedImageMaker := &systemv1alpha1.ImageMaker{}
	require.NoError(t, fakeClient.Get(context.TODO(), client.ObjectKey{Namespace: "default", Name: "test-image"}, updatedImageMaker))

	assert.Equal(t, systemv1alpha1.IMRunning, updatedImageMaker.Status.State.Phase)
	assert.Equal(t, "Running", updatedImageMaker.Status.State.Reason)
	assert.Equal(t, "Pod is running", updatedImageMaker.Status.State.Message)
}

func TestCheckNodeLabels(t *testing.T) {
	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{systemv1alpha1.ImagemakerNameLabels: "test-image"},
		},
	}

	retrievedPod, name, exist := checkNodeLabels(pod)
	assert.True(t, exist)
	assert.Equal(t, "test-image", name)
	assert.Equal(t, pod, retrievedPod)
}

func TestUpdateImageMaker(t *testing.T) {
	scheme := runtime.NewScheme()
	require.NoError(t, systemv1alpha1.AddToScheme(scheme))
	require.NoError(t, corev1.AddToScheme(scheme))

	fakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	handler := &PodEventHandler{
		Client:      fakeClient,
		Scheme:      scheme,
		ImageConfig: config.ImageConfig{},
	}

	imageMaker := &systemv1alpha1.ImageMaker{
		ObjectMeta: metav1.ObjectMeta{Name: "test-image", Namespace: "default"},
		Status: systemv1alpha1.ImageMakerStatus{
			State: systemv1alpha1.ImageMakerState{Reason: "Completed",
				Message:            "Pod completed successfully",
				LastTransitionTime: metav1.Now(),
			},
			NodeName: "node-1",
		},
	}

	require.NoError(t, fakeClient.Create(context.TODO(), imageMaker))

	handler.UpdateImageMaker(true, imageMaker)

	updatedImageMaker := &systemv1alpha1.ImageMaker{}
	require.NoError(t, fakeClient.Get(context.TODO(), client.ObjectKey{Namespace: "default", Name: "test-image"}, updatedImageMaker))

	assert.Equal(t, "Completed", updatedImageMaker.Status.State.Reason)
	assert.Equal(t, "Pod completed successfully", updatedImageMaker.Status.State.Message)
	assert.Equal(t, "node-1", updatedImageMaker.Status.NodeName)
	assert.WithinDuration(t, time.Now(), updatedImageMaker.Status.State.LastTransitionTime.Time, time.Second)
}
