package imagemaker

import (
	"testing"

	"hero.ai/hero-controllers/internal/controller/config"
)

func TestQueryCpuArch(t *testing.T) {

	imageName := "hero-dev-image.cnbita.com:5000/training/systemuser-llama2:jupyter"
	imageRepoCredentials :=
		ImageRepoCredentials{
			ImageRepo: "https://hero-dev-image.cnbita.com:5000",
			UserName:  "admin",
			Passwd:    "harboradmin",
		}
	arch, err := QueryCpuArch(imageName, imageRepoCredentials)
	if err != nil || arch == "unknown" {
		t.<PERSON><PERSON><PERSON>("QueryCpuArch returned an empty string")
	}

	imageName = "hero-dev-image.cnbita.com:5000/training/systemuser-llama2:jupytertest"
	arch, err = QueryCpuArch(imageName, imageRepoCredentials)
	if err == nil || arch != "" {
		t.Errorf("QueryCpuArch wrong")
	}

}

func TestConvertKubeContainerID(t *testing.T) {
	dockerID, err := ConvertKubeContainerIDToDocker("docker://test-container-id")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("convertKubeContainerIDToDocker returned an error: %v", err)
	}
	if dockerID != "test-container-id" {
		t.Errorf("Expected test-container-id, got %v", dockerID)
	}

	_, err = ConvertKubeContainerIDToDocker("invalid-container-id")
	if err == nil {
		t.Errorf("Expected an error for invalid container ID")
	}
}

func TestGetToolPodTemplate(t *testing.T) {
	params := &ToolPodParam{
		Name:           "test-pod",
		NameSpace:      "default",
		ImageMakerName: "test-im",
		NodeName:       "node-1",
		ToolImage:      "busybox:latest",
		Shell:          "/script.sh",
		ImageUrl:       "test-image",
		ImageRePoUrl:   "harbor.example.com",
	}

	pod := GetToolPodTemplate(config.ImageConfig{}, config.ServerConfig{}, params)

	// 验证关键字段
	if pod.Spec.NodeName != "node-1" ||
		pod.Spec.Containers[0].Image != "busybox:latest" ||
		len(pod.Spec.Volumes) != 2 {
		t.Fatal("Pod模板生成不符合预期")
	}
}

func TestAddTask(t *testing.T) {
	record := &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}

	record.NodeUpdate("node1", "x86_64", NodeAdd)

	// 测试添加任务
	record.AddTask("node1", "task1")

	nodeQueue, exists := record.NodeQueue["node1"]
	if !exists {
		t.Fatal("expected node1 to exist")
	}

	if _, exists := nodeQueue.Tasks["task1"]; !exists {
		t.Fatal("expected task1 to be added to node1's task list")
	}
}

func TestRemoveTask(t *testing.T) {
	record := &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}

	record.NodeUpdate("node1", "x86_64", NodeAdd)
	record.AddTask("node1", "task1")

	// 测试删除任务
	record.RemoveTask("node1", "task1")

	nodeQueue, exists := record.NodeQueue["node1"]
	if !exists {
		t.Fatal("expected node1 to exist")
	}

	if _, exists := nodeQueue.Tasks["task1"]; exists {
		t.Fatal("expected task1 to be removed from node1's task list")
	}
}

func TestTaskIsExist(t *testing.T) {
	record := &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}

	record.NodeUpdate("node1", "x86_64", NodeAdd)

	record.UpdateTaskState("node1", "task1", TaskStarting)
	if !record.TaskIsExist("node1", "task1") {
		t.Fatal("expected task1 exists")
	}

	if record.TaskIsExist("node1", "task2") {
		t.Fatal("expected task2 not exists")
	}
	if record.TaskIsExist("node3", "task2") {
		t.Fatal("expected task2 not exists node3")
	}
}

func TestUpdateTaskState(t *testing.T) {
	record := &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}

	record.NodeUpdate("node1", "x86_64", NodeAdd)

	// 测试任务状态更新为 TaskStarting
	record.UpdateTaskState("node1", "task1", TaskStarting)
	if len(record.NodeQueue["node1"].Tasks) != 1 {
		t.Fatal("expected task1 to be added to node1's task list")
	}

	// 测试任务状态更新为 TaskCompleted
	record.UpdateTaskState("node1", "task1", TaskCompleted)
	if len(record.NodeQueue["node1"].Tasks) != 0 {
		t.Fatal("expected task1 to be removed from node1's task list")
	}

	// 测试任务状态更新为 TaskDelete
	record.UpdateTaskState("node1", "task1", TaskDelete)
	if len(record.NodeQueue["node1"].Tasks) != 0 {
		t.Fatal("expected task1 to be removed from node1's task list")
	}
}

func TestQueryTaskNum(t *testing.T) {
	record := &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}

	record.NodeUpdate("node1", "x86_64", NodeAdd)
	record.AddTask("node1", "task1")
	record.AddTask("node1", "task2")

	// 测试查询任务数
	numTasks := record.QueryTaskNum("node1")
	if numTasks != 2 {
		t.Fatalf("expected 2 tasks, got %d", numTasks)
	}

	// 测试节点不存在的情况
	numTasks = record.QueryTaskNum("node2")
	if numTasks != -1 {
		t.Fatalf("expected -1 for non-existing node, got %d", numTasks)
	}
}

func TestQueryNodeName(t *testing.T) {
	record := &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}

	record.NodeUpdate("node1", "x86_64", NodeAdd)
	record.AddTask("node1", "task1")

	// 测试查询任务对应的节点
	nodeName := record.QueryNodeName("task1")
	if nodeName != "node1" {
		t.Fatalf("expected node1, got %s", nodeName)
	}

	// 测试查询不存在的任务
	nodeName = record.QueryNodeName("task2")
	if nodeName != "" {
		t.Fatalf("expected empty string, got %s", nodeName)
	}
}

func TestGetNodeCpuArch(t *testing.T) {
	record := &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}

	record.NodeUpdate("node1", "x86_64", NodeAdd)

	// 测试获取节点的 CPU 架构
	cpuArch, exists := record.GetNodeCpuArch("node1")
	if !exists {
		t.Fatal("expected node1 to exist")
	}

	if cpuArch != "x86_64" {
		t.Fatalf("expected x86_64, got %s", cpuArch)
	}

	// 测试获取不存在节点的 CPU 架构
	_, exists = record.GetNodeCpuArch("node2")
	if exists {
		t.Fatal("expected node2 to not exist")
	}
}

func TestNodeUpdate(t *testing.T) {
	record := &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}

	// 测试添加节点
	record.NodeUpdate("node1", "x86_64", NodeAdd)
	if _, exists := record.NodeQueue["node1"]; !exists {
		t.Fatal("expected node1 to be added")
	}

	// 测试删除节点
	record.NodeUpdate("node1", "", NodeRemove)
	if _, exists := record.NodeQueue["node1"]; exists {
		t.Fatal("expected node1 to be removed")
	}
}
