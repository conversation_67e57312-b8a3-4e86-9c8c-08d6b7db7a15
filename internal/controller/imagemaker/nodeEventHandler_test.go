package imagemaker

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/event"
)

type MockClient struct {
	mock.Mock
	client.Client
}

func TestNodeEventHandler_Create(t *testing.T) {
	// Arrange
	handler := &NodeEventHandler{
		Client:         &MockClient{},                     // Mock the client
		ServeNodeLabel: "node-role.kubernetes.io/service", // Set the label
	}

	node := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "node1",
			Labels: map[string]string{"node-role.kubernetes.io/training": "true", "node-role.kubernetes.io/service": "true"},
		},
	}
	handler.RecordNodeTaskQueue = &RecordNodeTaskQueue{
		NodeQueue: map[string]*NodeTaskQueue{},
	}
	handler.RecordNodeTaskQueue.NodeQueue["node-1"] = &NodeTaskQueue{
		CpuArch: "amd64",
		Tasks:   map[string]struct{}{},
	}
	handler.Create(event.CreateEvent{Object: node}, nil)

	// Assert
	// Assuming NodeUpdate performs some action, you can check if it was correctly called
	// assert.Contains(t, imRecordNodeTask, "node1", "Node 'node1' should be added to imRecordNodeTask")
}

func TestNodeEventHandler_Update(t *testing.T) {
	// Arrange
	handler := &NodeEventHandler{
		Client:         &MockClient{},
		ServeNodeLabel: "node-role.kubernetes.io/service",
	}

	oldNode := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "node-1",
			Labels: map[string]string{"node-role.kubernetes.io/service": "false", "node-role.kubernetes.io/training": "true"},
		},
	}
	newNode := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "node-1",
			Labels: map[string]string{"node-role.kubernetes.io/service": "true", "node-role.kubernetes.io/training": "true"},
		},
	}

	handler.RecordNodeTaskQueue = &RecordNodeTaskQueue{
		NodeQueue: map[string]*NodeTaskQueue{},
	}
	handler.RecordNodeTaskQueue.NodeQueue["node-1"] = &NodeTaskQueue{
		CpuArch: "amd64",
		Tasks:   map[string]struct{}{},
	}

	// Act
	handler.Update(event.UpdateEvent{ObjectOld: oldNode, ObjectNew: newNode}, nil)
}

func TestNodeEventHandler_Delete(t *testing.T) {
	// Arrange
	handler := &NodeEventHandler{
		Client:         &MockClient{},
		ServeNodeLabel: "node-role.kubernetes.io/service",
	}

	node := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "node-1",
			Labels: map[string]string{"node-role.kubernetes.io/service": "true", "node-role.kubernetes.io/training": "true"},
		},
	}
	handler.RecordNodeTaskQueue = &RecordNodeTaskQueue{
		NodeQueue: map[string]*NodeTaskQueue{},
	}
	handler.RecordNodeTaskQueue.NodeQueue["node-1"] = &NodeTaskQueue{
		CpuArch: "amd64",
		Tasks:   map[string]struct{}{},
	}

	// Act
	handler.Delete(event.DeleteEvent{Object: node}, nil)
}

func TestNodeEventHandler_checkNodeLabels(t *testing.T) {
	// Arrange
	handler := &NodeEventHandler{
		ServeNodeLabel: "node-role.kubernetes.io/service",
	}

	nodeWithCorrectLabels := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{"node-role.kubernetes.io/service": "true", "node-role.kubernetes.io/training": "true"},
		},
	}

	nodeWithIncorrectLabels := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{"node-role.kubernetes.io/service": "false", "node-role.kubernetes.io/training": "true"},
		},
	}

	_, flag := handler.checkNodeLabels(nodeWithCorrectLabels)
	// Act & Assert
	assert.True(t, flag, "Node should match the required labels")

	_, flag = handler.checkNodeLabels(nodeWithIncorrectLabels)
	assert.False(t, flag, "Node should not match the required labels")
}

func TestNodeEventHandler_getRequiredLabels(t *testing.T) {
	// Arrange
	handler := &NodeEventHandler{
		ServeNodeLabel: "node-role.kubernetes.io/service",
	}

	mps := handler.getRequiredLabels()
	fmt.Println(mps)
	// Act & Assert
	_, exists := mps[LabelTrainingNodeRole]

	assert.True(t, exists, "map should  match the required labels")

}
