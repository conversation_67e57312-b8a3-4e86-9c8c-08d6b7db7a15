package imagemaker

import (
	"context"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/util/workqueue"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/event"
)

// NodeEventHandler watches for changes to Nodes
type PodEventHandler struct {
	client.Client
	*runtime.Scheme
	ImageConfig config.ImageConfig
	*RecordNodeTaskQueue
}

func (p *PodEventHandler) Create(evt event.CreateEvent, q workqueue.RateLimitingInterface) {}

func (p *PodEventHandler) Update(evt event.UpdateEvent, q workqueue.RateLimitingInterface) {
	if pod, name, exist := checkNodeLabels(evt.ObjectNew); exist {
		imageMaker := &systemv1alpha1.ImageMaker{}
		if err := p.Get(context.Background(), client.ObjectKey{Namespace: pod.Namespace, Name: name}, imageMaker); err != nil {
			return
		}

		updated := false
		var podPhaseToImageMakerPhase = map[corev1.PodPhase]systemv1alpha1.ImageMakerPhase{
			corev1.PodPending:   systemv1alpha1.IMPending,
			corev1.PodFailed:    systemv1alpha1.IMFailed,
			corev1.PodRunning:   systemv1alpha1.IMRunning,
			corev1.PodSucceeded: systemv1alpha1.IMSucceeded,
		}

		if newPhase, exists := podPhaseToImageMakerPhase[pod.Status.Phase]; exists {
			if imageMaker.Status.State.Phase != newPhase {
				updated = true
				imageMaker.Status.State.Phase = newPhase
				imageMaker.Status.State.Reason = pod.Status.Reason
				imageMaker.Status.State.Message = pod.Status.Message
				imageMaker.Status.State.LastTransitionTime = metav1.Time{Time: time.Now()}
				imageMaker.Status.NodeName = pod.Spec.NodeName
			}
		}

		p.UpdateImageMaker(updated, imageMaker)
	}
}

func (p *PodEventHandler) Delete(evt event.DeleteEvent, q workqueue.RateLimitingInterface) {

	if pod, name, exist := checkNodeLabels(evt.Object); exist {
		imageMaker := &systemv1alpha1.ImageMaker{}
		if err := p.Get(context.Background(), client.ObjectKey{Namespace: pod.Namespace, Name: name}, imageMaker); err != nil {
			return
		}
		p.UpdateTaskState(imageMaker.Status.NodeName, imageMaker.Name, TaskDelete)

	}

}

func (p *PodEventHandler) Generic(evt event.GenericEvent, q workqueue.RateLimitingInterface) {
	// 处理其他事件，可以根据需要进行扩展
}

func checkNodeLabels(obj client.Object) (*corev1.Pod, string, bool) {
	pod := obj.(*corev1.Pod)
	for key, value := range pod.GetLabels() {
		if key == systemv1alpha1.ImagemakerNameLabels {
			return pod, value, true
		}
	}
	return pod, "", false
}

func (p *PodEventHandler) UpdateImageMaker(updated bool, imageMaker *systemv1alpha1.ImageMaker) {
	if !updated {
		return
	}
	if imageMaker.Status.State.Phase == systemv1alpha1.IMSucceeded {
		cpuArch, err := QueryCpuArch(imageMaker.Spec.DestImageUrl, ImageRepoCredentials{p.ImageConfig.ImageRepo, p.ImageConfig.UserName, p.ImageConfig.Password})
		if err != nil {
			return
		}

		if cpuArch != "" {
			imageMaker.Status.CpuArch = cpuArch
		} else {
			imageMaker.Status.CpuArch = "unknown"
		}
	}

	if err := p.Status().Update(context.Background(), imageMaker); err != nil {
		return
	}
}
