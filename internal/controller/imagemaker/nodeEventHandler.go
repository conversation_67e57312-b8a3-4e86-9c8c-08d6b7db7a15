package imagemaker

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/util/workqueue"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/event"
)

// NodeEventHandler watches for changes to Nodes
type NodeEventHandler struct {
	client.Client
	*runtime.Scheme
	*RecordNodeTaskQueue
	ServeNodeLabel string
}

func (p *NodeEventHandler) Create(evt event.CreateEvent, q workqueue.RateLimitingInterface) {
	if node, exist := p.checkNodeLabels(evt.Object); exist {
		p.NodeUpdate(node.Name, node.Status.NodeInfo.Architecture, NodeAdd)
	}
}

func (p *NodeEventHandler) Update(evt event.UpdateEvent, q workqueue.RateLimitingInterface) {
	if node, exist := p.checkNodeLabels(evt.ObjectNew); exist {
		p.NodeUpdate(node.Name, node.Status.NodeInfo.Architecture, NodeAdd)
	} else {
		p.NodeUpdate(node.Name, node.Status.NodeInfo.Architecture, NodeRemove)
	}
}

func (p *NodeEventHandler) Delete(evt event.DeleteEvent, q workqueue.RateLimitingInterface) {
	if node, exist := p.checkNodeLabels(evt.Object); exist {
		p.NodeUpdate(node.Name, "", NodeRemove)
	}
}

func (p *NodeEventHandler) Generic(evt event.GenericEvent, q workqueue.RateLimitingInterface) {
	// 处理其他事件，可以根据需要进行扩展
}

func (p *NodeEventHandler) checkNodeLabels(obj client.Object) (*corev1.Node, bool) {
	node := obj.(*corev1.Node)
	for label, expectedValue := range p.getRequiredLabels() {
		if value, exists := node.GetLabels()[label]; !exists || value != expectedValue {
			return node, false
		}
	}
	return node, true
}

func (p *NodeEventHandler) getRequiredLabels() map[string]string {

	return map[string]string{
		LabelTrainingNodeRole: "true",
		p.ServeNodeLabel:      "true",
	}
}
