/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package imagemaker

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"k8s.io/client-go/util/retry"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	jobcommon "hero.ai/hero-controllers/internal/controller/job"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/source"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
)

type ToolPodParam struct {
	Name           string
	NameSpace      string
	ContainerId    string //nolint
	NodeName       string
	Shell          string
	Dockerfile     string
	MountTar       string
	PublicImage    string
	PrivateImage   string
	PrivateRepo    string
	PrivateUser    string
	PrivatePwd     string
	ImageUrl       string
	ToolImage      string
	BuildMaxTime   string
	ImageRePoUrl   string //nolint
	MakeType       systemv1alpha1.MakeMethod
	ImageMakerName string
	Volume         *corev1.Volume
	VolumeMount    *corev1.VolumeMount
	Envs           []corev1.EnvVar
	CpuArch        string //nolint
}

const (
	BuildImageTool          = "registry.cnbita.com:5000/cloud-images/hero-user/imagebuildtool:0.11"
	CommitContainerShell    = "/imagebuild/commit-container.sh"
	BuildDockerfileShell    = "/imagebuild/build-dockerfile.sh"
	BuildxDockerfileShell   = "/imagebuild/buildx-dockerfile.sh"
	LoadTarShell            = "/imagebuild/load-tar.sh"
	PullPublicImageShell    = "/imagebuild/pull-public-image.sh"
	PullPrivateImageShell   = "/imagebuild/pull-private-image.sh"
	BuildUrlDockerfileShell = "/imagebuild/build-urldockerfile.sh" //nolint
	MountDir                = "/mount/filedir"
	LabelTrainingNodeRole   = "node-role.kubernetes.io/training"
	CpuArchLabelKey         = "kubernetes.io/arch"
	AnnotationKeyCommand    = "command.system.hero.ai"
	CommandStop             = "stop"
	ServeNodeLabel          = "node-role.kubernetes.io/service"
	DefaultRequeueTime      = 30
)

//+kubebuilder:rbac:groups=system.hero.ai,resources=imagemakers,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=imagemakers/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=imagemakers/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the ImageMaker object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.14.4/pkg/reconcile

// ImageMakerReconciler reconciles a ImageMaker object

type ImageMakerReconciler struct {
	client.Client
	*RecordNodeTaskQueue
	Scheme       *runtime.Scheme
	Recorder     record.EventRecorder
	ServerConfig config.ServerConfig
	ImageConfig  config.ImageConfig
}

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *ImageMakerReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	imageMaker := &systemv1alpha1.ImageMaker{}
	if err := r.Get(ctx, req.NamespacedName, imageMaker); err != nil {
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}

	imPhase := imageMaker.Status.State.Phase
	if imPhase == systemv1alpha1.IMSucceeded || imPhase == systemv1alpha1.IMStopped || imPhase == systemv1alpha1.IMFailed {
		r.UpdateTaskState(imageMaker.Status.NodeName, imageMaker.Name, TaskCompleted)
		return ctrl.Result{}, nil
	}

	if imageMaker.Annotations[AnnotationKeyCommand] == CommandStop {
		return r.errorHandler(r.StopBuildImage(ctx, imageMaker))
	}

	if imPhase == "" && imageMaker.Status.NodeName == "" {
		return r.handleNewImageMaker(ctx, imageMaker)
	}

	if err := r.updateIfToolPodEnd(ctx, imageMaker); err != nil {
		return r.errorHandler(err)
	}
	return ctrl.Result{}, nil
}

func (r *ImageMakerReconciler) handleNewImageMaker(ctx context.Context, imageMaker *systemv1alpha1.ImageMaker) (ctrl.Result, error) {
	logf := log.FromContext(ctx)
	nodeName, ok := r.selectAvailableTrainingNode(ctx, imageMaker, r.ImageConfig.MaxTaskPerNode)
	if !ok {
		return ctrl.Result{Requeue: true, RequeueAfter: time.Second * DefaultRequeueTime}, nil
	}

	if imageMaker.Spec.Source.Type == systemv1alpha1.Container && nodeName == "" {
		if err := r.UpdateStatus(ctx, imageMaker, systemv1alpha1.IMFailed, nodeName, "notebook not running", "notebook-pod not exist"); err != nil {
			r.UpdateTaskState(nodeName, imageMaker.Name, TaskDelete)
			return r.errorHandler(err)
		}
	}

	if err := r.createToolPod(ctx, imageMaker, nodeName); err != nil {
		if err = r.UpdateStatus(ctx, imageMaker, systemv1alpha1.IMFailed, nodeName, "create tool pod failed", err.Error()); err != nil {
			return r.errorHandler(err)
		}
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}

	r.UpdateTaskState(nodeName, imageMaker.Name, TaskStarting)
	logf.Info("map info", "name", imageMaker.Name, "messege(updated)", r.RecordNodeTaskQueue.NodeQueue[nodeName])
	if err := r.UpdateStatus(ctx, imageMaker, systemv1alpha1.IMPending, nodeName, "", ""); err != nil {
		r.UpdateTaskState(nodeName, imageMaker.Name, TaskDelete)
		return r.errorHandler(err)
	}

	return ctrl.Result{}, nil
}

func (r *ImageMakerReconciler) updateIfToolPodEnd(ctx context.Context, imageMaker *systemv1alpha1.ImageMaker) error {
	logf := log.FromContext(ctx)
	var toolPod = &corev1.Pod{}
	if err := r.Get(ctx, types.NamespacedName{Namespace: imageMaker.Namespace, Name: imageMaker.Name + "-imagemaker"}, toolPod); err != nil {
		return client.IgnoreNotFound(err)
	}
	nodeName := imageMaker.Status.NodeName
	updated := false
	var podPhaseToImageMakerPhase = map[corev1.PodPhase]systemv1alpha1.ImageMakerPhase{
		corev1.PodFailed:    systemv1alpha1.IMFailed,
		corev1.PodSucceeded: systemv1alpha1.IMSucceeded,
	}

	newPhase, exists := podPhaseToImageMakerPhase[toolPod.Status.Phase]
	if imageMaker.Status.State.Phase != newPhase && exists {
		imageMaker.Status.State.Phase = newPhase
		updated = true

	}

	if updated && !r.TaskIsExist(nodeName, imageMaker.Name) {
		r.UpdateStatus(ctx, imageMaker, newPhase, nodeName, toolPod.Status.Reason, "unormal sync status")
		logf.Info("pod is end", "name", imageMaker.Name)
	}
	return nil

}

func (r *ImageMakerReconciler) errorHandler(err error) (ctrl.Result, error) {
	if k8serrors.IsConflict(err) {
		return reconcile.Result{Requeue: true}, nil
	}
	return ctrl.Result{}, err
}

func (r *ImageMakerReconciler) UpdateStatus(ctx context.Context, imageMaker *systemv1alpha1.ImageMaker, phase systemv1alpha1.ImageMakerPhase, nodeName, reason, message string) error {
	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		image := &systemv1alpha1.ImageMaker{}
		if err := r.Get(ctx, client.ObjectKey{Namespace: imageMaker.Namespace, Name: imageMaker.Name}, image); err != nil {
			return err
		}
		image.Status.State.Phase = phase
		image.Status.StoppedTime = metav1.Time{Time: time.Now()}
		image.Status.State.LastTransitionTime = metav1.Time{Time: time.Now()}
		image.Status.State.Reason = reason
		image.Status.NodeName = nodeName
		image.Status.State.Message = message
		return r.Status().Update(ctx, image)
	})
	return err
}

func (r *ImageMakerReconciler) selectAvailableTrainingNode(ctx context.Context, imageMaker *systemv1alpha1.ImageMaker, maxTaskPerNode int) (string, bool) {
	logf := log.FromContext(ctx)

	if maxTaskPerNode <= 0 {
		maxTaskPerNode = 1
	}
	if nodeName := r.QueryNodeName(imageMaker.Name); nodeName != "" {
		r.RemoveTask(nodeName, imageMaker.Name)
	}

	if imageMaker.Spec.Source.Type == systemv1alpha1.Container {
		notebookpod, err := r.GetNotebookpod(imageMaker, context.TODO())
		if err != nil {
			r.Recorder.Event(imageMaker, corev1.EventTypeNormal, "getNotebookPodFailed", "Imagemaker Get Notebookpod failed: "+err.Error())
			return "", true
		}
		nodeName := notebookpod.Spec.NodeName
		taskNum := r.QueryTaskNum(nodeName)
		if taskNum > -1 && taskNum < maxTaskPerNode {
			logf.Info("Reconciling ImageMaker", "nodename", nodeName, "messege(查询任务数量)", taskNum)
			return nodeName, true
		}

		return "", false
	}

	cpuArch := NormalizeCpuArch(imageMaker.Spec.Source.CpuArch)

	for nodeName, nodeQueue := range r.NodeQueue {
		if nodeQueue.CpuArch != cpuArch {
			continue
		}
		taskNum := r.QueryTaskNum(nodeName)
		if taskNum > -1 && taskNum < maxTaskPerNode {
			return nodeName, true
		}
	}

	return "", false
}

func NormalizeCpuArch(cpuArch string) string {
	if len(cpuArch) == 0 || strings.Contains(cpuArch, "x86") || cpuArch == "amd64" {
		return "amd64"
	}
	return "arm64"
}

func (r *ImageMakerReconciler) StopBuildImage(ctx context.Context, imageMaker *systemv1alpha1.ImageMaker) error {
	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		image := &systemv1alpha1.ImageMaker{}
		if err := r.Get(ctx, client.ObjectKey{Namespace: imageMaker.Namespace, Name: imageMaker.Name}, image); err != nil {
			return err
		}

		if image.Status.State.Phase != systemv1alpha1.IMStopping {
			image.Status.State.Phase = systemv1alpha1.IMStopping

		} else if image.Status.State.Phase == systemv1alpha1.IMStopping {
			image.Status.State.Phase = systemv1alpha1.IMStopped
			image.Status.StoppedTime = metav1.Time{Time: time.Now()}
		}
		pod := &corev1.Pod{
			ObjectMeta: metav1.ObjectMeta{
				Name:      imageMaker.Name + "-imagemaker",
				Namespace: imageMaker.Namespace,
			},
		}
		if err := r.Delete(ctx, pod); client.IgnoreNotFound(err) != nil {
			return err
		}
		image.Status.State.LastTransitionTime = metav1.Time{Time: time.Now()}
		return r.Status().Update(ctx, image)
	})
	return err
}

func (r *ImageMakerReconciler) createToolPod(ctx context.Context, imageMaker *systemv1alpha1.ImageMaker, nodeName string) error {

	logf := log.FromContext(ctx)
	toolPodParam, err := r.getPodParam(ctx, imageMaker)
	if err != nil {
		return err
	}
	toolPodParam.NodeName = nodeName
	toolPod := GetToolPodTemplate(r.ImageConfig, r.ServerConfig, toolPodParam)

	if err := controllerutil.SetControllerReference(imageMaker, toolPod, r.Scheme); err != nil {
		return err
	}

	if err := r.Create(ctx, toolPod); err != nil {

		if k8serrors.IsAlreadyExists(err) {
			logf.Info("create info", "name", imageMaker.Name, "messege(warning)", "pod has been creating")
			return nil
		}
		return err
	}

	return nil
}
func (r *ImageMakerReconciler) getPodParam(ctx context.Context, imageMaker *systemv1alpha1.ImageMaker) (*ToolPodParam, error) {
	var toolpodparam *ToolPodParam
	var err error
	if toolpodparam, err = r.InitPodParam(imageMaker, ctx); err != nil {
		return nil, err
	}
	// toolpod = &corev1.Pod{}
	switch toolpodparam.MakeType {
	case systemv1alpha1.Container:
		err = r.FromNotebookPodParam(toolpodparam, imageMaker, ctx)
	case systemv1alpha1.PublicImage:
		err = r.FromPublicImagePodParam(toolpodparam, imageMaker, ctx)
	case systemv1alpha1.PrivateImage:
		err = r.FromPrivateImagePodParam(toolpodparam, imageMaker, ctx)
	case systemv1alpha1.UrlDockerfile:
		err = r.FromDockerfileUrlPodParam(toolpodparam, imageMaker, ctx)
	default:
		err = r.FromVolumePodParam(toolpodparam, imageMaker, ctx)
	}

	if err != nil {
		return nil, err
	}

	return toolpodparam, nil
}

func (r *ImageMakerReconciler) InitPodParam(imageMaker *systemv1alpha1.ImageMaker, ctx context.Context) (*ToolPodParam, error) { //nolint
	toolImage := r.ServerConfig.ToolImage
	logf := log.FromContext(ctx)
	if toolImage == "" {
		logf.Info("nacos config [ToolImage] is None")
		toolImage = BuildImageTool
	}
	// return err if not match
	if !strings.Contains(imageMaker.Spec.DestImageUrl, r.ImageConfig.Domain) {
		return nil, errors.New("[imageMaker.Spec.DestImageUrl not match imageMaker.ImageConfig.Domain ]")
	}

	buildMaxTime := r.ServerConfig.ImageBuildMaxTime
	if buildMaxTime == "" {
		logf.Info("nacos config [ImageBuildMaxTime] is None")
		buildMaxTime = "2d"
	}

	var (
		toolpodparam = &ToolPodParam{
			Name:           imageMaker.Name + "-imagemaker",
			NameSpace:      imageMaker.Namespace,
			ImageUrl:       imageMaker.Spec.DestImageUrl,
			ToolImage:      toolImage,
			MakeType:       imageMaker.Spec.Source.Type,
			BuildMaxTime:   buildMaxTime,
			ImageRePoUrl:   r.ImageConfig.Domain,
			ImageMakerName: imageMaker.Name,
			// HarborLoginInfo: harborLoginInfo,
		}
	)
	return toolpodparam, nil

}

func (r *ImageMakerReconciler) FromPrivateImagePodParam(param *ToolPodParam, imageMaker *systemv1alpha1.ImageMaker, ctx context.Context) error {

	logf := log.FromContext(ctx)
	param.Shell = PullPrivateImageShell
	param.PrivateImage = imageMaker.Spec.Source.SourceImageUrl

	param.PrivateUser = imageMaker.Spec.Source.ImageSecret.Username

	pwd, err := jobcommon.RsaPublicKeyEncryt(imageMaker.Spec.Source.ImageSecret.Password)
	if err != nil {
		return err
	}
	//===z
	// pwd = imageMaker.Spec.Source.ImageSecret.Password
	param.PrivatePwd = pwd

	param.PrivateRepo = strings.Split(param.PrivateImage, "/")[0]

	if len(param.PrivateRepo) == 0 {
		logf.Info("param.PrivateRepo [PrivateRepo] is None")
	}

	param.Envs = []corev1.EnvVar{
		{
			Name:  "PRIVATEUSER",
			Value: param.PrivateUser,
		},
		{
			Name:  "PRIVATEPASSWD",
			Value: param.PrivatePwd,
		},
		{
			Name:  "PRIVATEIMAGEREPO",
			Value: param.PrivateRepo,
		},
		{
			Name:  "PRIVATEIMAGE",
			Value: param.PrivateImage,
		},
	}

	return nil
}

func (r *ImageMakerReconciler) FromPublicImagePodParam(param *ToolPodParam, imageMaker *systemv1alpha1.ImageMaker, ctx context.Context) error {

	param.PublicImage = imageMaker.Spec.Source.SourceImageUrl
	param.Shell = PullPublicImageShell

	param.Envs = []corev1.EnvVar{
		{
			Name:  "PUBLICIMAGE",
			Value: param.PublicImage,
		},
	}

	return nil
}

func (r *ImageMakerReconciler) FromDockerfileUrlPodParam(param *ToolPodParam, imageMaker *systemv1alpha1.ImageMaker, ctx context.Context) error {

	param.Shell = BuildUrlDockerfileShell
	param.Dockerfile = imageMaker.Spec.Source.DockerfileUrl
	param.Envs = []corev1.EnvVar{
		{
			Name:  "DOCKERFILE",
			Value: param.Dockerfile,
		},
	}

	return nil
}

func (r *ImageMakerReconciler) FromVolumePodParam(param *ToolPodParam, imageMaker *systemv1alpha1.ImageMaker, ctx context.Context) error {

	filename := filepath.Base(imageMaker.Spec.Source.DataSource.VolumeSubPath)

	mountPath := filepath.Join(MountDir, filename)

	param.Volume = &corev1.Volume{
		Name: "pvcvolume",
		VolumeSource: corev1.VolumeSource{
			PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
				ClaimName: imageMaker.Spec.Source.DataSource.VolumeName,
			},
		},
	}
	switch param.MakeType {
	case systemv1alpha1.Dockerfile:
		//Dockerfile 所在的目录作为依赖的上下文环境一起挂载
		param.VolumeMount = &corev1.VolumeMount{
			Name:      "pvcvolume",
			MountPath: MountDir,
			SubPath:   filepath.Dir(imageMaker.Spec.Source.DataSource.VolumeSubPath),
		}
		param.Dockerfile = mountPath
		param.Shell = BuildDockerfileShell

		param.CpuArch = imageMaker.Spec.Source.CpuArch
		param.CpuArch = NormalizeCpuArch(param.CpuArch)
		param.Envs = []corev1.EnvVar{
			{
				Name:  "DOCKERFILE",
				Value: param.Dockerfile,
			}, {
				Name:  "IMAGEBUILDMAXTIME",
				Value: param.BuildMaxTime,
			}, {
				Name:  "IMAGEARCH",
				Value: param.CpuArch,
			},
		}
	case systemv1alpha1.TarPkg:
		param.VolumeMount = &corev1.VolumeMount{
			Name:      "pvcvolume",
			MountPath: mountPath,
			SubPath:   imageMaker.Spec.Source.DataSource.VolumeSubPath,
		}
		param.MountTar = mountPath
		param.Shell = LoadTarShell
		param.Envs = []corev1.EnvVar{
			{
				Name:  "TAR",
				Value: param.MountTar,
			},
		}
	default:
		return fmt.Errorf("%s has wrong imageMaker.Spec.MakeType: %s", imageMaker.Name, param.MakeType)
	}

	return nil
}

func (r *ImageMakerReconciler) FromNotebookPodParam(param *ToolPodParam, imageMaker *systemv1alpha1.ImageMaker, ctx context.Context) error {

	Notebookpod, err := r.GetNotebookpod(imageMaker, ctx)
	if err != nil {
		r.Recorder.Event(imageMaker, corev1.EventTypeNormal, "getNotebookPodFailed", "Imagemaker Get Notebookpod failed: "+err.Error())
		return err
	}

	notebook := systemv1alpha1.Notebook{}
	notebookName := imageMaker.Spec.Source.Containers.Name
	notebookNamespace := imageMaker.Spec.Source.Containers.Namespace
	err = r.Client.Get(ctx, client.ObjectKey{Name: notebookName, Namespace: notebookNamespace}, &notebook)
	if err != nil {
		r.Recorder.Event(imageMaker, corev1.EventTypeNormal, "getNotebookFailed", "Imagemaker Get Notebook failed: "+err.Error())
		return err
	}

	dataSources := notebook.Spec.DataSources
	var mountPaths []string
	for _, ds := range dataSources {
		mountPaths = append(mountPaths, ds.MountPath)
	}

	paths := strings.Join(mountPaths, "|")

	k8scontainerID := ""
	for _, container := range Notebookpod.Status.ContainerStatuses {
		if container.Name != "main" {
			continue
		}
		k8scontainerID = container.ContainerID

	}

	containerID, err := ConvertKubeContainerIDToDocker(k8scontainerID)
	if err != nil {
		return err
	}
	param.ContainerId = containerID
	param.NodeName = Notebookpod.Spec.NodeName

	param.Envs = []corev1.EnvVar{
		{
			Name:  "CONTAINER_ID",
			Value: param.ContainerId,
		},
		{
			Name:  "MOUNTPATHS",
			Value: paths,
		},
	}
	param.Volume = &corev1.Volume{
		Name: "squashdir",
		VolumeSource: corev1.VolumeSource{
			HostPath: &corev1.HostPathVolumeSource{
				Path: "/leinaostorage/squashdir",
			},
		},
	}
	param.VolumeMount = &corev1.VolumeMount{
		Name:      "squashdir",
		MountPath: "/tmp",
	}

	param.Shell = CommitContainerShell

	return nil

}

func (r *ImageMakerReconciler) GetNotebookpod(imageMaker *systemv1alpha1.ImageMaker, ctx context.Context) (*corev1.Pod, error) {

	Notebookpod := &corev1.Pod{}
	NotebookpodName := fmt.Sprintf("%s%s-%s-0", vcjobbuilder.VcjobNameNotebookPrefix, imageMaker.Spec.Source.Containers.Name, imageMaker.Spec.Source.Containers.Name)

	if err := r.Get(ctx, types.NamespacedName{Namespace: imageMaker.Namespace, Name: NotebookpodName}, Notebookpod); err != nil {
		return nil, err
	}

	return Notebookpod, nil
}

func (r *ImageMakerReconciler) InitQueue(apiReader client.Reader, namespace string) error {
	r.RecordNodeTaskQueue = &RecordNodeTaskQueue{
		NodeQueue: make(map[string]*NodeTaskQueue),
	}
	var podList corev1.PodList
	if err := apiReader.List(context.TODO(), &podList, client.InNamespace(namespace),
		&client.ListOptions{
			LabelSelector: labels.SelectorFromSet(labels.Set{ImagemakerLabelKey: ImageMakerLabelValue}), // 假设有标签
		}); err != nil {
		if k8serrors.IsNotFound(err) {
			return nil
		}
		return err
	}
	if len(podList.Items) == 0 {
		return nil
	}

	for i := range podList.Items {
		pod := &podList.Items[i]
		if pod.Status.Phase != corev1.PodRunning && pod.Status.Phase != corev1.PodPending {
			continue
		}
		imName := strings.TrimSuffix(pod.Name, "-imagemaker")
		phase := pod.Status.Phase
		if phase == corev1.PodPending ||
			phase == corev1.PodRunning {
			r.UpdateTaskState(pod.Spec.NodeName, imName, TaskStarting)
		}
	}

	return nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *ImageMakerReconciler) SetupWithManager(mgr ctrl.Manager) error {

	apiReader := mgr.GetAPIReader()
	if err := r.InitQueue(apiReader, r.ServerConfig.Namespace); err != nil {
		return err
	}

	serveNodeLabel := ServeNodeLabel
	if len(r.ServerConfig.ServeNodeLabel) != 0 {
		serveNodeLabel = r.ServerConfig.ServeNodeLabel
	}

	builder := ctrl.NewControllerManagedBy(mgr)
	builder.Watches(
		&source.Kind{Type: &corev1.Node{}},
		&NodeEventHandler{mgr.GetClient(), mgr.GetScheme(), r.RecordNodeTaskQueue, serveNodeLabel},
	)
	builder.Watches(
		&source.Kind{Type: &corev1.Pod{}},
		&PodEventHandler{mgr.GetClient(), mgr.GetScheme(), r.ImageConfig, r.RecordNodeTaskQueue},
	)
	return builder.For(&systemv1alpha1.ImageMaker{}).
		Complete(r)
}
