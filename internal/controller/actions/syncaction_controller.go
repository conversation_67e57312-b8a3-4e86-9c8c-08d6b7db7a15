/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package actions

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/actions/logexport"
	"hero.ai/hero-controllers/internal/controller/actions/resourcepool"
)

// SyncActionReconciler reconciles a SyncAction object
type SyncActionReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

//+kubebuilder:rbac:groups=system.hero.ai,resources=syncactions,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=syncactions/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=syncactions/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the SyncAction object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.17.0/pkg/reconcile
func (r *SyncActionReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := log.FromContext(ctx)
	var action Action

	syncAction := &systemv1alpha1.SyncAction{}

	if err := r.Get(ctx, req.NamespacedName, syncAction); err != nil {
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}
	switch syncAction.Spec.Name {
	case systemv1alpha1.LogExportAction:
		action = &logexport.LogExport{Log: log}
	case systemv1alpha1.SyncRpAction:
		action = resourcepool.NewResourcePool(r.Client, ctx)
	default:
		log.Info("No action was matched")
		return ctrl.Result{}, nil
	}

	if err := r.ActionRun(ctx, req, syncAction, action); err != nil {
		log.Error(err, "sync action stgatus failed")
		return ctrl.Result{}, nil
	}

	return ctrl.Result{}, nil
}

func (r *SyncActionReconciler) ActionRun(ctx context.Context, req ctrl.Request, syncAction *systemv1alpha1.SyncAction, action Action) error {
	var err error
	switch syncAction.Status.State.Phase {
	case "":
		if err = r.SyncStatusSyncing(ctx, req); err != nil {
			return err
		}
	case systemv1alpha1.Syncing:
		if err = action.ParseParameter(syncAction); err != nil {
			return err
		}
		errChan := make(chan error)
		go func() {
			errChan <- action.Call(syncAction)
		}()
		callErr := <-errChan
		if callErr != nil {
			if err = r.SyncStatusFailed(ctx, req, callErr); err != nil {
				return err
			}
		} else {
			if err = r.SyncStatusSuccess(ctx, req); err != nil {
				return err
			}

		}
	case systemv1alpha1.SyncFailed, systemv1alpha1.SyncSuccess:
		//
	default:
		return fmt.Errorf("unknown state: %s", syncAction.Status.State.Phase)
	}

	return nil
}

func (r *SyncActionReconciler) SyncStatusFailed(ctx context.Context, req ctrl.Request, callErr error) error {
	syncAction := &systemv1alpha1.SyncAction{}
	if err := r.Get(ctx, req.NamespacedName, syncAction); err != nil {
		return err
	}
	syncAction.Status.State.Phase = systemv1alpha1.SyncFailed
	syncAction.Status.State.Reason = callErr.Error()

	if err := r.Status().Update(ctx, syncAction); err != nil {
		return err
	}
	return nil
}

func (r *SyncActionReconciler) SyncStatusSuccess(ctx context.Context, req ctrl.Request) error {
	syncAction := &systemv1alpha1.SyncAction{}
	if err := r.Get(ctx, req.NamespacedName, syncAction); err != nil {
		return err
	}
	syncAction.Status.State.Phase = systemv1alpha1.SyncSuccess
	if err := r.Status().Update(ctx, syncAction); err != nil {
		return err
	}
	return nil
}

func (r *SyncActionReconciler) SyncStatusSyncing(ctx context.Context, req ctrl.Request) error {
	syncAction := &systemv1alpha1.SyncAction{}
	if err := r.Get(ctx, req.NamespacedName, syncAction); err != nil {
		return err
	}
	syncAction.Status.State.Phase = systemv1alpha1.Syncing
	if err := r.Status().Update(ctx, syncAction); err != nil {
		return err
	}
	return nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *SyncActionReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.SyncAction{}).
		Complete(r)
}

type Action interface {
	ParseParameter(p *systemv1alpha1.SyncAction) error
	Call(p *systemv1alpha1.SyncAction) error
}
