/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package actions

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func TestSyncActionReconcilerReconcile(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &SyncActionReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a SyncAction object
	syncAction := &systemv1alpha1.SyncAction{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-syncaction",
			Namespace: "default",
		},
		Spec: systemv1alpha1.SyncActionSpec{
			Name: systemv1alpha1.SyncRpAction,
		},
	}

	// Add the SyncAction to the fake client
	err := reconciler.Create(context.TODO(), syncAction)
	assert.NoError(t, err)

	// Prepare a reconcile request
	req := ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-syncaction",
			Namespace: "default",
		},
	}

	// Test Reconcile
	_, err = reconciler.Reconcile(context.TODO(), req)
	assert.NoError(t, err)

	// Verify that the SyncAction object exists in the cluster
	updatedSyncAction := &systemv1alpha1.SyncAction{}
	err = reconciler.Get(context.TODO(), req.NamespacedName, updatedSyncAction)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.SyncRpAction, updatedSyncAction.Spec.Name)
}

func TestSyncActionReconcilerActionRun(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &SyncActionReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a SyncAction object
	syncAction := &systemv1alpha1.SyncAction{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-actionrun",
			Namespace: "default",
		},
		Status: systemv1alpha1.SyncActionStatus{
			State: systemv1alpha1.SyncActionState{
				Phase: systemv1alpha1.Syncing,
			},
		},
	}

	// Add the SyncAction to the fake client
	err := reconciler.Create(context.TODO(), syncAction)
	assert.NoError(t, err)

	// Create a mock action
	action := &mockAction{}

	// Call ActionRun
	err = reconciler.ActionRun(context.TODO(), ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-actionrun",
			Namespace: "default",
		},
	}, syncAction, action)
	assert.NoError(t, err)

	// Verify the SyncAction status is updated to success
	updatedSyncAction := &systemv1alpha1.SyncAction{}
	err = reconciler.Get(context.TODO(), types.NamespacedName{
		Name:      "test-actionrun",
		Namespace: "default",
	}, updatedSyncAction)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.SyncSuccess, updatedSyncAction.Status.State.Phase)
}

func TestSyncActionReconcilerSyncStatusFailed(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &SyncActionReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a SyncAction object
	syncAction := &systemv1alpha1.SyncAction{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-syncstatusfailed",
			Namespace: "default",
		},
	}

	// Add the SyncAction to the fake client
	err := reconciler.Create(context.TODO(), syncAction)
	assert.NoError(t, err)

	// Update the status to failed
	err = reconciler.SyncStatusFailed(context.TODO(), ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-syncstatusfailed",
			Namespace: "default",
		},
	}, fmt.Errorf("error occurred"))
	assert.NoError(t, err)

	// Verify the SyncAction status is updated to failed
	updatedSyncAction := &systemv1alpha1.SyncAction{}
	err = reconciler.Get(context.TODO(), types.NamespacedName{
		Name:      "test-syncstatusfailed",
		Namespace: "default",
	}, updatedSyncAction)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.SyncFailed, updatedSyncAction.Status.State.Phase)
	assert.Equal(t, "error occurred", updatedSyncAction.Status.State.Reason)
}

func TestSyncActionReconcilerSyncStatusSuccess(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &SyncActionReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a SyncAction object
	syncAction := &systemv1alpha1.SyncAction{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-syncstatussuccess",
			Namespace: "default",
		},
	}

	// Add the SyncAction to the fake client
	err := reconciler.Create(context.TODO(), syncAction)
	assert.NoError(t, err)

	// Update the status to success
	err = reconciler.SyncStatusSuccess(context.TODO(), ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-syncstatussuccess",
			Namespace: "default",
		},
	})
	assert.NoError(t, err)

	// Verify the SyncAction status is updated to success
	updatedSyncAction := &systemv1alpha1.SyncAction{}
	err = reconciler.Get(context.TODO(), types.NamespacedName{
		Name:      "test-syncstatussuccess",
		Namespace: "default",
	}, updatedSyncAction)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.SyncSuccess, updatedSyncAction.Status.State.Phase)
}

func TestSyncActionReconcilerSyncStatusSyncing(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	reconciler := &SyncActionReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a SyncAction object
	syncAction := &systemv1alpha1.SyncAction{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-syncstatussyncing",
			Namespace: "default",
		},
	}

	// Add the SyncAction to the fake client
	err := reconciler.Create(context.TODO(), syncAction)
	assert.NoError(t, err)

	// Update the status to syncing
	err = reconciler.SyncStatusSyncing(context.TODO(), ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-syncstatussyncing",
			Namespace: "default",
		},
	})
	assert.NoError(t, err)

	// Verify the SyncAction status is updated to syncing
	updatedSyncAction := &systemv1alpha1.SyncAction{}
	err = reconciler.Get(context.TODO(), types.NamespacedName{
		Name:      "test-syncstatussyncing",
		Namespace: "default",
	}, updatedSyncAction)
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.Syncing, updatedSyncAction.Status.State.Phase)
}

// Mock action for testing
type mockAction struct{}

func (m *mockAction) ParseParameter(p *systemv1alpha1.SyncAction) error {
	return nil
}

func (m *mockAction) Call(p *systemv1alpha1.SyncAction) error {
	return nil
}

//import (
//	"context"
//
//	. "github.com/onsi/ginkgo/v2"
//	. "github.com/onsi/gomega"
//	"k8s.io/apimachinery/pkg/api/errors"
//	"k8s.io/apimachinery/pkg/types"
//	"sigs.k8s.io/controller-runtime/pkg/client"
//	"sigs.k8s.io/controller-runtime/pkg/reconcile"
//
//	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
//
//	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
//)
//
//var k8sClient client.Client
//
//var _ = Describe("SyncAction Controller", func() {
//	Context("When reconciling a resource", func() {
//		const resourceName = "test-resource"
//
//		ctx := context.Background()
//
//		typeNamespacedName := types.NamespacedName{
//			Name:      resourceName,
//			Namespace: "default", // TODO(user):Modify as needed
//		}
//		syncaction := &systemv1alpha1.SyncAction{}
//
//		BeforeEach(func() {
//			By("creating the custom resource for the Kind SyncAction")
//			err := k8sClient.Get(ctx, typeNamespacedName, syncaction)
//			if err != nil && errors.IsNotFound(err) {
//				resource := &systemv1alpha1.SyncAction{
//					ObjectMeta: metav1.ObjectMeta{
//						Name:      resourceName,
//						Namespace: "default",
//					},
//					// TODO(user): Specify other spec details if needed.
//				}
//				Expect(k8sClient.Create(ctx, resource)).To(Succeed())
//			}
//		})
//
//		AfterEach(func() {
//			// TODO(user): Cleanup logic after each test, like removing the resource instance.
//			resource := &systemv1alpha1.SyncAction{}
//			err := k8sClient.Get(ctx, typeNamespacedName, resource)
//			Expect(err).NotTo(HaveOccurred())
//
//			By("Cleanup the specific resource instance SyncAction")
//			Expect(k8sClient.Delete(ctx, resource)).To(Succeed())
//		})
//		It("should successfully reconcile the resource", func() {
//			By("Reconciling the created resource")
//			controllerReconciler := &SyncActionReconciler{
//				Client: k8sClient,
//				Scheme: k8sClient.Scheme(),
//			}
//
//			_, err := controllerReconciler.Reconcile(ctx, reconcile.Request{
//				NamespacedName: typeNamespacedName,
//			})
//			Expect(err).NotTo(HaveOccurred())
//			// TODO(user): Add more specific assertions depending on your controller's reconciliation logic.
//			// Example: If you expect a certain status condition after reconciliation, verify it here.
//		})
//	})
//})
