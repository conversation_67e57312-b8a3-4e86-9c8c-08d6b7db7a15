package resourcepool

import (
	"context"
	"errors"
	"fmt"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type ResourcePool struct {
	client         client.Client
	ctx            context.Context
	originalLabels map[string]map[string]string
}

func NewResourcePool(c client.Client, ctx context.Context) *ResourcePool { //nolint
	return &ResourcePool{
		client:         c,
		ctx:            ctx,
		originalLabels: make(map[string]map[string]string),
	}
}

func (rp *ResourcePool) ParseParameter(p *systemv1alpha1.SyncAction) error {
	if len(p.Spec.TargetObject.Name) == 0 {
		return errors.New("params resource.name not found")
	}
	return nil
}

func (rp *ResourcePool) Call(p *systemv1alpha1.SyncAction) (err error) {
	var resourcePool systemv1alpha1.ResourcePool
	if err := rp.client.Get(rp.ctx, types.NamespacedName{Name: p.Spec.TargetObject.Name}, &resourcePool); err != nil {
		klog.Errorf("ResourcePool <%s> is terminating, skip management process.", p.Spec.TargetObject.Name)
		return nil
	}

	srcNodes := resourcePool.Status.Nodes
	klog.Infof("resourcepool %s old nodes: %v", resourcePool.Name, srcNodes)
	if err := rp.saveOldLabels(rp.ctx, srcNodes); err != nil {
		return err
	}
	defer func() {
		if err != nil {
			rp.recover()
		}
	}()

	var bindFunc func(labels map[string]string)
	//最终status显示的节点
	var nodes []string
	switch p.Spec.ResourcePool.Type {
	case systemv1alpha1.UnBind:
		//解绑只应添加变更的节点
		bindFunc = func(labels map[string]string) {
			delete(labels, systemv1alpha1.NodeLabelKey)
		}
		nodes = rp.getNodes(srcNodes, p.Spec.ResourcePool.Nodes)
	default:
		//绑节点，添加所有新加节点，应考虑重复绑
		bindFunc = func(labels map[string]string) {
			labels[systemv1alpha1.NodeLabelKey] = resourcePool.Name
		}
		nodes = rp.getAddNodes(srcNodes, p.Spec.ResourcePool.Nodes)
	}
	errmsg, isallow := rp.checkNodeLabels(nodes, resourcePool.Name)
	if !isallow {
		return errmsg
	}

	resourcePool.Status.Nodes = nodes
	return rp.nodeLabels(rp.ctx, bindFunc, p.Spec.ResourcePool.Nodes, &resourcePool)
}

func (rp *ResourcePool) saveOldLabels(ctx context.Context, nodes []string) error {
	for _, nodeName := range nodes {
		var node v1.Node
		err := rp.client.Get(ctx, types.NamespacedName{
			Name: nodeName,
		}, &node)
		if err != nil {
			if apierrors.IsNotFound(err) {
				continue
			}
			return err
		}

		if _, found := rp.originalLabels[nodeName]; !found {
			rp.originalLabels[nodeName] = make(map[string]string)
		}

		rp.originalLabels[nodeName] = node.GetLabels()
	}

	return nil
}

func (rp *ResourcePool) recover() {
	for nodeName, labels := range rp.originalLabels {
		var node v1.Node
		if err := rp.client.Get(rp.ctx, types.NamespacedName{Name: nodeName}, &node); err != nil {
			klog.Errorf("Failed to get node %s for recovery: %v", nodeName, err)
			continue
		}

		node.SetLabels(labels)
		if err := rp.updateNodeLabels(rp.ctx, &node); err != nil {
			klog.Errorf("Failed to restore labels for node %s: %v", nodeName, err)
		}
	}
}

// ["yigou-stg-101-69", "yigou-stg-101-68"]  "yigou-stg-101-68"
// 解绑节点列表，去重后取差集
func (rp *ResourcePool) getNodes(old, new []string) []string {
	remove := map[string]struct{}{}
	for _, node := range new {
		remove[node] = struct{}{}
	}
	var result []string
	for _, node := range old {
		if _, exists := remove[node]; !exists {
			result = append(result, node)
		}
	}

	return result
}

// 绑节点，节点列表去重交集
func (rp *ResourcePool) getAddNodes(old, new []string) []string {
	var result = old
	addMap := map[string]struct{}{}
	for _, node := range old {
		addMap[node] = struct{}{}
	}

	for _, newNode := range new {
		if _, found := addMap[newNode]; !found {
			result = append(result, newNode)
		}
	}
	return result
}

func (rp *ResourcePool) nodeLabels(ctx context.Context, fn func(labels map[string]string), nodes []string, resourcepool *systemv1alpha1.ResourcePool) error {
	var updateNode = func(nodes []string, fn func(map[string]string)) error {
		for _, nodeName := range nodes {
			var node v1.Node
			err := rp.client.Get(ctx, types.NamespacedName{
				Name: nodeName,
			}, &node)
			if err != nil {
				if apierrors.IsNotFound(err) {
					continue
				}
				return err
			}

			labels := node.GetLabels()
			fn(labels)
			node.SetLabels(labels)

			if err := rp.updateNodeLabels(ctx, &node); err != nil {
				return err
			}
		}
		return nil
	}

	//先更新状态
	if err := rp.updateResourcePoolStatus(ctx, resourcepool); err != nil {
		return err
	}

	return updateNode(nodes, fn)
}

func (rp *ResourcePool) updateNodeLabels(ctx context.Context, node *v1.Node) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		srcnode := &v1.Node{}
		if err = rp.client.Get(ctx, client.ObjectKey{Name: node.Name}, srcnode); err != nil {
			return
		}

		srcnode.Labels = node.Labels
		return rp.client.Update(ctx, srcnode)
	})
}

func (rp *ResourcePool) updateResourcePoolStatus(ctx context.Context, resourcepool *systemv1alpha1.ResourcePool) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		new := &systemv1alpha1.ResourcePool{}
		if err = rp.client.Get(ctx, client.ObjectKey{Name: resourcepool.Name}, new); err != nil {
			return
		}
		klog.Infof("resourcePool status source: %v, new: %v", new.Status, resourcepool.Status)
		new.Status = resourcepool.Status
		return rp.client.Status().Update(ctx, new)
	})
}

func (rp *ResourcePool) checkNodeLabels(nodes []string, resourcepoolName string) (error, bool) { //nolint
	errMsg := ""
	for _, nodeName := range nodes {

		var node v1.Node
		err := rp.client.Get(rp.ctx, types.NamespacedName{
			Name: nodeName,
		}, &node)
		if err != nil {
			if !apierrors.IsNotFound(err) {
				return err, true
			}
			continue
		}

		labels := node.GetLabels()
		if _, found := labels[systemv1alpha1.NodeLabelKey]; found {
			if labels[systemv1alpha1.NodeLabelKey] != resourcepoolName {
				errMsg += fmt.Sprintf("node %s belong to other %s resourcepool,", nodeName, labels[systemv1alpha1.NodeLabelKey])
			}
		}
	}

	if len(errMsg) > 0 {
		errMsg = errMsg[:len(errMsg)-1]
		return errors.New(errMsg), false
	}

	return nil, true
}
