package resourcepool

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func TestResourcePoolParseParameter(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	rp := NewResourcePool(k8sFakeClient, context.TODO())
	testCases := []struct {
		name    string
		action  *systemv1alpha1.SyncAction
		wantErr bool
	}{
		{
			name: "valid SyncAction",
			action: &systemv1alpha1.SyncAction{
				Spec: systemv1alpha1.SyncActionSpec{
					TargetObject: &systemv1alpha1.TargetObject{
						Name: "valid-name",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "missing TargetObject.Name",
			action: &systemv1alpha1.SyncAction{
				Spec: systemv1alpha1.SyncActionSpec{
					TargetObject: &systemv1alpha1.TargetObject{
						Name: "",
					},
				},
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := rp.ParseParameter(tc.action)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestResourcePoolCall(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	rp := NewResourcePool(k8sFakeClient, context.TODO())

	testCases := []struct {
		name    string
		action  *systemv1alpha1.SyncAction
		setup   func()
		wantErr bool
	}{
		{
			name: "valid SyncAction",
			action: &systemv1alpha1.SyncAction{
				Spec: systemv1alpha1.SyncActionSpec{
					TargetObject: &systemv1alpha1.TargetObject{
						Name: "test-pool",
					},
					ResourcePool: systemv1alpha1.ResourcePoolNodes{
						Type:  systemv1alpha1.Bind,
						Nodes: []string{"node1", "node2"},
					},
				},
			},
			setup: func() {
				_ = k8sFakeClient.Create(context.TODO(), &systemv1alpha1.ResourcePool{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-pool",
					},
					Status: systemv1alpha1.ResourcePoolStatus{
						Nodes: []string{"node1"},
					},
				})
			},
			wantErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.setup()
			err := rp.Call(tc.action)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestResourcePoolRecover(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	rp := NewResourcePool(k8sFakeClient, context.TODO())

	// Setup original labels
	rp.originalLabels["node1"] = map[string]string{"key1": "value1"}

	// Create a Node in the fake k8sFakeClient
	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "node1",
			Labels: map[string]string{"key1": "oldValue"},
		},
	}
	k8sFakeClient.Create(context.TODO(), node)

	// Call recover method
	rp.recover()

	// Check if the labels were restored
	updatedNode := &v1.Node{}
	err := k8sFakeClient.Get(context.TODO(), types.NamespacedName{Name: "node1"}, updatedNode)
	assert.NoError(t, err)
	assert.Equal(t, "value1", updatedNode.Labels["key1"])
}

func TestResourcePoolCheckNodeLabels(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	rp := NewResourcePool(k8sFakeClient, context.TODO())

	node := &v1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "node1",
			Labels: map[string]string{systemv1alpha1.NodeLabelKey: "other-pool"},
		},
	}
	k8sFakeClient.Create(context.TODO(), node)

	err, isAllow := rp.checkNodeLabels([]string{"node1"}, "test-pool")
	assert.Error(t, err)
	assert.False(t, isAllow)
}
