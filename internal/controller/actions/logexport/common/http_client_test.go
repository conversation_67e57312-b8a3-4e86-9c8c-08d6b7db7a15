package common

import (
	"bytes"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestNewTransport(t *testing.T) {
	tr := NewTransport()
	if tr == nil {
		t.Error("Expected non-nil transport")
	}
	if tr.MaxIdleConns != 200 {
		t.<PERSON><PERSON><PERSON>("Expected MaxIdleConns 200, got %d", tr.MaxIdleConns)
	}
}

func TestNewHTTPClient(t *testing.T) {
	client := NewHTTPClient()
	if client == nil {
		t.Error("Expected non-nil client")
	}
	if client.(*HTTPClient).Client.Transport == nil {
		t.Error("Expected non-nil transport")
	}
}

func TestNewAPIRequest(t *testing.T) {
	method := "GET"
	base := "https://example.com"
	endpoint := "/api/v1/test"
	headers := http.Header{}
	basicAuth := &BasicAuth{"user", "pass"}
	payload := bytes.NewBufferString("test payload")

	ar := NewAPIRequest(method, base, endpoint, headers, basicAuth, payload)
	if ar.Method != method {
		t.<PERSON>("Expected method %s, got %s", method, ar.Method)
	}
	if ar.Base != base {
		t.Errorf("Expected base %s, got %s", base, ar.Base)
	}
	if ar.Endpoint != endpoint {
		t.Errorf("Expected endpoint %s, got %s", endpoint, ar.Endpoint)
	}
	if ar.BasicAuth.Username != basicAuth.Username {
		t.Errorf("Expected username %s, got %s", basicAuth.Username, ar.BasicAuth.Username)
	}
}

func TestHTTPClient_Post(t *testing.T) {
	client := NewHTTPClient()
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			t.Errorf("Expected POST request, got %s", r.Method)
		}
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{"message":"created"}`))
	}))
	defer mockServer.Close()

	var responseStruct map[string]interface{}
	resp, err := client.Post(mockServer.URL, "/", nil, nil, bytes.NewBufferString("payload"), &responseStruct, nil)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}
	if responseStruct["message"] != "created" {
		t.Errorf("Expected message 'created', got %v", responseStruct["message"])
	}
}

func TestHTTPClient_Get(t *testing.T) {
	client := NewHTTPClient()
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{"message":"success"}`))
	}))
	defer mockServer.Close()

	var responseStruct map[string]interface{}
	resp, err := client.Get(mockServer.URL, "/", nil, nil, &responseStruct, nil)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}
	if responseStruct["message"] != "success" {
		t.Errorf("Expected message 'success', got %v", responseStruct["message"])
	}
}

func TestHTTPClient_Do(t *testing.T) {
	client := NewHTTPClient()
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{"message":"success"}`))
	}))
	defer mockServer.Close()

	ar := NewAPIRequest(http.MethodGet, mockServer.URL, "/", nil, nil, nil)
	var responseStruct map[string]interface{}
	resp, err := client.Do(ar, &responseStruct)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}
	if responseStruct["message"] != "success" {
		t.Errorf("Expected message 'success', got %v", responseStruct["message"])
	}
}

func TestHTTPClient_ReadRawResponse(t *testing.T) {
	client := NewHTTPClient()
	response := httptest.NewRecorder()
	response.WriteString("raw response")

	var result string
	_, err := client.(*HTTPClient).ReadRawResponse(response.Result(), &result)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if result != "raw response" {
		t.Errorf("Expected 'raw response', got %s", result)
	}
}

func TestHTTPClient_ReadJSONResponse(t *testing.T) {
	client := NewHTTPClient()
	response := httptest.NewRecorder() // Create a ResponseRecorder, which is an implementation of http.ResponseWriter.

	// Write the response body to the ResponseRecorder.
	response.Body = bytes.NewBufferString(`{"key":"value"}`)

	var result map[string]interface{}
	// Create an http.Response object from the ResponseRecorder's Result method.
	resp := response.Result()
	resp.Body = io.NopCloser(response.Body) // Wrap the Body to make it an io.ReadCloser.

	_, err := client.(*HTTPClient).ReadJSONResponse(resp, &result)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if result["key"] != "value" {
		t.Errorf("Expected 'value', got %v", result["key"])
	}
}
