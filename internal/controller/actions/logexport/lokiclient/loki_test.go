package lokiclient

import (
	"bytes"
	"errors"
	"github.com/stretchr/testify/assert"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/common"
	"io"
	"net/http"
	"strconv"
	"testing"
	"time"
)

type LokiClientInterface interface {
	SearchFullRange(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) (*LogResult, error)
	queryAndStream(result *LogResult, lastEntry []*Entry, lokiResponse *LokiResponse, queryStr string, bs int64, start, end time.Time, direction string, step, interval string) (int64, []*Entry, error)
}

type MockLokiClient struct {
	mockSearchFullRange func(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) (*LogResult, error)
	mockQueryAndStream  func(result *LogResult, lastEntry []*Entry, lokiResponse *LokiResponse, queryStr string, bs int64, start, end time.Time, direction string, step, interval string) (int64, []*Entry, error)
}

func (m *MockLokiClient) SearchFullRange(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) (*LogResult, error) {
	return m.mockSearchFullRange(queryStr, limit, start, end, direction, step, interval)
}

func (m *MockLokiClient) queryAndStream(result *LogResult, lastEntry []*Entry, lokiResponse *LokiResponse, queryStr string, bs int64, start, end time.Time, direction string, step, interval string) (int64, []*Entry, error) {
	return m.mockQueryAndStream(result, lastEntry, lokiResponse, queryStr, bs, start, end, direction, step, interval)
}

type MockCommonClient struct {
	mockGet  func(base string, endpoint string, headers http.Header, basicAuth *common.BasicAuth, responseStruct interface{}, querystring map[string]string) (*http.Response, error)
	mockPost func(base string, endpoint string, headers http.Header, basicAuth *common.BasicAuth, payload io.Reader, responseStruct interface{}, querystring map[string]string) (*http.Response, error)
	mockDo   func(ar *common.APIRequest, responseStruct interface{}, options ...interface{}) (*http.Response, error)
}

func (m *MockCommonClient) Get(base string, endpoint string, headers http.Header, basicAuth *common.BasicAuth, responseStruct interface{}, querystring map[string]string) (*http.Response, error) {
	return m.mockGet(base, endpoint, headers, basicAuth, responseStruct, querystring)
}

func (m *MockCommonClient) Post(base string, endpoint string, headers http.Header, basicAuth *common.BasicAuth, payload io.Reader, responseStruct interface{}, querystring map[string]string) (*http.Response, error) {
	return m.mockPost(base, endpoint, headers, basicAuth, payload, responseStruct, querystring)
}

func (m *MockCommonClient) Do(ar *common.APIRequest, responseStruct interface{}, options ...interface{}) (*http.Response, error) {
	return m.mockDo(ar, responseStruct, options...)
}

func TestBuildQueryString(t *testing.T) {
	t.Run("Valid Parameters", func(t *testing.T) {
		sf := SearchFilter{
			NamespaceFilter: []string{"default", "kube-system"},
			PodFilter:       []string{"pod1", "pod2"},
			ContainerFilter: []string{"container1"},
			Keywords:        "error",
		}
		queryStr, err := BuildQueryString(sf)
		assert.NoError(t, err)
		expected := `{namespace=~"default|kube-system", pod=~"pod1|pod2", container=~"container1"}|~"error"`
		assert.Equal(t, expected, queryStr)
	})

	t.Run("Missing Filters", func(t *testing.T) {
		sf := SearchFilter{}
		queryStr, err := BuildQueryString(sf)
		assert.Error(t, err)
		assert.Equal(t, "", queryStr)
	})
}

func TestLokiClient_ExportLogs(t *testing.T) {
	mockClient := &LokiClient{
		Host: "http://localhost:3100",
		C:    common.NewHTTPClient(),
	}

	t.Run("Size Not Zero", func(t *testing.T) {
		sf := SearchFilter{
			Size: 10,
		}
		var buf bytes.Buffer
		logs, err := mockClient.ExportLogs(sf, &buf)
		assert.Error(t, err)
		assert.Equal(t, int64(0), logs.Total)
	})
}

func TestLokiClient_SearchFullRange(t *testing.T) {
	mockClient := &MockLokiClient{}

	t.Run("Successful Search", func(t *testing.T) {
		queryStr := "{namespace=~\"default\"}"
		start := time.Now().Add(-time.Hour)
		end := time.Now()

		// Mocking the behavior of SearchFullRange
		mockClient.mockSearchFullRange = func(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) (*LogResult, error) {
			return &LogResult{
				Logs: []Entry{
					{Line: "log1", Timestamp: time.Now()},
					{Line: "log2", Timestamp: time.Now()},
				},
			}, nil
		}

		result, err := mockClient.SearchFullRange(queryStr, 10, start, end, "asc", "", "")
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(2), int64(len(result.Logs)))
	})

	t.Run("Error on Query", func(t *testing.T) {
		queryStr := "{namespace=~\"default\"}"
		start := time.Now().Add(-time.Hour)
		end := time.Now()

		// Mocking the behavior of SearchFullRange to return an error
		mockClient.mockSearchFullRange = func(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) (*LogResult, error) {
			return nil, errors.New("query error")
		}

		result, err := mockClient.SearchFullRange(queryStr, 10, start, end, "asc", "", "")
		assert.Error(t, err)
		assert.Nil(t, result)
	})
}

func TestLokiClient_queryAndStream(t *testing.T) {
	mockClient := &MockLokiClient{}

	t.Run("Successful Query", func(t *testing.T) {
		queryStr := "{namespace=~\"default\"}"
		lokiResponse := &LokiResponse{Data: &Data{ResultType: ResultTypeStream}}

		mockClient.mockQueryAndStream = func(result *LogResult, lastEntry []*Entry, lokiResponse *LokiResponse, queryStr string, bs int64, start, end time.Time, direction string, step, interval string) (int64, []*Entry, error) {
			return 2, []*Entry{
				{Line: "log1", Timestamp: time.Now()},
				{Line: "log2", Timestamp: time.Now()},
			}, nil
		}

		resultLength, entries, err := mockClient.queryAndStream(&LogResult{}, nil, lokiResponse, queryStr, 10, time.Now(), time.Now(), "asc", "", "")
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resultLength)
		assert.NotNil(t, entries)
		assert.Equal(t, "log1", entries[0].Line)
	})

	t.Run("Error on Response Parsing", func(t *testing.T) {
		queryStr := "{namespace=~\"default\"}"
		lokiResponse := &LokiResponse{Data: &Data{ResultType: ResultTypeStream}}

		mockClient.mockQueryAndStream = func(result *LogResult, lastEntry []*Entry, lokiResponse *LokiResponse, queryStr string, bs int64, start, end time.Time, direction string, step, interval string) (int64, []*Entry, error) {
			return 0, nil, errors.New("query error")
		}

		resultLength, entries, err := mockClient.queryAndStream(&LogResult{}, nil, lokiResponse, queryStr, 10, time.Now(), time.Now(), "asc", "", "")
		assert.Error(t, err)
		assert.Equal(t, int64(0), resultLength)
		assert.Nil(t, entries)
	})
}

func TestLokiClient_printStream(t *testing.T) {
	mockClient := &LokiClient{}

	t.Run("Successful Print", func(t *testing.T) {
		streams := Streams{
			{Labels: map[string]string{"namespace": "default"}, Values: []Entry{{Line: "log1", Timestamp: time.Now()}}},
		}
		out := &LogResult{}

		printed, lastEntries := mockClient.printStream("asc", streams, out, nil)
		assert.Equal(t, int64(1), printed)
		assert.NotNil(t, lastEntries)
		assert.Equal(t, "log1", lastEntries[0].Line)
	})

	t.Run("Empty Streams", func(t *testing.T) {
		streams := Streams{}
		out := &LogResult{}

		printed, lastEntries := mockClient.printStream("asc", streams, out, nil)
		assert.Equal(t, int64(0), printed)
		assert.Empty(t, lastEntries)
	})
}

func TestLokiClient_BuilderQuery(t *testing.T) {
	mockClient := &LokiClient{}

	t.Run("Build Query with All Parameters", func(t *testing.T) {
		start := time.Now().Add(-time.Hour)
		end := time.Now()
		query := mockClient.BuilderQuery("some_query", 10, start, end, "asc", "10s", "1m")

		assert.Equal(t, "10", query[LIMIT])
		assert.Equal(t, "FORWARD", query[DIRECTION])
		assert.Equal(t, strconv.FormatInt(end.UnixNano(), 10), query[END])
		assert.Equal(t, strconv.FormatInt(start.UnixNano(), 10), query[START])
		assert.Equal(t, "some_query", query[QUERYSTR])
	})

	t.Run("Build Query without Start Time", func(t *testing.T) {
		end := time.Now()
		query := mockClient.BuilderQuery("some_query", 10, time.Time{}, end, "asc", "", "")

		assert.Equal(t, "10", query[LIMIT])
		assert.Equal(t, "FORWARD", query[DIRECTION])
		assert.Equal(t, strconv.FormatInt(end.UnixNano(), 10), query[END])
		assert.NotContains(t, query, START)
		assert.Equal(t, "some_query", query[QUERYSTR])
	})
}
