//nolint:all
package lokiclient

import (
	"fmt"
	"io"
	"strings"

	log "github.com/sirupsen/logrus"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/common"

	"sort"
	"strconv"
	"time"
)

const (
	DefaultPatterns1 = ``
	DefaultPatterns2 = `| json |line_format "{{.log}}"`
)

// Log search result
type Logs struct {
	Total   int64         `json:"total" description:"total number of matched results"`
	Records []interface{} `json:"records,omitempty" description:"actual array of results"`
}

type Record struct {
	Log       string `json:"log,omitempty" description:"log message"`
	Timestamp int64  `json:"timestamp,omitempty" description:"log timestamp"`
	Namespace string `json:"namespace,omitempty" description:"namespace"`
	Pod       string `json:"pod,omitempty" description:"pod name"`
	Container string `json:"container,omitempty" description:"container name"`
}

// Log statistics result
type Statistics struct {
	Containers int64 `json:"containers" description:"total number of containers"`
	Logs       int64 `json:"logs" description:"total number of logs"`
}

// Log count result by interval
type Histogram struct {
	Total   int64    `json:"total" description:"total number of logs"`
	Buckets []Bucket `json:"histograms" description:"actual array of histogram results"`
}

type Bucket struct {
	Time  int64 `json:"time" description:"timestamp"`
	Count int64 `json:"count" description:"total number of logs at intervals"`
}

// General query conditions
type SearchFilter struct {
	NamespaceFilter []string
	NamespaceSearch []string
	PodSearch       []string
	PodFilter       []string
	ContainerFilter []string
	Keywords        string

	Starttime time.Time
	Endtime   time.Time

	Interval string
	Sort     string
	Showtime string
	From     int64
	Size     int64
}

const (
	QUERYRANGE = "/loki/api/v1/query_range"
	QUERY      = "/loki/api/v1/query"
)

type LokiClient struct {
	// Client *resty.Client
	Host string
	C    common.Client
}

func (c *LokiClient) ExportLogs(sf SearchFilter, w io.Writer) (Logs, error) {
	var err error
	var resp *LogResult
	queryStr, err := BuildQueryString(sf)
	if err != nil {
		return Logs{}, fmt.Errorf("logs querystr err: %v", err)
	}

	// 使用 Logclient 中的方法进行日志搜索
	if sf.Size == 0 {
		resp, err = c.SearchFullRange(queryStr, sf.Size, sf.Starttime, sf.Endtime, sf.Sort, "", "")
	} else {
		return Logs{}, fmt.Errorf("sf.Size != 0")
	}
	if err != nil {
		return Logs{}, fmt.Errorf("failed to search logs: %v", err)
	}

	if resp == nil || len(resp.Logs) == 0 {
		return Logs{}, nil
	}

	log.Info("resp Content: " + resp.Logs[0].Line)
	log.Info("resp Timestamp: " + strconv.FormatInt(resp.Logs[0].Timestamp.UnixNano(), 10))

	l := Logs{}
	for _, logDetail := range resp.Logs {
		if logDetail.Line != "" {

			l.Records = append(l.Records, Record{

				Log:       logDetail.Line,
				Timestamp: logDetail.Timestamp.UnixNano(),
				Namespace: "",
				Container: "",
			})
		}
	}

	l.Total = int64(len(l.Records))

	// 将搜索到的日志内容写入 io.Writer 接口
	if sf.Sort == "desc" {
		// 倒序写入 todo
		for i := len(resp.Logs) - 1; i >= 0; i-- {
			logDetail := resp.Logs[i]
			logstr := logDetail.Line
			if sf.Showtime == "true" {
				logstr = fmt.Sprintf("%s: %s", logDetail.Timestamp.Format("2006-01-02 15:04:05"), logDetail.Line)

			}
			if _, err := fmt.Fprint(w, logstr); err != nil {
				return Logs{}, fmt.Errorf("failed to write log: %v", err)
			}
		}
	} else {
		for _, logDetail := range resp.Logs {
			logstr := logDetail.Line
			if sf.Showtime == "true" {
				logstr = fmt.Sprintf("%s: %s", logDetail.Timestamp.Format("2006-01-02 15:04:05"), logDetail.Line)

			}
			if _, err := fmt.Fprint(w, logstr); err != nil {
				return Logs{}, fmt.Errorf("failed to write log: %v", err)
			}
		}
	}

	return l, nil
}

func BuildQueryString(sf SearchFilter) (string, error) {
	if len(sf.NamespaceFilter) == 0 && len(sf.PodFilter) == 0 && len(sf.ContainerFilter) == 0 {
		return "", fmt.Errorf("invalid parameter req: %v", sf)
	}

	var (
		podList       string
		queryStr      string
		nsList        string
		containerList string
		queryList     []string
	)
	if len(sf.NamespaceFilter) != 0 {
		for _, ns := range sf.NamespaceFilter {
			nsList = nsList + ns + "|"
		}
		nsList = fmt.Sprintf(`namespace=~"%s"`, strings.TrimSuffix(nsList, "|"))
		queryList = append(queryList, nsList)
	}

	if len(sf.PodFilter) != 0 {
		for _, pod := range sf.PodFilter {
			podList = podList + pod + "|"
		}
		podList = fmt.Sprintf(`pod=~"%s"`, strings.TrimSuffix(podList, "|"))
		queryList = append(queryList, podList)
	}
	if len(sf.ContainerFilter) != 0 {
		for _, container := range sf.ContainerFilter {
			containerList = containerList + container + "|"
		}
		containerList = fmt.Sprintf(`container=~"%s"`, strings.TrimSuffix(containerList, "|"))
		queryList = append(queryList, containerList)
	}

	queryStr = "{" + strings.Join(queryList, ", ") + "}" + DefaultPatterns1
	if len(queryStr) > 0 && sf.Keywords != "" {
		queryStr = fmt.Sprintf(`%s|~"%s"`, queryStr, sf.Keywords)
	}

	return queryStr, nil
}

func (c *LokiClient) SearchFullRange(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) (*LogResult, error) {
	var (
		lokiResponse = &LokiResponse{}
		resultLength = DefaultBatchSize
		lastEntry    = make([]*Entry, 0)
		result       = &LogResult{}
	)
	var err error
	if limit == 0 {
		limit = DefaultBatchSize
	}
	// log.Info("start search SearchRange,time is: ", time.Now().String())

	for start.Before(end) {
		resultLength, lastEntry, err = c.queryAndStream(result, lastEntry, lokiResponse, queryStr, limit, start, end, direction, step, interval)
		if err != nil {
			return nil, fmt.Errorf("query failed: %+v", err)
		}

		if resultLength < limit-1 || resultLength == 0 {
			break
		}

		if direction == "asc" {
			start = lastEntry[0].Timestamp
		} else {
			end = lastEntry[0].Timestamp.Add(1 * time.Nanosecond)
		}
	}

	log.Info("end search SearchRange, time is: ", time.Now().String())
	return result, nil
}

func (c *LokiClient) queryAndStream(result *LogResult, lastEntry []*Entry, lokiResponse *LokiResponse,
	queryStr string, bs int64, start, end time.Time, direction string, step, interval string) (resultLength int64, Entry []*Entry, err error) {

	params := c.BuilderQuery(queryStr, bs, start, end, direction, step, interval)
	response, err := c.doQuery(QUERYRANGE, lokiResponse, params)
	if err != nil {
		return resultLength, nil, fmt.Errorf("query failed: %+v", err)
	}
	switch lokiResponse.Data.ResultType {
	case ResultTypeStream:
		resultLength, Entry = c.printStream(direction, response.Streams, result, lastEntry)
	}

	return resultLength, Entry, nil
}

func (c *LokiClient) doQuery(endpoint string, lokiResponse *LokiResponse, query map[string]string) (*LokiStructResp, error) {
	// _, err := c.Client.R().
	// 	SetHeader("Accept", "application/json").
	// 	SetQueryParams(query).
	// 	SetResult(lokiResponse).
	// 	Get(endpoint)
	// if err != nil {
	// 	return nil, err
	// }
	_, err := c.C.Get(c.Host, endpoint, nil, nil, lokiResponse, query)
	// log.Info(fmt.Sprintf("\n=====> lokiapi finished in %dms", time.Since(startTime)/time.Millisecond)) //Segment finished in xxms
	if err != nil {
		return nil, fmt.Errorf("query loki err: %v", err)
	}

	lokiResults, err := parseResponse(lokiResponse)
	if err != nil {
		return nil, err
	}
	return lokiResults, nil
}

func (c *LokiClient) BuilderQuery(queryStr string, limit int64, start, end time.Time, direction string, step, interval string) map[string]string {

	query := map[string]string{
		LIMIT:     strconv.FormatInt(limit, 10),
		DIRECTION: "BACKWARD",
		END:       strconv.FormatInt(end.UnixNano(), 10),
	}
	if direction == "asc" {
		query[DIRECTION] = "FORWARD"
	}
	if !start.IsZero() {
		query[START] = strconv.FormatInt(start.UnixNano(), 10)
	}

	if interval != "" {
		query[INTERVAL] = interval
	}

	if step != "" {
		query[STEP] = step
	}

	query[QUERYSTR] = queryStr

	return query
}

func (c *LokiClient) printStream(direction string, streams Streams, out *LogResult, lastEntry []*Entry) (int64, []*Entry) {
	allEntries := make([]streamEntryPair, 0)
	for _, s := range streams {
		for _, e := range s.Values {
			allEntries = append(allEntries, streamEntryPair{
				entry:  e,
				labels: s.Labels,
			})
		}
	}
	if len(allEntries) == 0 {
		return 0, nil
	}

	if direction == "asc" {
		sort.Slice(allEntries, func(i, j int) bool { return allEntries[i].entry.Timestamp.Before(allEntries[j].entry.Timestamp) })
	} else {
		sort.Slice(allEntries, func(i, j int) bool { return allEntries[i].entry.Timestamp.After(allEntries[j].entry.Timestamp) })
	}

	printed := int64(0)
	for _, e := range allEntries {
		if len(lastEntry) > 0 && e.entry.Timestamp == lastEntry[0].Timestamp {
			skip := false
			for _, le := range lastEntry {
				if e.entry.Line == le.Line {
					skip = true
				}
			}
			if skip {
				continue
			}
		}
		if len(e.entry.Line) != 0 {
			out.Logs = append(out.Logs, e.entry)
		}
		printed++
	}

	lel := []*Entry{}
	le := allEntries[len(allEntries)-1].entry
	for i, e := range allEntries {
		if e.entry.Timestamp.Equal(le.Timestamp) {
			lel = append(lel, &allEntries[i].entry)
		}
	}

	return printed, lel
}

func (c *LokiClient) SearchPodLogs(namespace, pod, container, keywords string, start, end time.Time, limit int64) ([]Entry, error) {
	return c.SearchLogs(SearchFilter{
		NamespaceFilter: []string{namespace},
		PodFilter:       []string{pod},
		ContainerFilter: []string{container},
		Keywords:        keywords,
		Starttime:       start,
		Endtime:         end,
		Size:            limit,
		Sort:            "asc",
	})
}

func (c *LokiClient) SearchLogs(sf SearchFilter) ([]Entry, error) {
	queryStr, err := BuildQueryString(sf)
	if err != nil {
		return nil, err
	}
	log.Info(queryStr)

	resp, err := c.SearchFullRange(queryStr, sf.Size, sf.Starttime, sf.Endtime, sf.Sort, "", "")
	if err != nil {
		return nil, err
	}

	if resp == nil || len(resp.Logs) == 0 {
		return nil, nil
	}
	return resp.Logs, nil
}
