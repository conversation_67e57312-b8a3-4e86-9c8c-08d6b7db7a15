package lokiclient

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
)

const (
	ResultTypeStream ResultType = "streams"
	ResultTypeScalar ResultType = "scalar"
	ResultTypeVector ResultType = "vector"
	ResultTypeMatrix ResultType = "matrix"
)

const (
	LIMIT     = "limit"
	DIRECTION = "direction"
	END       = "end"
	START     = "start"
	INTERVAL  = "interval"
	STEP      = "step"
	QUERYSTR  = "query"
)

const DefaultBatchSize = int64(5000)

// ResultType holds the type of the result
type ResultType string

// ResultValue interface mimics the promql.Value interface
type ResultValue interface {
	Type() ResultType
}

type LokiStructResp struct {
	Stats        *Stats       `json:"stats,omitempty"`
	Vector       Vector       `json:"vector,omitempty"`
	Scalars      Scalars      `json:"scalars,omitempty"`
	SampleStream SampleStream `json:"samplestream,omitempty"`
	Streams      Streams      `json:"streams,omitempty"`
}

type LogResult struct {
	Logs []Entry `json:"logs"`
}

type LokiResponse struct {
	Status string `json:"status"`
	Data   *Data  `json:"data"`
}

type Data struct {
	ResultType ResultType      `json:"resultType,omitempty"`
	Result     json.RawMessage `json:"result,omitempty"`
	Stats      *Stats          `json:"stats,omitempty"`
}

type Stats struct {
	Ingester *Ingester `json:"ingester,omitempty"`
	Store    *Store    `json:"store,omitempty"`
	Summary  *Summary  `json:"summary,omitempty"`
}

type Summary struct {
	BytesProcessedPerSecond int     `json:"bytesProcessedPerSecond,omitempty"`
	LinesProcessedPerSecond int     `json:"linesProcessedPerSecond,omitempty"`
	TotalBytesProcessed     int     `json:"totalBytesProcessed,omitempty"`
	TotalLinesProcessed     int     `json:"totalLinesProcessed,omitempty"`
	ExecTime                float64 `json:"execTime,omitempty"`
}
type Store struct {
	TotalChunksRef        int `json:"totalChunksRef,omitempty"`
	TotalChunksDownloaded int `json:"-"`
	ChunksDownloadTime    int `json:"-"`
	HeadChunkBytes        int `json:"headChunkBytes,omitempty"`
	HeadChunkLines        int `json:"headChunkLines,omitempty"`
	DecompressedBytes     int `json:"decompressedBytes,omitempty"`
	DecompressedLines     int `json:"decompressedLines,omitempty"`
	CompressedBytes       int `json:"compressedBytes,omitempty"`
	TotalDuplicates       int `json:"totalDuplicates,omitempty"`
}

type Ingester struct {
	TotalReached       int `json:"totalReached,omitempty"`
	TotalChunksMatched int `json:"totalChunksMatched,omitempty"`
	TotalBatches       int `json:"totalBatches,omitempty"`
	TotalLinesSent     int `json:"totalLinesSent,omitempty"`
	HeadChunkBytes     int `json:"headChunkBytes,omitempty"`
	HeadChunkLines     int `json:"headChunkLines,omitempty"`
	DecompressedBytes  int `json:"decompressedBytes,omitempty"`
	DecompressedLines  int `json:"decompressedLines,omitempty"`
	CompressedBytes    int `json:"compressedBytes,omitempty"`
	TotalDuplicates    int `json:"totalDuplicates,omitempty"`
}

func (Streams) Type() ResultType { return ResultTypeStream }
func (Vector) Type() ResultType  { return ResultTypeVector }
func (Scalars) Type() ResultType { return ResultTypeScalar }
func (Matrix) Type() ResultType  { return ResultTypeMatrix }

var dotPrecision = int(math.Log10(float64(second)))

const (
	minimumTick = time.Millisecond
	second      = int64(time.Second / minimumTick)
)

type Time int64

func (t *Time) UnmarshalJSON(b []byte) error {
	p := strings.Split(string(b), ".")
	switch len(p) {
	case 1:
		v, err := strconv.ParseInt(p[0], 10, 64)
		if err != nil {
			return err
		}
		*t = Time(v * second)

	case 2:
		v, err := strconv.ParseInt(p[0], 10, 64)
		if err != nil {
			return err
		}
		v *= second

		prec := dotPrecision - len(p[1])
		if prec < 0 {
			p[1] = p[1][:dotPrecision]
		} else if prec > 0 {
			p[1] = p[1] + strings.Repeat("0", prec)
		}

		va, err := strconv.ParseInt(p[1], 10, 32)
		if err != nil {
			return err
		}

		if len(p[0]) > 0 && p[0][0] == '-' && v+va > 0 {
			*t = Time(v+va) * -1
		} else {
			*t = Time(v + va)
		}

	default:
		return fmt.Errorf("invalid time %q", string(b))
	}
	return nil
}

type SampleValue float64

func (v *SampleValue) UnmarshalJSON(b []byte) error {
	if len(b) < 2 || b[0] != '"' || b[len(b)-1] != '"' {
		return fmt.Errorf("sample value must be a quoted string")
	}
	f, err := strconv.ParseFloat(string(b[1:len(b)-1]), 64)
	if err != nil {
		return err
	}
	*v = SampleValue(f)
	return nil
}

type Vector []Sample

type Sample struct {
	Metric LabelSet   `json:"metric"`
	Value  SamplePair `json:"value"`
}

type SamplePair struct {
	Timestamp Time
	Value     SampleValue
}

func (s *SamplePair) UnmarshalJSON(b []byte) error {
	v := [...]json.Unmarshaler{&s.Timestamp, &s.Value}
	return json.Unmarshal(b, &v)
}

type Scalars Scalar

type Scalar struct {
	Value     SampleValue `json:"value"`
	Timestamp Time        `json:"timestamp"`
}

type Matrix SampleStream

type SampleStream struct {
	Metric LabelSet     `json:"metric"`
	Values []SamplePair `json:"values"`
}

type LabelSet map[string]string

type Streams []Stream

type Stream struct {
	Values []Entry  `json:"values,omitempty"`
	Labels LabelSet `json:"stream,omitempty"`
}

type Entry struct {
	Timestamp time.Time `json:"timestamp"`
	Line      string    `json:"line"`
}

type streamEntryPair struct {
	entry  Entry
	labels LabelSet
}

func (e *Entry) MarshalJSON() ([]byte, error) {
	l, err := json.Marshal(e.Line)
	if err != nil {
		return nil, err
	}
	return []byte(fmt.Sprintf("[\"%d\",%s]", e.Timestamp.UnixNano(), l)), nil
}

func (e *Entry) UnmarshalJSON(data []byte) error {
	var unmarshal []string

	err := json.Unmarshal(data, &unmarshal)
	if err != nil {
		return err
	}

	t, err := strconv.ParseInt(unmarshal[0], 10, 64)
	if err != nil {
		return err
	}

	e.Timestamp = time.Unix(0, t)
	e.Line = unmarshal[1]

	return nil
}

func parseResponse(lokiLog *LokiResponse) (*LokiStructResp, error) {

	if lokiLog.Status != "success" {
		return nil, errors.New("get Log err")
	}
	result := &LokiStructResp{
		Stats: lokiLog.Data.Stats,
	}

	var err error

	switch lokiLog.Data.ResultType {
	case ResultTypeStream:
		err = UnmarshalProm(lokiLog.Data.Result, &result.Streams)
	case ResultTypeVector:
		err = UnmarshalProm(lokiLog.Data.Result, &result.Vector)
	case ResultTypeScalar:
		err = UnmarshalProm(lokiLog.Data.Result, &result.Scalars)
	case ResultTypeMatrix:
		err = UnmarshalProm(lokiLog.Data.Result, &result.SampleStream)
	default:
		return nil, fmt.Errorf("v1 endpoints do not support type %s", lokiLog.Data.ResultType)
	}

	if err != nil {
		return nil, err
	}

	return result, nil
}

func UnmarshalProm(result json.RawMessage, target interface{}) error {
	err := json.Unmarshal(result, &target)
	if err != nil {
		return err
	}
	return nil
}
