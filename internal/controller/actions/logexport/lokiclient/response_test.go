package lokiclient

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestParseResponse(t *testing.T) {
	t.Run("Error on non-success status", func(t *testing.T) {
		lokiLog := &LokiResponse{
			Status: "error",
		}
		resp, err := parseResponse(lokiLog)
		assert.Error(t, err)
		assert.Nil(t, resp)
	})

	t.Run("Error on unknown ResultType", func(t *testing.T) {
		lokiLog := &LokiResponse{
			Status: "success",
			Data: &Data{
				ResultType: "unknown",
				Result:     json.RawMessage(`{}`),
			},
		}
		resp, err := parseResponse(lokiLog)
		assert.Error(t, err)
		assert.Nil(t, resp)
	})
}

func TestTimeUnmarshalJSON(t *testing.T) {
	t.Run("Invalid format", func(t *testing.T) {
		var tm Time
		err := tm.UnmarshalJSON([]byte("invalid"))
		assert.Error(t, err)
	})
}

func TestSampleValueUnmarshalJSON(t *testing.T) {
	t.Run("Valid sample value", func(t *testing.T) {
		var v SampleValue
		err := v.UnmarshalJSON([]byte("\"123.456\""))
		assert.NoError(t, err)
		assert.Equal(t, SampleValue(123.456), v)
	})

	t.Run("Invalid sample value format", func(t *testing.T) {
		var v SampleValue
		err := v.UnmarshalJSON([]byte("123.456"))
		assert.Error(t, err)
	})
}

func TestEntryMarshalJSON(t *testing.T) {
	entry := &Entry{
		Timestamp: time.Now(),
		Line:      "Test log line",
	}

	data, err := entry.MarshalJSON()
	assert.NoError(t, err)

	var unmarshal []string
	err = json.Unmarshal(data, &unmarshal)
	assert.NoError(t, err)
	assert.Equal(t, entry.Line, unmarshal[1])
}

func TestEntryUnmarshalJSON(t *testing.T) {
	t.Run("Valid entry", func(t *testing.T) {
		entry := &Entry{}
		data := []byte("[\"1234567890123456789\",\"Test log line\"]")
		err := entry.UnmarshalJSON(data)
		assert.NoError(t, err)

		expectedTime := time.Unix(0, 1234567890123456789)
		assert.Equal(t, expectedTime, entry.Timestamp)
		assert.Equal(t, "Test log line", entry.Line)
	})

	t.Run("Invalid entry format", func(t *testing.T) {
		entry := &Entry{}
		data := []byte("[1234567890123456789, Test log line]") // Invalid JSON
		err := entry.UnmarshalJSON(data)
		assert.Error(t, err)
	})
}

func TestUnmarshalProm(t *testing.T) {
	t.Run("Invalid unmarshal", func(t *testing.T) {
		result := json.RawMessage(`invalid`)
		var streams Streams
		err := UnmarshalProm(result, &streams)
		assert.Error(t, err)
	})
}

func TestResultTypeMethods(t *testing.T) {
	var stream Streams
	assert.Equal(t, ResultTypeStream, stream.Type())

	var vector Vector
	assert.Equal(t, ResultTypeVector, vector.Type())

	var scalars Scalars
	assert.Equal(t, ResultTypeScalar, scalars.Type())

	var matrix Matrix
	assert.Equal(t, ResultTypeMatrix, matrix.Type())
}

func TestSamplePairUnmarshalJSON(t *testing.T) {
	t.Run("Invalid SamplePair format", func(t *testing.T) {
		var sp SamplePair
		data := []byte("[1234567890123456789, invalid]")
		err := sp.UnmarshalJSON(data)
		assert.Error(t, err)
	})
}

func TestLokiStructResp(t *testing.T) {
	t.Run("Empty Response", func(t *testing.T) {
		resp := &LokiStructResp{}
		assert.NotNil(t, resp)
		assert.Nil(t, resp.Streams)
	})
}
