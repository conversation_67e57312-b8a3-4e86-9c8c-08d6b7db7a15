package logexport

import (
	"bytes"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/common"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/lokiclient"
	"hero.ai/hero-controllers/internal/controller/config"
)

type LogExportQueryParameter struct { //nolint
	SF             lokiclient.SearchFilter
	StorageType    string
	BucketName     string
	BucketFilePath string
	TaskID         string
}

type LogExport struct {
	Log         logr.Logger
	LogExportQP LogExportQueryParameter
}

var (
	minioS3    minio.Client
	minioFS    minio.Client
	LokiClient *lokiclient.LokiClient
)

func init() {
	minioS3 = *NewMinioS3Client()
	minioFS = *NewMinioFSClient()
	LokiClient, _ = NewLokiClient(config.SC.LokiURL, common.NewHTTPClient())
}

func NewMinioS3Client() *minio.Client {
	minioClient, err := minio.New(config.EC.MinioS3.Url, &minio.Options{
		Creds:  credentials.NewStaticV4(config.EC.MinioS3.AccessKey, config.EC.MinioS3.SecretKey, ""),
		Secure: config.EC.MinioS3.Https,
	})
	if err != nil {
		fmt.Println("Initial Error, ", err.Error())
	}
	return minioClient
}

func NewMinioFSClient() *minio.Client {
	minioClient, err := minio.New(config.EC.MinioFS.Url, &minio.Options{
		Creds:  credentials.NewStaticV4(config.EC.MinioFS.AccessKey, config.EC.MinioFS.SecretKey, ""),
		Secure: config.EC.MinioFS.Https,
	})
	if err != nil {
		fmt.Println("Initial Error, ", err.Error())
	}
	return minioClient
}

// func NewLokiClient(host string) *lokiclient.LokiClient {
// 	return &lokiclient.LokiClient{
// 		Client: resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}).SetBaseURL(host),
// 	}
// }

func NewLokiClient(host string, htc common.Client) (*lokiclient.LokiClient, error) {
	var err error
	loki := &lokiclient.LokiClient{
		C:    htc,
		Host: host,
	}

	return loki, err
}

func (l *LogExport) ParseParameter(p *systemv1alpha1.SyncAction) error {
	var err error
	l.LogExportQP.StorageType = p.Spec.ExportFullLogAction.StorageType
	l.LogExportQP.SF.NamespaceFilter = strings.Split(p.Spec.ExportFullLogAction.Namespaces, ",")
	l.LogExportQP.BucketName = p.Spec.ExportFullLogAction.BucketName
	l.LogExportQP.BucketFilePath = p.Spec.ExportFullLogAction.BucketFilePath
	l.LogExportQP.SF.PodFilter = strings.Split(p.Spec.ExportFullLogAction.Pods, ",")

	if len(p.Spec.ExportFullLogAction.Containers) != 0 {
		l.LogExportQP.SF.ContainerFilter = strings.Split(p.Spec.ExportFullLogAction.Containers, ",")
	}

	l.LogExportQP.SF.Keywords = p.Spec.ExportFullLogAction.KeywordFilter
	l.LogExportQP.SF.From, _ = strconv.ParseInt(p.Spec.ExportFullLogAction.From, 10, 64)
	l.LogExportQP.SF.Size, _ = strconv.ParseInt(p.Spec.ExportFullLogAction.Size, 10, 64)
	l.LogExportQP.SF.Sort = p.Spec.ExportFullLogAction.Sort
	l.LogExportQP.SF.Showtime = p.Spec.ExportFullLogAction.ShowTime

	l.LogExportQP.SF.Starttime, err = timestamp2Time(p.Spec.ExportFullLogAction.StartTime, time.Now().AddDate(0, 0, -10))
	if err != nil {
		return err
	}

	l.LogExportQP.SF.Endtime, err = timestamp2Time(p.Spec.ExportFullLogAction.EndTime, time.Now())
	if err != nil {
		return err
	}

	if l.LogExportQP.SF.Starttime.After(l.LogExportQP.SF.Endtime) {
		l.LogExportQP.SF.Starttime = l.LogExportQP.SF.Endtime.AddDate(0, 0, -10)
	}

	if l.LogExportQP.SF.Showtime != "false" {
		l.LogExportQP.SF.Showtime = "true"
	}
	if l.LogExportQP.SF.Sort == "" {
		l.LogExportQP.SF.Sort = "desc"
	}

	return nil
}

func (l *LogExport) Call(p *systemv1alpha1.SyncAction) error {
	var buf bytes.Buffer
	_, err := LokiClient.ExportLogs(l.LogExportQP.SF, &buf)
	if err != nil {
		return err
	}
	//p.Status.ExportFullLogStatus.Total = result.Total
	bucket_file_name := l.LogExportQP.BucketFilePath + "/" + l.LogExportQP.SF.PodFilter[0] + ".txt" //nolint
	reader := bytes.NewReader(buf.Bytes())
	if l.LogExportQP.StorageType == "s3" {
		_, err = minioS3.PutObject(context.Background(), l.LogExportQP.BucketName, bucket_file_name, reader, int64(len(buf.Bytes())),
			minio.PutObjectOptions{ContentType: "application/octet-stream"})
	} else {
		_, err = minioFS.PutObject(context.Background(), l.LogExportQP.BucketName, bucket_file_name, reader, int64(len(buf.Bytes())),
			minio.PutObjectOptions{ContentType: "application/octet-stream"})
	}
	if err != nil {
		return err
	}
	return nil
}

type Default struct {
	Log logr.Logger
}

func (d *Default) ParseParameter() {
	d.Log.Info("default parse parameter ...")
}

func (d *Default) Call() {
	d.Log.Info("default action call ...")
}

func timestamp2Time(timeStamp string, defaultTime time.Time) (time.Time, error) {
	if timeStamp == "" || len(timeStamp) == 0 {
		return defaultTime, nil
	}

	sec, err := strconv.ParseInt(timeStamp, 10, 64)
	if err != nil {
		return defaultTime, err
	}
	newSec := sec / int64(time.Second)
	nSec := sec % int64(time.Second)
	return time.Unix(newSec, nSec), nil
}
