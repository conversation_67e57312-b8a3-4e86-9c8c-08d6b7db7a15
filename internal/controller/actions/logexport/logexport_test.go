package logexport

import (
	"bytes"
	"context"
	"testing"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/common"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/lokiclient"
)

type MockLokiClient struct {
	mock.Mock
}

func (m *MockLokiClient) ExportLogs(filter lokiclient.SearchFilter, buf *bytes.Buffer) (*lokiclient.LogResult, error) {
	args := m.Called(filter, buf)
	return args.Get(0).(*lokiclient.LogResult), args.Error(1)
}

type MockMinioClient struct {
	mock.Mock
}

func (m *MockMinioClient) PutObject(ctx context.Context, bucketName, objectName string, reader *bytes.Reader, size int64, opts minio.PutObjectOptions) (minio.UploadInfo, error) {
	args := m.Called(ctx, bucketName, objectName, reader, size, opts)
	return args.Get(0).(minio.UploadInfo), args.Error(1)
}

func (m *MockMinioClient) MakeBucket(ctx context.Context, bucketName string, opts minio.MakeBucketOptions) error {
	args := m.Called(ctx, bucketName, opts)
	return args.Error(0)
}

func TestNewMinioS3Client(t *testing.T) {
	client := NewMinioS3Client()
	assert.NotNil(t, client, "Minio S3 client should not be nil")
}

func TestNewMinioFSClient(t *testing.T) {
	client := NewMinioFSClient()
	assert.NotNil(t, client, "Minio FS client should not be nil")
}

func TestNewLokiClient(t *testing.T) {
	client, err := NewLokiClient("http://localhost:3100", common.NewHTTPClient())
	assert.NoError(t, err, "Should not return an error when creating a Loki client")
	assert.NotNil(t, client, "Loki client should not be nil")
}

func TestParseParameter(t *testing.T) {
	logExport := &LogExport{}
	param := &systemv1alpha1.SyncAction{
		Spec: systemv1alpha1.SyncActionSpec{
			ExportFullLogAction: systemv1alpha1.ExportFullLogParameters{
				StorageType:    "s3",
				BucketName:     "test-bucket",
				BucketFilePath: "logs",
				Namespaces:     "namespace1,namespace2",
				Pods:           "pod1,pod2",
				KeywordFilter:  "error",
				From:           "1",
				Size:           "10",
				Sort:           "asc",
				ShowTime:       "true",
				StartTime:      "1234567890",
				EndTime:        "1234567891",
			},
		},
	}

	err := logExport.ParseParameter(param)
	assert.NoError(t, err, "Should parse parameters without error")
	assert.Equal(t, logExport.LogExportQP.StorageType, "s3")
	assert.Equal(t, logExport.LogExportQP.BucketName, "test-bucket")
	assert.Equal(t, logExport.LogExportQP.BucketFilePath, "logs")
	assert.Equal(t, logExport.LogExportQP.SF.NamespaceFilter, []string{"namespace1", "namespace2"})
	assert.Equal(t, logExport.LogExportQP.SF.PodFilter, []string{"pod1", "pod2"})
	assert.Equal(t, logExport.LogExportQP.SF.Keywords, "error")
}

func TestTimestamp2Time(t *testing.T) {
	defaultTime := time.Now()
	tests := []struct {
		name      string
		timeStamp string
		want      time.Time
		wantErr   bool
	}{
		{"empty timestamp", "", defaultTime, false},
		{"invalid timestamp", "invalid", defaultTime, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := timestamp2Time(tt.timeStamp, defaultTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("timestamp2Time() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, got, tt.want, "Should return expected time")
		})
	}
}
