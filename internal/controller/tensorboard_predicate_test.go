package controller

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/event"
)

func TestTensorboardPredicate_Create(t *testing.T) {
	predicate := NewtensorboardPredicate()

	// Test Create event
	e := event.CreateEvent{
		Object: &systemv1alpha1.Tensorboard{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-tensorboard",
				Namespace: "default",
			},
		},
	}
	result := predicate.Create(e)
	assert.True(t, result, "Create event should always return true")
}

func TestTensorboardPredicate_Update(t *testing.T) {
	predicate := NewtensorboardPredicate()

	// Test Update event for Tensorboard
	oldTensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}
	newTensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}

	e := event.UpdateEvent{
		ObjectOld: oldTensorboard,
		ObjectNew: newTensorboard,
	}
	result := predicate.Update(e)
	assert.True(t, result, "Update event for Tensorboard should return true")

	// Test Update event for Pod
	oldPod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}
	newPod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodSucceeded,
		},
	}

	ePod := event.UpdateEvent{
		ObjectOld: oldPod,
		ObjectNew: newPod,
	}
	result = predicate.Update(ePod)
	assert.True(t, result, "Update event for Pod with status change should return true")

	// Test Update event for Pod without status change
	oldPodSame := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}
	newPodSame := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}

	ePodSame := event.UpdateEvent{
		ObjectOld: oldPodSame,
		ObjectNew: newPodSame,
	}
	result = predicate.Update(ePodSame)
	assert.False(t, result, "Update event for Pod without status change should return false")
}

func TestTensorboardPredicate_Delete(t *testing.T) {
	predicate := NewtensorboardPredicate()

	// Create a tensorboard object
	tensorboard := &systemv1alpha1.Tensorboard{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-tensorboard",
			Namespace: "default",
		},
	}

	// Test Delete event
	e := event.DeleteEvent{
		Object: tensorboard,
	}
	result := predicate.Delete(e)
	assert.True(t, result, "First delete event should return true")

	// Simulate rapid delete
	result = predicate.Delete(e)
	assert.False(t, result, "Second delete event within 1 second should return false")
	time.Sleep(2 * time.Second) // Wait for the next delete to be valid

	result = predicate.Delete(e)
	assert.True(t, result, "Third delete event after 2 seconds should return true")
}

func TestTensorboardPredicate_Generic(t *testing.T) {
	predicate := NewtensorboardPredicate()

	// Test Generic event
	e := event.GenericEvent{
		Object: &systemv1alpha1.Tensorboard{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-tensorboard",
				Namespace: "default",
			},
		},
	}
	result := predicate.Generic(e)
	assert.True(t, result, "Generic event should always return true")
}
