package event

import (
	"context"
	"fmt"
	"sync"
	"time"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/reference"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var (
	rd          *RemoveDup
	idleTimeout = 60 * time.Second
	timeCheck   = 10 * time.Second
	prefix      = "job-"
)

type RemoveDup struct {
	data    map[string]time.Time
	timeout time.Duration
	mutex   sync.Mutex
	timer   *time.Timer
}

func NewRemoveDup(timeout time.Duration) *RemoveDup { //nolint
	rp := &RemoveDup{
		data:    make(map[string]time.Time),
		timeout: timeout,
	}

	rp.startClean()
	return rp
}

func (rd *RemoveDup) Add(key string) {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	rd.data[key] = time.Now().Add(rd.timeout)
}

func (rd *RemoveDup) Exists(key string) bool {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	if _, ok := rd.data[key]; !ok {
		return false
	}

	return true
}

func (rd *RemoveDup) startClean() {
	rd.timer = time.NewTimer(timeCheck)
	go func() {
		for {
			<-rd.timer.C
			rd.mutex.Lock()
			expiredKeys := make([]string, 0)
			now := time.Now()
			for key, expiration := range rd.data {
				if now.After(expiration) {
					expiredKeys = append(expiredKeys, key)
				}
			}
			for _, key := range expiredKeys {
				delete(rd.data, key)
			}
			rd.mutex.Unlock()
			rd.timer.Reset(timeCheck)
		}
	}()
}

func init() {
	rd = NewRemoveDup(idleTimeout)
}

func NewEventRecord(cli client.Client, scheme *runtime.Scheme) *EventRecord { //nolint
	return &EventRecord{
		client: cli,
		scheme: scheme,
	}
}

type EventRecord struct {
	client client.Client
	scheme *runtime.Scheme
	reason string
}

func (e *EventRecord) SetReason(reason string) {
	e.reason = reason
}

func (e *EventRecord) EventRecord(ctx context.Context, obj client.Object, eventtype, reason, message string) error {
	if !rd.Exists(prefix + obj.GetName() + reason + e.reason) {
		rd.Add(prefix + obj.GetName() + reason + e.reason)

		ref, err := reference.GetReference(e.scheme, obj)
		if err != nil {
			klog.Errorf("%s getReference failed: %s", obj.GetObjectKind().GroupVersionKind().Kind, err.Error())
			return err
		}
		evt := e.makeEvent(ref, nil, eventtype, reason, message, obj.GetObjectKind().GroupVersionKind().Kind)
		return e.client.Create(ctx, evt)
	}

	return nil
}

func (e *EventRecord) Eventf(object client.Object, eventtype, reason, messageFmt string, args ...interface{}) {
	message := fmt.Sprintf(messageFmt, args...)
	key := fmt.Sprintf("%s/%s/%s,%s,%s,%s", prefix, object.GetNamespace(), object.GetName(), eventtype, reason, message)
	if !rd.Exists(key) {
		rd.Add(key)
		ref, err := reference.GetReference(e.scheme, object)
		if err != nil {
			klog.Errorf("%s getReference failed: %s", object.GetObjectKind().GroupVersionKind().Kind, err.Error())
		}
		evt := e.makeEvent(ref, nil, eventtype, reason, message, object.GetObjectKind().GroupVersionKind().Kind)
		err = e.client.Create(context.TODO(), evt)
		if err != nil {
			klog.Errorf("make event error: %s", err)
		}
	}
}

func (e *EventRecord) makeEvent(ref *v1.ObjectReference, annotations map[string]string, eventtype, reason, message, reskind string) *v1.Event {
	t := metav1.Time{Time: time.Now()}
	namespace := ref.Namespace
	if namespace == "" {
		namespace = metav1.NamespaceDefault
	}
	return &v1.Event{
		ObjectMeta: metav1.ObjectMeta{
			Name:        fmt.Sprintf("%v.%x", ref.Name, t.UnixNano()),
			Namespace:   namespace,
			Annotations: annotations,
			Labels:      map[string]string{"system.hero.ai": "event"},
		},
		InvolvedObject: *ref,
		Reason:         reason,
		Message:        message,
		FirstTimestamp: t,
		LastTimestamp:  t,
		Type:           eventtype,
		Source: v1.EventSource{
			Component: reskind,
		},
	}
}
