package event

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func TestNewRemoveDup(t *testing.T) {
	red := NewRemoveDup(10 * time.Second)
	assert.NotNil(t, red)
	assert.Equal(t, 10*time.Second, red.timeout)
}

func TestAdd(t *testing.T) {
	red := NewRemoveDup(10 * time.Second)
	key := "test-key"
	red.Add(key)

	assert.True(t, red.Exists(key))
}

func TestExists(t *testing.T) {
	red := NewRemoveDup(10 * time.Second)
	key := "test-key"
	assert.False(t, red.Exists(key))

	red.Add(key)
	assert.True(t, red.Exists(key))
}

func TestNewEventRecord(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	er := NewEventRecord(k8sFakeClient, scheme)
	assert.NotNil(t, er)
}

func TestSetReason(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	er := NewEventRecord(k8sFakeClient, scheme)
	reason := "test-reason"
	er.SetReason(reason)
	assert.Equal(t, reason, er.reason)
}

func TestEventRecord(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	er := NewEventRecord(k8sFakeClient, scheme)
	obj := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
		},
	}

	eventType := "Normal"
	reason := "TestReason"
	message := "This is a test message."

	err := er.EventRecord(context.TODO(), obj, eventType, reason, message)
	assert.NoError(t, err)
}

func TestMakeEvent(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	er := NewEventRecord(k8sFakeClient, scheme)

	ref := &v1.ObjectReference{
		Kind:      "Pod",
		Namespace: "default",
		Name:      "test-pod",
	}

	eventType := "Normal"
	reason := "TestReason"
	message := "This is a test message."
	reskind := "Pod"

	event := er.makeEvent(ref, nil, eventType, reason, message, reskind)

	assert.NotNil(t, event)
	assert.Equal(t, ref.Name, event.InvolvedObject.Name)
	assert.Equal(t, reason, event.Reason)
	assert.Equal(t, message, event.Message)
	assert.Equal(t, eventType, event.Type)
}
