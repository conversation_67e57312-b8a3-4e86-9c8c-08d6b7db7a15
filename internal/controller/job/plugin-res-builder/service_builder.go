package pluginResBuilder

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

type ServicePara struct {
	Name           string
	JobName        string
	JobUID         interface{}
	Namespace      string
	Port           int
	TargetPort     int
	Selector       map[string]string
	CustomizePorts map[string]int
	job            *systemv1alpha1.Notebook
}

type ServiceParaTJ struct {
	ServicePara
	jobTJ     *systemv1alpha1.TrainingJob
	AddLabels map[string]string
}

type ServiceBuilder struct {
}

func (sb *ServiceBuilder) BaseBuild(servicePara *ServicePara) *v1.Service {
	selector := map[string]string{"volcano.sh/job-name": vcjobbuilder.VcjobNameNotebookPrefix + servicePara.JobName, "volcano.sh/job-namespace": servicePara.Namespace}
	if len(servicePara.Selector) > 0 {
		selector = servicePara.Selector
	}
	return &v1.Service{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Service",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      servicePara.Name,
			Namespace: servicePara.Namespace,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(servicePara.job, systemv1alpha1.SchemeBuilder.GroupVersion.WithKind("Notebook")),
			},
		},
		Spec: v1.ServiceSpec{
			Ports: []v1.ServicePort{
				{
					Port:       int32(servicePara.Port),
					TargetPort: intstr.FromInt(servicePara.TargetPort),
				},
			},

			Selector: selector,
		},
		Status: v1.ServiceStatus{},
	}
}

func (sb *ServiceBuilder) Build(servicePara *ServicePara) *v1.Service {
	svc := sb.BaseBuild(servicePara)
	if servicePara.CustomizePorts != nil && len(servicePara.CustomizePorts) > 0 {
		svc.Spec.Ports = []v1.ServicePort{}
		for name, port := range servicePara.job.Spec.CustomizePorts {
			svc.Spec.Ports = append(svc.Spec.Ports, v1.ServicePort{
				Name:       name,
				Port:       int32(port),
				TargetPort: intstr.FromInt(port),
			})
		}
	}
	return svc
}

func (sb *ServiceBuilder) BuildTJ(serviceParaTJ *ServiceParaTJ) *v1.Service {
	selector := map[string]string{
		"volcano.sh/job-name":      vcjobbuilder.VcjobNameTrainjobPrefix + serviceParaTJ.JobName,
		"volcano.sh/job-namespace": serviceParaTJ.Namespace,
	}
	for k, v := range serviceParaTJ.AddLabels {
		if _, ok := selector[k]; !ok {
			selector[k] = v
		}
	}

	return &v1.Service{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Service",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      serviceParaTJ.Name,
			Namespace: serviceParaTJ.Namespace,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(serviceParaTJ.jobTJ, systemv1alpha1.SchemeBuilder.GroupVersion.WithKind("TrainingJob")),
			},
		},
		Spec: v1.ServiceSpec{
			Ports: []v1.ServicePort{
				{
					Port:       int32(serviceParaTJ.Port),
					TargetPort: intstr.FromInt(serviceParaTJ.TargetPort),
				},
			},

			Selector: selector,
		},
		Status: v1.ServiceStatus{},
	}
}
