package pluginResBuilder

import (
	"testing"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/stretchr/testify/assert"
)

func TestServiceBuilder_Build(t *testing.T) {
	// 设置测试数据
	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	servicePara := &ServicePara{
		Name:       "test-service",
		Namespace:  "default",
		Port:       80,
		TargetPort: 8080,
		Selector:   map[string]string{"app": "test-app"},
		job:        job,
	}

	builder := &ServiceBuilder{}

	// 调用被测方法
	service := builder.Build(servicePara)

	// 验证生成的 Service 是否符合预期
	assert.Equal(t, "test-service", service.Name, "Service name should be 'test-service'")
	assert.Equal(t, "default", service.Namespace, "Service namespace should be 'default'")
	assert.Equal(t, int32(80), service.Spec.Ports[0].Port, "Service port should be 80")
	assert.Equal(t, int32(8080), service.Spec.Ports[0].TargetPort.IntVal, "Service target port should be 8080")
	assert.Equal(t, "test-app", service.Spec.Selector["app"], "Selector should contain 'app=test-app'")
}

func TestServiceBuilder_BuildTJ(t *testing.T) {
	// 设置测试数据
	jobTJ := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-training-job",
			Namespace: "default",
		},
	}
	serviceParaTJ := &ServiceParaTJ{
		ServicePara: ServicePara{
			Name:       "test-service-tj",
			JobName:    "test-training-job",
			Namespace:  "default",
			Port:       8081,
			TargetPort: 9090,
		},
		jobTJ: jobTJ,
		AddLabels: map[string]string{
			"extra-label": "extra-value",
		},
	}

	builder := &ServiceBuilder{}

	// 调用被测方法
	service := builder.BuildTJ(serviceParaTJ)

	// 验证生成的 Service 是否符合预期
	assert.Equal(t, "test-service-tj", service.Name, "Service name should be 'test-service-tj'")
	assert.Equal(t, "default", service.Namespace, "Service namespace should be 'default'")
	assert.Equal(t, int32(8081), service.Spec.Ports[0].Port, "Service port should be 8081")
	assert.Equal(t, int32(9090), service.Spec.Ports[0].TargetPort.IntVal, "Service target port should be 9090")
	assert.Equal(t, "extra-value", service.Spec.Selector["extra-label"], "Selector should contain 'extra-label=extra-value'")
	assert.Equal(t, vcjobbuilder.VcjobNameTrainjobPrefix+"test-training-job", service.Spec.Selector["volcano.sh/job-name"], "Selector should contain the correct job name")
	assert.Equal(t, "default", service.Spec.Selector["volcano.sh/job-namespace"], "Selector should contain the correct job namespace")
}
