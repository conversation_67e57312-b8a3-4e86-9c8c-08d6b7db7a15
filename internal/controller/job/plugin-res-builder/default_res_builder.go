package pluginResBuilder //nolint

import (
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/klog"
	batch "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

type Action interface {
	Create(job *systemv1alpha1.Notebook, serviceName string) error
	CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error
	Delete(job *systemv1alpha1.Notebook) error
	DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error
}

type UsableChecker interface {
	IsUsable(kind, namespace, jobName string) (bool, error)
}

type FrontAction interface {
	Create(job *systemv1alpha1.Notebook) error
	CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error
	Exist(job *systemv1alpha1.Notebook) bool
	Delete(job *systemv1alpha1.Notebook) error
	DeleteTJ(jobTJ *systemv1alpha1.TrainingJ<PERSON>, podSuffixList []string) error
	FrontPath(nameSpace, jobName string) map[string]string
	GetWebURL() string
}

type DefaultCapability struct {
	BackEndActions []Action
	FrontAction    FrontAction
	Kind           string
	BaseURLKey     *string
	OffNetPolicy   bool
}

func (dc *DefaultCapability) GetURL() string {
	return dc.FrontAction.GetWebURL()
}

func (dc *DefaultCapability) PreCreate(vcJob *batch.Job, notebookjob *systemv1alpha1.Notebook) error {

	if dc.OffNetPolicy {
		delete(vcJob.Spec.Plugins, "svc")
	}

	return nil
}

func (dc *DefaultCapability) Create(job *systemv1alpha1.Notebook, serviceName string) error {
	if dc.FrontAction != nil {
		if err := dc.FrontAction.Create(job); err != nil {
			return err
		}
	}

	for index, action := range dc.BackEndActions {
		if err := action.Create(job, serviceName); err != nil {
			klog.Error(err.Error())
			if dc.FrontAction != nil {
				if err := dc.FrontAction.Delete(job); err != nil {
					return err
				}
			}
			for i := 0; i < index; i++ {
				if err := dc.BackEndActions[i].Delete(job); err != nil {
					return err
				}
			}
			return err
		}
	}
	return nil
}

func (dc *DefaultCapability) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	if dc.FrontAction != nil {
		if err := dc.FrontAction.CreateTJ(jobTJ, podSuffixList); err != nil {
			return err
		}
	}

	for index, action := range dc.BackEndActions {
		if err := action.CreateTJ(jobTJ, podSuffixList); err != nil {
			if apierrors.IsAlreadyExists(err) {
				return err
			}
			klog.Error(err.Error())
			if dc.FrontAction != nil {
				if err := dc.FrontAction.DeleteTJ(jobTJ, podSuffixList); err != nil {
					return err
				}
			}
			for i := 0; i < index; i++ {
				if err := dc.BackEndActions[i].DeleteTJ(jobTJ, podSuffixList); err != nil {
					return err
				}
			}
			return err
		}
	}
	return nil
}

func (dc *DefaultCapability) Delete(job *systemv1alpha1.Notebook) error {
	if err := dc.FrontAction.Delete(job); err != nil {
		klog.Error(err.Error())
		return err
	}
	for _, action := range dc.BackEndActions {
		if err := action.Delete(job); err != nil {
			klog.Error(err.Error())
			return err
		}
	}

	return nil
}

func (dc *DefaultCapability) GetKind() *string {
	return &dc.Kind
}
