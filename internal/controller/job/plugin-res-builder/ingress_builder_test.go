package pluginResBuilder

import (
	"testing"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/stretchr/testify/assert"
)

func TestIngressBuilder_Build(t *testing.T) {
	// 设置测试数据
	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	ingressPara := &IngressPara{
		Name:        "test-ingress",
		Namespace:   "default",
		Path:        "/test-path",
		ServiceName: "test-service",
		Port:        80,
		job:         job,
	}

	ingressConfig := config.IngressConfig{
		Host:          "example.com",
		TlsSecretName: "test-tls-secret",
	}
	builder := &IngressBuilder{
		Config: ingressConfig,
	}

	// 调用被测方法
	ingress := builder.Build(ingressPara)

	// 验证生成的 Ingress 是否符合预期
	assert.Equal(t, "test-ingress", ingress.Name, "Ingress name should be 'test-ingress'")
	assert.Equal(t, "default", ingress.Namespace, "Ingress namespace should be 'default'")
	assert.Equal(t, "example.com", ingress.Spec.Rules[0].Host, "Ingress host should be 'example.com'")
	assert.Equal(t, "/test-path", ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths[0].Path, "Ingress path should be '/test-path'")
	assert.Equal(t, "test-service", ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths[0].Backend.Service.Name, "Service name should be 'test-service'")
	assert.Equal(t, int32(80), ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths[0].Backend.Service.Port.Number, "Service port should be 80")

	// 验证 TLS 设置
	assert.Equal(t, 1, len(ingress.Spec.TLS), "Ingress should have one TLS configuration")
	assert.Equal(t, "test-tls-secret", ingress.Spec.TLS[0].SecretName, "TLS secret name should be 'test-tls-secret'")
	assert.Equal(t, "example.com", ingress.Spec.TLS[0].Hosts[0], "TLS host should be 'example.com'")
}

func TestIngressBuilder_BuildTJ(t *testing.T) {
	// 设置测试数据
	jobTJ := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-training-job",
			Namespace: "default",
		},
	}
	ingressParaTJ := &IngressParaTJ{
		IngressPara: IngressPara{
			Name:        "test-ingress-tj",
			Namespace:   "default",
			Path:        "/training-path",
			ServiceName: "training-service",
			Port:        8080,
		},
		jobTJ: jobTJ,
	}

	ingressConfig := config.IngressConfig{
		Host:          "training.example.com",
		TlsSecretName: "",
	}
	builder := &IngressBuilder{
		Config: ingressConfig,
	}

	// 调用被测方法
	ingress := builder.BuildTJ(ingressParaTJ)

	// 验证生成的 Ingress 是否符合预期
	assert.Equal(t, "test-ingress-tj", ingress.Name, "Ingress name should be 'test-ingress-tj'")
	assert.Equal(t, "default", ingress.Namespace, "Ingress namespace should be 'default'")
	assert.Equal(t, "training.example.com", ingress.Spec.Rules[0].Host, "Ingress host should be 'training.example.com'")
	assert.Equal(t, "/training-path", ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths[0].Path, "Ingress path should be '/training-path'")
	assert.Equal(t, "training-service", ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths[0].Backend.Service.Name, "Service name should be 'training-service'")
	assert.Equal(t, int32(8080), ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths[0].Backend.Service.Port.Number, "Service port should be 8080")

	// 验证 TLS 未配置的情况
	assert.Equal(t, 0, len(ingress.Spec.TLS), "Ingress should not have any TLS configuration")
}
