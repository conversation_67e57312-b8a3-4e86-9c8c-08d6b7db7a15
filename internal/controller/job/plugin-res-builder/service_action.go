package pluginResBuilder

import (
	"context"
	"fmt"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type SelectorFuncType func(kind string, job *systemv1alpha1.Notebook) map[string]string

func DefaultSelectFunc(kind string, job *systemv1alpha1.Notebook) map[string]string {
	return map[string]string{}
}

type ServiceAction struct {
	Client       client.Client
	Kind         string
	SelectorFunc SelectorFuncType
	Port         int
	ServiceBuilder
}

func (sa *ServiceAction) Create(job *systemv1alpha1.Notebook, serviceName string) error {
	namespace := job.Namespace
	jobName := job.Name
	klog.Infof("Creating service for Job \"%s/%s\"'s %q capability", namespace, jobName, sa.Kind)

	var svc v1.Service
	err := sa.Client.Get(context.TODO(), types.NamespacedName{
		Namespace: namespace,
		Name:      serviceName,
	}, &svc)

	if !apierrors.IsNotFound(err) {
		return nil
	}

	servicePara := &ServicePara{
		Name:       serviceName,
		JobName:    jobName,
		Namespace:  namespace,
		Port:       sa.Port,
		TargetPort: sa.Port,
		job:        job,
		Selector:   sa.SelectorFunc(sa.Kind, job),
	}

	if serviceName == fmt.Sprintf("customize-%s", job.Name) {
		servicePara.CustomizePorts = job.Spec.CustomizePorts
	}
	err = sa.Client.Create(context.TODO(), sa.Build(servicePara))
	if err != nil {
		return err
	}
	klog.Infof("Created service \"%s/%s\" for Job \"%s/%s\"'s %q capability, port is %d, targetPort is %d",
		namespace, serviceName, namespace, jobName, sa.Kind, sa.Port, sa.Port)
	return nil
}

func (sa *ServiceAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	namespace := jobTJ.Namespace
	jobName := jobTJ.Name
	klog.Infof("Creating service for Job \"%s/%s\"'s %q capability", namespace, jobName, sa.Kind)

	for _, podSuffix := range podSuffixList {
		//serviceName := ServiceName(sa.Kind, jobName)
		//ifNotExist create
		var svc v1.Service
		err := sa.Client.Get(context.TODO(), types.NamespacedName{
			Namespace: namespace,
			Name:      PodServiceName(sa.Kind, jobName, podSuffix),
		}, &svc)

		if !apierrors.IsNotFound(err) {
			return nil
		}

		serviceParaTJ := &ServiceParaTJ{}
		serviceParaTJ.Name = PodServiceName(sa.Kind, jobName, podSuffix)
		serviceParaTJ.JobName = jobName
		serviceParaTJ.Namespace = namespace
		serviceParaTJ.Port = sa.Port
		serviceParaTJ.TargetPort = sa.Port
		serviceParaTJ.jobTJ = jobTJ
		serviceParaTJ.AddLabels = map[string]string{
			"podSuffixName": podSuffix,
		}

		err = sa.Client.Create(context.TODO(), sa.BuildTJ(serviceParaTJ))
		if err != nil {
			return err
		}
		klog.Infof("Created service \"%s/%s\" for Job \"%s/%s\"'s %q capability, port is %d, targetPort is %d",
			namespace, PodServiceName(sa.Kind, jobName, podSuffix), namespace, jobName, sa.Kind, sa.Port, sa.Port)
	}

	return nil
}

func (sa *ServiceAction) Delete(job *systemv1alpha1.Notebook) error {
	var svc *v1.Service
	err := sa.Client.Get(context.TODO(), types.NamespacedName{
		Namespace: job.Namespace,
		Name:      ServiceName(sa.Kind, job.Name),
	}, svc)
	if err != nil {
		return err
	}

	return sa.Client.Delete(context.TODO(), svc)
}

func (sa *ServiceAction) DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	var errRet error
	for _, podSuffix := range podSuffixList {
		var svc *v1.Service
		err := sa.Client.Get(context.TODO(), types.NamespacedName{
			Namespace: jobTJ.Namespace,
			Name:      PodServiceName(sa.Kind, jobTJ.Name, podSuffix),
		}, svc)
		if err != nil {
			return err
		}
		if err = sa.Client.Delete(context.TODO(), svc); err != nil {
			errRet = err
		}
	}

	return errRet
}

func ServiceName(kind string, jobName string) string {
	return kind + "-" + jobName
}

func PodServiceName(kind string, jobName string, podSuffix string) string {
	return kind + "-" + jobName + "-" + podSuffix + "-svc"
}
