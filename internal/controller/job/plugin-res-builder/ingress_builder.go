package pluginResBuilder //nolint

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	v1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

var pathType = v1.PathTypeImplementationSpecific

type IngressPara struct {
	Name           string
	JobName        string
	UID            interface{}
	Namespace      string
	Path           string
	CusPortAndPath map[int]string
	ServiceName    string
	RewritePath    *string
	Port           int
	job            *systemv1alpha1.Notebook
}

type IngressParaTJ struct {
	IngressPara
	jobTJ *systemv1alpha1.TrainingJob
}

type IngressBuilder struct {
	Config config.IngressConfig
}

func (ib *IngressBuilder) Build(ingressPara *IngressPara) *v1.Ingress {
	ingress := ib.BuildBaseNotebookIngress(ingressPara)
	if ingressPara.CusPortAndPath != nil && len(ingressPara.CusPortAndPath) > 0 {
		ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths = []v1.HTTPIngressPath{}
		for port, path := range ingressPara.CusPortAndPath {
			ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths = append(ingress.Spec.Rules[0].IngressRuleValue.HTTP.Paths, v1.HTTPIngressPath{
				Path:     path,
				PathType: &pathType,
				Backend: v1.IngressBackend{
					Service: &v1.IngressServiceBackend{
						Name: ingressPara.ServiceName,
						Port: v1.ServiceBackendPort{
							Number: int32(port),
						},
					},

					Resource: nil,
				},
			})
		}
	}

	return ingress
}

func (ib *IngressBuilder) BuildBaseNotebookIngress(ingressPara *IngressPara) *v1.Ingress {
	ingressClass := "nginx"
	ingress := &v1.Ingress{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Ingress",
			APIVersion: "networking.k8s.io",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      ingressPara.Name,
			Namespace: ingressPara.Namespace,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(ingressPara.job, systemv1alpha1.SchemeBuilder.GroupVersion.WithKind("Notebook")),
			},
		},
		Spec: v1.IngressSpec{
			IngressClassName: &ingressClass,
			Rules: []v1.IngressRule{
				{
					Host: ib.Config.Host,
					IngressRuleValue: v1.IngressRuleValue{
						HTTP: &v1.HTTPIngressRuleValue{
							Paths: []v1.HTTPIngressPath{
								{
									Path:     ingressPara.Path,
									PathType: &pathType,
									Backend: v1.IngressBackend{
										Service: &v1.IngressServiceBackend{
											Name: ingressPara.ServiceName,
											Port: v1.ServiceBackendPort{
												Number: int32(ingressPara.Port),
											},
										},

										Resource: nil,
									},
								},
							},
						},
					},
				},
			},
		},
		Status: v1.IngressStatus{},
	}

	if len(ib.Config.TlsSecretName) != 0 {
		ingress.Spec.TLS = []v1.IngressTLS{
			{
				Hosts:      []string{ib.Config.Host},
				SecretName: ib.Config.TlsSecretName,
			},
		}
	}

	if ingressPara.RewritePath != nil {
		if ingress.ObjectMeta.Annotations == nil {
			ingress.ObjectMeta.Annotations = map[string]string{}
		}
		ingress.ObjectMeta.Annotations["nginx.ingress.kubernetes.io/rewrite-target"] = *ingressPara.RewritePath
	}

	return ingress
}

func (ib *IngressBuilder) BuildTJ(ingressParaTJ *IngressParaTJ) *v1.Ingress {
	ingressClass := "nginx"
	ingress := &v1.Ingress{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Ingress",
			APIVersion: "networking.k8s.io",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      ingressParaTJ.Name,
			Namespace: ingressParaTJ.Namespace,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(ingressParaTJ.jobTJ, systemv1alpha1.SchemeBuilder.GroupVersion.WithKind("TrainingJob")),
			},
		},
		Spec: v1.IngressSpec{
			IngressClassName: &ingressClass,
			Rules: []v1.IngressRule{
				{
					Host: ib.Config.Host,
					IngressRuleValue: v1.IngressRuleValue{
						HTTP: &v1.HTTPIngressRuleValue{
							Paths: []v1.HTTPIngressPath{
								{
									Path:     ingressParaTJ.Path,
									PathType: &pathType,
									Backend: v1.IngressBackend{
										Service: &v1.IngressServiceBackend{
											Name: ingressParaTJ.ServiceName,
											Port: v1.ServiceBackendPort{
												Number: int32(ingressParaTJ.Port),
											},
										},

										Resource: nil,
									},
								},
							},
						},
					},
				},
			},
		},
		Status: v1.IngressStatus{},
	}

	if len(ib.Config.TlsSecretName) != 0 {
		ingress.Spec.TLS = []v1.IngressTLS{
			{
				Hosts:      []string{ib.Config.Host},
				SecretName: ib.Config.TlsSecretName,
			},
		}
	}

	if ingressParaTJ.RewritePath != nil {
		if ingress.ObjectMeta.Annotations == nil {
			ingress.ObjectMeta.Annotations = map[string]string{}
		}
		ingress.ObjectMeta.Annotations["nginx.ingress.kubernetes.io/rewrite-target"] = *ingressParaTJ.RewritePath
	}

	return ingress
}
