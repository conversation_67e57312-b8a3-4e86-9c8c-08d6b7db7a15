package pluginResBuilder

import (
	"context"
	"testing"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"github.com/stretchr/testify/assert"
)

func TestIngressAction_GetWebURL(t *testing.T) {
	webURL := "http://example.com"
	ia := &IngressAction{
		WebURL: &webURL,
	}

	result := ia.GetWebURL()
	assert.Equal(t, "http://example.com", result)
}

func TestIngressAction_Create(t *testing.T) {
	s := scheme.Scheme
	s.AddKnownTypes(v1.SchemeGroupVersion, &v1.Ingress{})

	k8sFakeClient := fake.NewClientBuilder().WithScheme(s).Build()
	ia := &IngressAction{
		Client:  k8sFakeClient,
		Kind:    "test-kind",
		Port:    8080,
		Regular: "/.*",
		IngressBuilder: IngressBuilder{
			Config: struct {
				Domain        string `yaml:"Domain"`
				Host          string `yaml:"Host"`
				TlsSecretName string `yaml:"TlsSecretName"`
			}{
				Domain: "http://localhost",
			},
		},
	}

	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
	}

	err := ia.Create(job)
	assert.NoError(t, err)

	ing := &v1.Ingress{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{
		Namespace: job.Namespace,
		Name:      ingressName(ia.Kind, job.Name),
	}, ing)
	assert.NoError(t, err)
	assert.Equal(t, "default", ing.Namespace)
	assert.Contains(t, ia.GetWebURL(), ia.Config.Domain)
}

func TestIngressAction_CreateTJ(t *testing.T) {
	s := scheme.Scheme
	s.AddKnownTypes(v1.SchemeGroupVersion, &v1.Ingress{})

	k8sFakeClient := fake.NewClientBuilder().WithScheme(s).Build()
	ia := &IngressAction{
		Client:  k8sFakeClient,
		Kind:    "test-kind",
		Port:    8080,
		Regular: "/.*",
	}

	jobTJ := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-training-job",
			Namespace: "default",
		},
	}

	err := ia.CreateTJ(jobTJ, []string{"pod1"})
	assert.NoError(t, err)

	ing := &v1.Ingress{}
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{
		Namespace: jobTJ.Namespace,
		Name:      podIngressName(ia.Kind, jobTJ.Name, "pod1"),
	}, ing)
	assert.NoError(t, err)
	assert.Equal(t, "default", ing.Namespace)
}

func TestIngressAction_Exist(t *testing.T) {
	s := scheme.Scheme
	s.AddKnownTypes(v1.SchemeGroupVersion, &v1.Ingress{})

	k8sFakeClient := fake.NewClientBuilder().WithScheme(s).Build()
	ia := &IngressAction{
		Client: k8sFakeClient,
		Kind:   "test-kind",
	}

	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "existing-job",
			Namespace: "default",
		},
	}

	ingress := &v1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      ingressName(ia.Kind, job.Name),
			Namespace: job.Namespace,
		},
	}
	_ = k8sFakeClient.Create(context.TODO(), ingress)

	// 调用 Exist 方法前，给 ing 分配内存
	ing := &v1.Ingress{}
	err := k8sFakeClient.Get(context.TODO(), types.NamespacedName{
		Namespace: job.Namespace,
		Name:      ingressName(ia.Kind, job.Name),
	}, ing)
	if err != nil {
		panic(err)
	}

	exists := ia.Exist(job)
	assert.False(t, exists)
}

func TestIngressAction_FrontPath(t *testing.T) {
	ia := &IngressAction{
		Kind: "test-kind",
	}

	result := ia.FrontPath("default", "test-job")
	assert.Equal(t, map[string]string{"path": "http://112.31.12.175:8280/test-kind/test-job"}, result)
}

func TestIngressAction_ingressURI(t *testing.T) {
	ia := &IngressAction{
		Kind: "test-kind",
	}

	// 测试 IngressPathFormat 为 nil 的情况
	result := ia.ingressURI("test-job")
	expected := "/test-kind/test-job"
	assert.Equal(t, expected, result, "Expected URI to be %s, got %s", expected, result)

	// 测试 IngressPathFormat 不为 nil 的情况
	format := "/%s/custom/%s"
	ia.IngressPathFormat = &format
	result = ia.ingressURI("test-job")
	expected = "/test-kind/custom/test-job"
	assert.Equal(t, expected, result, "Expected URI to be %s, got %s", expected, result)
}

func TestIngressAction_podIngressURI(t *testing.T) {
	ia := &IngressAction{
		Kind: "test-kind",
	}

	result := ia.podIngressURI("test-job", "pod1")
	expected := "/test-kind/test-job/pod1/"
	assert.Equal(t, expected, result, "Expected pod URI to be %s, got %s", expected, result)
}

func TestIngressName(t *testing.T) {
	result := ingressName("test-kind", "test-job")
	expected := "test-kind-test-job-ingress"
	assert.Equal(t, expected, result, "Expected ingress name to be %s, got %s", expected, result)
}

func TestPodIngressName(t *testing.T) {
	result := podIngressName("test-kind", "test-job", "pod1")
	expected := "test-kind-test-job-pod1-ingress"
	assert.Equal(t, expected, result, "Expected pod ingress name to be %s, got %s", expected, result)
}
