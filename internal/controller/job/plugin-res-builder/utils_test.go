package pluginResBuilder

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestToJSON(t *testing.T) {
	// Setup test data
	type TestStruct struct {
		Name  string `json:"name"`
		Value int    `json:"value"`
	}

	testData := TestStruct{
		Name:  "test",
		Value: 123,
	}

	// Act
	jsonBytes := ToJSON(testData)

	// Assert
	expectedJSON, err := json.Marshal(testData)
	assert.NoError(t, err, "Marshaling with json.Marshal should not return an error")
	assert.Equal(t, expectedJSON, jsonBytes, "ToJSON should produce the expected JSON output")

	// Test with nil input
	nilJSON := ToJSON(nil)
	assert.Equal(t, []byte("null"), nilJSON, "ToJSON should return 'null' for nil input")

	// Test with an empty map
	emptyMap := map[string]string{}
	emptyMapJSON := ToJSON(emptyMap)
	assert.Equal(t, []byte("{}"), emptyMapJSO<PERSON>, "ToJSON should return '{}' for an empty map")
}
