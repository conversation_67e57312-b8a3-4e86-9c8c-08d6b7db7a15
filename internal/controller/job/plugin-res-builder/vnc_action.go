package pluginResBuilder

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"

	"sigs.k8s.io/controller-runtime/pkg/client"
)

type VncAction struct {
	Client client.Client
	Port   int
	Kind   string

	RewritePath   *string
	DeleteTokenFn func(string, string) error
}

func (va *VncAction) Create(job *systemv1alpha1.Notebook, serviceName string) error {

	return nil
}

func (va *VncAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {

	return nil
}

func (va *VncAction) Exist(job *systemv1alpha1.Notebook) bool {
	return true
}

func (va *VncAction) Delete(job *systemv1alpha1.Notebook) error {

	if err := va.DeleteTokenFn(job.Namespace, job.Name); err != nil {
		return err
	}
	return nil
}

func (va *VncAction) DeleteTJ(jobTJ *systemv1alpha1.<PERSON><PERSON>ob, podSuffixList []string) error {

	return nil
}

func (va *VncAction) FrontPath(nameSpace, jobName string) map[string]string {

	return nil
}
