package pluginResBuilder

import (
	"errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

func TestVncAction_Create(t *testing.T) {
	// Setup
	vncAction := &VncAction{}

	// Act
	err := vncAction.Create(&systemv1alpha1.Notebook{}, "test-service")

	// Assert
	assert.NoError(t, err, "Create should not return an error")
}

func TestVncAction_CreateTJ(t *testing.T) {
	// Setup
	vncAction := &VncAction{}

	// Act
	err := vncAction.CreateTJ(&systemv1alpha1.TrainingJob{}, []string{"pod1", "pod2"})

	// Assert
	assert.NoError(t, err, "CreateTJ should not return an error")
}

func TestVncAction_Exist(t *testing.T) {
	// Setup
	vncAction := &VncAction{}

	// Act
	exists := vncAction.Exist(&systemv1alpha1.Notebook{})

	// Assert
	assert.True(t, exists, "Exist should return true")
}

func TestVncAction_Delete(t *testing.T) {
	// Setup
	deleteTokenCalled := false
	vncAction := &VncAction{
		DeleteTokenFn: func(namespace, name string) error {
			deleteTokenCalled = true
			if namespace == "default" && name == "test-notebook" {
				return nil
			}
			return errors.New("unexpected namespace or name")
		},
	}
	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}

	// Act
	err := vncAction.Delete(job)

	// Assert
	assert.NoError(t, err, "Delete should not return an error")
	assert.True(t, deleteTokenCalled, "DeleteTokenFn should have been called")
}

func TestVncAction_DeleteTJ(t *testing.T) {
	// Setup
	vncAction := &VncAction{}

	// Act
	err := vncAction.DeleteTJ(&systemv1alpha1.TrainingJob{}, []string{"pod1", "pod2"})

	// Assert
	assert.NoError(t, err, "DeleteTJ should not return an error")
}
