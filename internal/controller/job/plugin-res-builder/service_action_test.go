package pluginResBuilder

import (
	"context"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"testing"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"github.com/stretchr/testify/assert"
)

func TestServiceAction_Create(t *testing.T) {
	// Setup
	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	serviceName := "test-service"
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects().Build()

	serviceAction := &ServiceAction{
		Client: k8sFakeClient,
		Kind:   "Notebook",
		Port:   8080,
		SelectorFunc: func(kind string, job *systemv1alpha1.Notebook) map[string]string {
			return map[string]string{"app": "test"}
		},
		ServiceBuilder: ServiceBuilder{},
	}

	// Act
	err := serviceAction.Create(job, serviceName)

	// Assert
	assert.NoError(t, err, "Create should not return an error")

	// Verify if the service is created
	var svc v1.Service
	err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{
		Namespace: job.Namespace,
		Name:      serviceName,
	}, &svc)
	assert.NoError(t, err, "Service should exist")
	assert.Equal(t, serviceName, svc.Name, "Service name should match")
	assert.Equal(t, job.Namespace, svc.Namespace, "Service namespace should match")
	assert.Equal(t, int32(8080), svc.Spec.Ports[0].Port, "Service port should match")
	assert.Equal(t, "test", svc.Spec.Selector["app"], "Service selector should match")
}

func TestServiceAction_CreateTJ(t *testing.T) {
	// Setup
	jobTJ := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-training-job",
			Namespace: "default",
		},
	}
	podSuffixList := []string{"pod1", "pod2"}
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects().Build()

	serviceAction := &ServiceAction{
		Client:         k8sFakeClient,
		Kind:           "TrainingJob",
		Port:           8081,
		SelectorFunc:   DefaultSelectFunc,
		ServiceBuilder: ServiceBuilder{},
	}

	// Act
	err := serviceAction.CreateTJ(jobTJ, podSuffixList)

	// Assert
	assert.NoError(t, err, "CreateTJ should not return an error")

	// Verify if the services are created
	for _, podSuffix := range podSuffixList {
		var svc v1.Service
		err = k8sFakeClient.Get(context.TODO(), types.NamespacedName{
			Namespace: jobTJ.Namespace,
			Name:      PodServiceName(serviceAction.Kind, jobTJ.Name, podSuffix),
		}, &svc)
		assert.NoError(t, err, "Service should exist")
		assert.Equal(t, jobTJ.Namespace, svc.Namespace, "Service namespace should match")
		assert.Equal(t, int32(8081), svc.Spec.Ports[0].Port, "Service port should match")
	}
}

func TestServiceName(t *testing.T) {
	result := ServiceName("notebook", "test-job")
	expected := "notebook-test-job"
	assert.Equal(t, expected, result, "ServiceName should return the correct service name")
}

func TestPodServiceName(t *testing.T) {
	result := PodServiceName("training", "test-job", "pod1")
	expected := "training-test-job-pod1-svc"
	assert.Equal(t, expected, result, "PodServiceName should return the correct pod service name")
}
