package pluginResBuilder

import (
	"errors"
	"testing"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	batch "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type MockFrontAction struct {
	mock.Mock
}

func (m *MockFrontAction) Create(job *systemv1alpha1.Notebook) error {
	args := m.Called(job)
	return args.Error(0)
}

func (m *MockFrontAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	args := m.Called(jobTJ, podSuffixList)
	return args.Error(0)
}

func (m *MockFrontAction) Delete(job *systemv1alpha1.Notebook) error {
	args := m.Called(job)
	return args.Error(0)
}

func (m *MockFrontAction) DeleteTJ(jobTJ *systemv1alpha1.<PERSON><PERSON><PERSON>, podSuffixList []string) error {
	args := m.Called(jobTJ, podSuffixList)
	return args.Error(0)
}

func (m *MockFrontAction) Exist(job *systemv1alpha1.Notebook) bool {
	args := m.Called(job)
	return args.Bool(0)
}

func (m *MockFrontAction) FrontPath(nameSpace, jobName string) map[string]string {
	args := m.Called(nameSpace, jobName)
	return args.Get(0).(map[string]string)
}

func (m *MockFrontAction) GetWebURL() string {
	args := m.Called()
	return args.String(0)
}

type MockAction struct {
	mock.Mock
}

func (m *MockAction) Create(job *systemv1alpha1.Notebook, serviceName string) error {
	args := m.Called(job, serviceName)
	return args.Error(0)
}

func (m *MockAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	args := m.Called(jobTJ, podSuffixList)
	return args.Error(0)
}

func (m *MockAction) Delete(job *systemv1alpha1.Notebook) error {
	args := m.Called(job)
	return args.Error(0)
}

func (m *MockAction) DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	args := m.Called(jobTJ, podSuffixList)
	return args.Error(0)
}

func TestDefaultCapability_GetURL(t *testing.T) {
	mockFrontAction := new(MockFrontAction)
	mockFrontAction.On("GetWebURL").Return("http://example.com")

	dc := &DefaultCapability{
		FrontAction: mockFrontAction,
	}

	url := dc.GetURL()
	assert.Equal(t, "http://example.com", url)
	mockFrontAction.AssertCalled(t, "GetWebURL")
}

func TestDefaultCapability_PreCreate(t *testing.T) {
	vcJob := &batch.Job{
		Spec: batch.JobSpec{
			Plugins: map[string][]string{
				"svc": []string{},
			},
		},
	}
	notebook := &systemv1alpha1.Notebook{}
	dc := &DefaultCapability{
		OffNetPolicy: true,
	}

	err := dc.PreCreate(vcJob, notebook)
	assert.NoError(t, err)
	_, exists := vcJob.Spec.Plugins["svc"]
	assert.False(t, exists)
}

func TestDefaultCapability_Create(t *testing.T) {
	mockFrontAction := new(MockFrontAction)
	mockAction := new(MockAction)

	job := &systemv1alpha1.Notebook{}
	serviceName := "test-service"

	// 模拟前端创建成功
	mockFrontAction.On("Create", job).Return(nil)

	// 模拟后端第一个操作创建成功，第二个操作失败
	mockAction.On("Create", job, serviceName).Return(nil).Once()
	mockAction.On("Create", job, serviceName).Return(errors.New("backend action failed")).Once()

	mockFrontAction.On("Delete", job).Return(nil)
	mockAction.On("Delete", job).Return(nil)

	dc := &DefaultCapability{
		FrontAction:    mockFrontAction,
		BackEndActions: []Action{mockAction, mockAction},
	}

	err := dc.Create(job, serviceName)
	assert.Error(t, err)
	assert.Equal(t, "backend action failed", err.Error())

	mockFrontAction.AssertCalled(t, "Create", job)
	mockFrontAction.AssertCalled(t, "Delete", job)
	mockAction.AssertNumberOfCalls(t, "Create", 2)
	mockAction.AssertCalled(t, "Delete", job)
}

func TestDefaultCapability_CreateTJ(t *testing.T) {
	mockFrontAction := new(MockFrontAction)
	mockAction := new(MockAction)

	jobTJ := &systemv1alpha1.TrainingJob{}
	podSuffixList := []string{"suffix1", "suffix2"}

	// 模拟前端创建成功
	mockFrontAction.On("CreateTJ", jobTJ, podSuffixList).Return(nil)

	// 模拟后端第一个操作创建成功，第二个操作失败
	mockAction.On("CreateTJ", jobTJ, podSuffixList).Return(nil).Once()
	mockAction.On("CreateTJ", jobTJ, podSuffixList).Return(errors.New("backend action failed")).Once()

	mockFrontAction.On("DeleteTJ", jobTJ, podSuffixList).Return(nil)
	mockAction.On("DeleteTJ", jobTJ, podSuffixList).Return(nil)

	dc := &DefaultCapability{
		FrontAction:    mockFrontAction,
		BackEndActions: []Action{mockAction, mockAction},
	}

	err := dc.CreateTJ(jobTJ, podSuffixList)
	assert.Error(t, err)
	assert.Equal(t, "backend action failed", err.Error())

	mockFrontAction.AssertCalled(t, "CreateTJ", jobTJ, podSuffixList)
	mockFrontAction.AssertCalled(t, "DeleteTJ", jobTJ, podSuffixList)
	mockAction.AssertNumberOfCalls(t, "CreateTJ", 2)
	mockAction.AssertCalled(t, "DeleteTJ", jobTJ, podSuffixList)
}

func TestDefaultCapability_Delete(t *testing.T) {
	mockFrontAction := new(MockFrontAction)
	mockAction := new(MockAction)

	job := &systemv1alpha1.Notebook{}

	// 模拟前端删除成功
	mockFrontAction.On("Delete", job).Return(nil)

	// 模拟后端删除成功
	mockAction.On("Delete", job).Return(nil)

	dc := &DefaultCapability{
		FrontAction:    mockFrontAction,
		BackEndActions: []Action{mockAction},
	}

	err := dc.Delete(job)
	assert.NoError(t, err)

	mockFrontAction.AssertCalled(t, "Delete", job)
	mockAction.AssertCalled(t, "Delete", job)
}

func TestDefaultCapability_GetKind(t *testing.T) {
	kind := "TestKind"
	dc := &DefaultCapability{
		Kind: kind,
	}

	retrievedKind := dc.GetKind()
	assert.Equal(t, kind, *retrievedKind)
}
