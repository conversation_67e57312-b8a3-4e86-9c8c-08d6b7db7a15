package pluginResBuilder //nolint

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/networking/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	// "http://112.31.12.175:8280" 进行base64内容加密，为通过代码安全审查
	ingress_url = "aHR0cDovLzExMi4zMS4xMi4xNzU6ODI4MA==" //nolint
)

type IngressAction struct {
	Client            client.Client
	Port              int
	Kind              string
	Regular           string
	WebURL            *string
	RewritePath       *string
	IngressPathFormat *string
	IngressBuilder
}

func (ia *IngressAction) GetWebURL() string {
	return *ia.WebURL
}

func (ia *IngressAction) Create(job *systemv1alpha1.Notebook) error {
	var ing v1.Ingress
	webURL := ia.Config.Domain + ia.ingressURI(job.Name)
	ia.WebURL = &webURL
	err := ia.Client.Get(context.TODO(), types.NamespacedName{
		Namespace: job.Namespace,
		Name:      ingressName(ia.Kind, job.Name),
	}, &ing)
	if err != nil && !apierrors.IsNotFound(err) {
		klog.Infof("ingress is exist: %s, err: %s", ToJSON(&ing), err.Error())
		return nil
	}

	ingressPara := &IngressPara{
		Name:        ingressName(ia.Kind, job.Name),
		ServiceName: ServiceName(ia.Kind, job.Name),
		Path:        ia.ingressURI(job.Name) + ia.Regular,
		JobName:     job.Name,
		Namespace:   job.Namespace,
		RewritePath: ia.RewritePath,
		job:         job,
		Port:        ia.Port,
	}

	klog.Infof("Creating ingress: %v, %v", ToJSON(ingressPara), job.Status)

	if ia.Kind == "customize" {
		ingressPara.CusPortAndPath = make(map[int]string)
		for name, port := range job.Spec.CustomizePorts {
			ingressPara.CusPortAndPath[port] = fmt.Sprintf("%s/%s/%s", ia.ingressURI(job.Name), name, ia.Regular)
		}
	}

	return ia.Client.Create(context.TODO(), ia.Build(ingressPara))
}

func (ia *IngressAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	var ing v1.Ingress
	var errRet error
	for _, podSuffix := range podSuffixList {
		err := ia.Client.Get(context.TODO(), types.NamespacedName{
			Namespace: jobTJ.Namespace,
			Name:      podIngressName(ia.Kind, jobTJ.Name, podSuffix),
		}, &ing)
		if err == nil {
			klog.Infof("ingress %s already exists for trainingjob %s/%s", ing.Name, jobTJ.Namespace, jobTJ.Name)
			continue
		}
		if !apierrors.IsNotFound(err) {
			return err
		}

		ingressParaTJ := &IngressParaTJ{}
		ingressParaTJ.Name = podIngressName(ia.Kind, jobTJ.Name, podSuffix)
		ingressParaTJ.ServiceName = PodServiceName(ia.Kind, jobTJ.Name, podSuffix)
		ingressParaTJ.Path = ia.podIngressURI(jobTJ.Name, podSuffix) + ia.Regular
		ingressParaTJ.JobName = jobTJ.Name
		ingressParaTJ.Namespace = jobTJ.Namespace
		ingressParaTJ.RewritePath = ia.RewritePath
		ingressParaTJ.jobTJ = jobTJ
		ingressParaTJ.Port = ia.Port

		klog.Infof("Creating ingress: %s, trainingjob.status: %+v", ToJSON(ingressParaTJ), jobTJ.Status)

		if err = ia.Client.Create(context.TODO(), ia.BuildTJ(ingressParaTJ)); err != nil {
			errRet = err
		}
	}

	return errRet
}

func (ia *IngressAction) Exist(job *systemv1alpha1.Notebook) bool {
	var ing *v1.Ingress
	err := ia.Client.Get(context.TODO(), types.NamespacedName{
		Namespace: job.Namespace,
		Name:      ingressName(ia.Kind, job.Name),
	}, ing)

	return err == nil && ing != nil
}

func (ia *IngressAction) Delete(job *systemv1alpha1.Notebook) error {
	var ing *v1.Ingress
	err := ia.Client.Get(context.TODO(), types.NamespacedName{
		Namespace: job.Namespace,
		Name:      ingressName(ia.Kind, job.Name),
	}, ing)
	if err != nil {
		return err
	}
	return ia.Client.Delete(context.TODO(), ing)
}

func (ia *IngressAction) DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	var errRet error
	for _, podSuffix := range podSuffixList {
		var ing *v1.Ingress
		err := ia.Client.Get(context.TODO(), types.NamespacedName{
			Namespace: jobTJ.Namespace,
			Name:      podIngressName(ia.Kind, jobTJ.Name, podSuffix),
		}, ing)
		if err != nil {
			return err
		}
		if err = ia.Client.Delete(context.TODO(), ing); err != nil {
			errRet = err
		}
	}

	return errRet
}

func (ia *IngressAction) FrontPath(nameSpace, jobName string) map[string]string {
	// 为通过代码安全审查，将 ingress_url 进行base64解密使用
	decoded, _ := base64.StdEncoding.DecodeString(ingress_url)
	path := strings.TrimRight(string(decoded), "/") + ia.ingressURI(jobName)
	return map[string]string{"path": path}
}

func (ia *IngressAction) ingressURI(jobName string) string {
	if ia.IngressPathFormat != nil {
		return fmt.Sprintf(*ia.IngressPathFormat, ia.Kind, jobName)
	}
	return fmt.Sprintf("/%s/%s", ia.Kind, jobName)
}

func (ia *IngressAction) podIngressURI(jobName string, podSuffix string) string {
	return fmt.Sprintf("/%s/%s/%s/", ia.Kind, jobName, podSuffix)
}

func ingressName(kind string, jobName string) string {
	return kind + "-" + jobName + "-ingress"
}

func podIngressName(kind string, jobName string, podSuffix string) string {
	return kind + "-" + jobName + "-" + podSuffix + "-ingress"
}
