package job

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInitServer(t *testing.T) {
	monitorAddress := ":8080"
	server := InitServer(monitorAddress)

	// 确保返回的服务器具有预期的属性
	assert.Equal(t, monitorAddress, server.Addr)
	assert.Equal(t, 60*time.Second, server.WriteTimeout)
	assert.Equal(t, 30*time.Second, server.ReadTimeout)
	assert.NotNil(t, server.Handler)
}

func TestNewRouter(t *testing.T) {
	router := newRouter()

	// 确保路由器可以处理请求
	req, err := http.NewRequest("POST", "/api/v1/plugin-status/update", nil)
	require.NoError(t, err)

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
}

//func TestPluginStatsUpdate(t *testing.T) {
//	// 设置请求的参数
//	req, err := http.NewRequest("POST", "/api/v1/plugin-status/update", nil)
//	require.NoError(t, err)
//
//	q := req.URL.Query()
//	q.Add("jobname", "test-job")
//	q.Add("plugin", "test-plugin")
//	q.Add("state", "running")
//	req.URL.RawQuery = q.Encode()
//
//	rr := httptest.NewRecorder()
//
//	// 创建路由并注册处理程序
//	router := newRouter()
//	router.ServeHTTP(rr, req)
//
//	// 验证状态码和日志
//	assert.Equal(t, http.StatusOK, rr.Code)
//
//	// 确保 statusChan 能够接收数据
//	select {
//	case info := <-statusChan:
//		assert.Equal(t, "test-job", info.jobname)
//		assert.Equal(t, "test-plugin", info.plugin)
//		assert.Equal(t, "running", info.state)
//	case <-time.After(1 * time.Second):
//		t.Fatal("expected info to be sent to statusChan")
//	}
//}

func TestPluginStatsUpdateEmptyFields(t *testing.T) {
	req, err := http.NewRequest("POST", "/api/v1/plugin-status/update", nil)
	require.NoError(t, err)

	rr := httptest.NewRecorder()
	router := newRouter()

	// 不传参数，应该能正常处理
	router.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code) // 假设没有参数时仍然返回200 OK
}
