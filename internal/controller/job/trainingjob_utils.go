//nolint:all
package job

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	"bytes"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"errors"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
)

var (
	normalTimeout    = time.Second * 120
	defaultUserAgent = "job-controllers/agent"
)

type wrappedTransport struct {
	Transport http.RoundTripper
}

type licensesResp struct {
	Code   int      `json:"code"`
	Reason string   `json:"reason"`
	Data   dataInfo `json:"data"`
}

type dataInfo struct {
	Expire string `json:"expire"`
}

func (t *wrappedTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	if req.UserAgent() == "" {
		req.Header.Set("User-Agent", defaultUserAgent)
	}

	return t.Transport.RoundTrip(req)
}

func StartCheckLicense(startChan chan struct{}, url string) {
	checkLicense(url)
	startChan <- struct{}{}
	cycleTime := time.Minute
	isExpiredChan := make(chan bool)
	t := time.NewTicker(cycleTime)
	defer t.Stop()
	go func() {
		for {
			select {
			case <-t.C:
				isExpired := checkLicense(url)
				isExpiredChan <- isExpired
			}
		}
	}()

	for {
		lastTime := cycleTime
		isExp := <-isExpiredChan
		if isExp && lastTime != time.Minute {
			cycleTime = time.Minute
			t.Reset(cycleTime)
		} else if !isExp && lastTime != time.Hour {
			cycleTime = time.Hour
			t.Reset(cycleTime)
		}
	}
}

func checkLicense(url string) bool {
	var err error
	defer func() {
		if err != nil {
			systemv1alpha1.Mutex.Lock()
			systemv1alpha1.License = true
			systemv1alpha1.Mutex.Unlock()
		}
	}()

	req, err := http.NewRequest("GET", url+"/license/check/expire", nil)
	if err != nil {
		return true
	}
	client := getTimeOutHttpClient()
	resp, err := client.Do(req)
	if err != nil {
		klog.Errorf("client.Do err: %s", err.Error())
		return true
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		klog.Errorf("resp.StatusCode err statusCode: %d", resp.StatusCode)
		err = errors.New("Error")
		return true
	}

	regBody, err := io.ReadAll(resp.Body)
	if err != nil {
		klog.Errorf("io.ReadAll err: %s", err.Error())
		return true
	}

	res := licensesResp{}
	err = json.Unmarshal(regBody, &res)
	if err != nil {
		klog.Errorf("json.Unmarshal err: %s", err.Error())
		return true
	}

	if res.Code != 0 {
		err = errors.New("Error")
		klog.Errorf("code=%d, reason=%s", res.Code, res.Reason)
		return true
	}

	decrypt := caesarCipher(res.Data.Expire, -3)
	if len(decrypt) <= 10 {
		klog.Errorf("code=%d, reason=%s", res.Code, "decrypt error.")
		err = errors.New("Error")
		return true
	}
	// 获取参数内容
	isExpire := decrypt[5 : len(decrypt)-5]
	systemv1alpha1.Mutex.Lock()
	defer systemv1alpha1.Mutex.Unlock()
	if isExpire == "true" {
		systemv1alpha1.License = true
		return true
	}

	systemv1alpha1.License = false
	return false
}

func getTimeOutHttpClient() http.Client {

	return http.Client{
		Transport: &wrappedTransport{
			Transport: &http.Transport{
				Dial: func(netw, addr string) (net.Conn, error) {
					c, err := net.DialTimeout(netw, addr, normalTimeout)
					if err != nil {
						return nil, err
					}
					return c, nil
				},
				DialContext: func(ctx context.Context, netw, addr string) (net.Conn, error) {
					var d net.Dialer
					ctx2, cancel := context.WithTimeout(ctx, normalTimeout)
					defer cancel()
					c, err := d.DialContext(ctx2, netw, addr)
					if err != nil {
						return nil, err
					}
					return c, nil
				},
				MaxIdleConnsPerHost:   20,
				ResponseHeaderTimeout: normalTimeout,
			},
		},
	}
}

func caesarCipher(s string, offset int) string {
	var cipher strings.Builder

	for i := 0; i < len(s); i++ {
		c := rune(s[i])
		if c >= 'a' && c <= 'z' {
			if offset > 0 {
				c += rune((offset + i) % 26)
			} else {
				c += rune((offset - i) % 26)
			}

			if c < 'a' {
				c += 26 // 向左超界
			}
			if c > 'z' {
				c -= 26 // 向右超界
			}
		} else if c >= 'A' && c <= 'Z' {
			if offset > 0 {
				c += rune((offset + i) % 26)
			} else {
				c += rune((offset - i) % 26)
			}

			if c < 'A' {
				c += 26
			}
			if c > 'Z' {
				c -= 26
			}
		}

		cipher.WriteRune(c)
	}

	return cipher.String()
}

func getPodRunningTime(pod *v1.Pod) metav1.Time {
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.State.Running != nil {
			return containerStatus.State.Running.StartedAt
		}
		if containerStatus.State.Terminated != nil {
			return containerStatus.State.Terminated.StartedAt
		}
	}
	for _, podCondition := range pod.Status.Conditions {
		if podCondition.Type == v1.PodReady {
			return podCondition.LastTransitionTime
		}
	}
	return metav1.Time{}
}

func getPodComplateTime(pod *v1.Pod, vcCompelted bool) (metav1.Time, v1.PodPhase) {
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.Name == "sidecar" {
			continue
		}
		//异常退出
		if containerStatus.State.Terminated != nil {
			if containerStatus.State.Terminated.ExitCode != 0 {
				return containerStatus.State.Terminated.FinishedAt, getPhase(vcCompelted)
			}
			//正常退出
			return containerStatus.State.Terminated.FinishedAt, v1.PodSucceeded
		}

	}
	return metav1.Time{}, v1.PodSucceeded
}

func getPhase(vcCompelted bool) v1.PodPhase {
	if vcCompelted {
		return v1.PodSucceeded
	}

	return v1.PodPhase("Terminated")
}

func generateMD5(input string) string {
	hash := md5.New()
	hash.Write([]byte(input))
	md5sum := hash.Sum(nil)
	return hex.EncodeToString(md5sum)
}

func base64Encode(data string) []byte {
	n := base64.StdEncoding.EncodedLen(len(data))
	dst := make([]byte, n)
	base64.StdEncoding.Encode(dst, []byte(data))
	return dst
}

const (
	CHAR_SET               = "UTF-8"
	BASE_64_FORMAT         = "UrlSafeNoPadding"
	RSA_ALGORITHM_KEY_TYPE = "PKCS8"
	RSA_ALGORITHM_SIGN     = crypto.SHA256
)

type XRsa struct {
	publicKey  *rsa.PublicKey
	privateKey *rsa.PrivateKey
}

func NewXRsa(publicKey []byte, privateKey []byte) (*XRsa, error) {
	block, _ := pem.Decode(publicKey)
	if block == nil {
		return nil, errors.New("public key error")
	}
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	pub := pubInterface.(*rsa.PublicKey)
	block, _ = pem.Decode(privateKey)
	if block == nil {
		return nil, errors.New("private key error")
	}
	priv, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	pri, ok := priv.(*rsa.PrivateKey)
	if ok {
		return &XRsa{
			publicKey:  pub,
			privateKey: pri,
		}, nil
	} else {
		return nil, errors.New("private key not supported")
	}
}

// 公钥加密
func (r *XRsa) PublicEncrypt(data string) (string, error) {
	partLen := r.publicKey.N.BitLen()/8 - 11
	chunks := split([]byte(data), partLen)
	buffer := bytes.NewBufferString("")
	for _, chunk := range chunks {
		bytes, err := rsa.EncryptPKCS1v15(rand.Reader, r.publicKey, chunk)
		if err != nil {
			return "", err
		}
		buffer.Write(bytes)
	}
	return base64.RawURLEncoding.EncodeToString(buffer.Bytes()), nil
}

// 私钥解密
func (r *XRsa) PrivateDecrypt(encrypted string) (string, error) {
	partLen := r.publicKey.N.BitLen() / 8
	raw, err := base64.StdEncoding.DecodeString(encrypted)
	chunks := split([]byte(raw), partLen)
	buffer := bytes.NewBufferString("")
	for _, chunk := range chunks {
		decrypted, err := rsa.DecryptPKCS1v15(rand.Reader, r.privateKey, chunk)
		if err != nil {
			return "", err
		}
		buffer.Write(decrypted)
	}
	return buffer.String(), err
}

func split(buf []byte, lim int) [][]byte {
	var chunk []byte
	chunks := make([][]byte, 0, len(buf)/lim+1)
	for len(buf) >= lim {
		chunk, buf = buf[:lim], buf[lim:]
		chunks = append(chunks, chunk)
	}
	if len(buf) > 0 {
		chunks = append(chunks, buf[:])
	}
	return chunks
}
