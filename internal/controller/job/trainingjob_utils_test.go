package job

import (
	"encoding/base64"
	"github.com/stretchr/testify/assert"
	"k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"testing"
)

func TestGenerateMD5(t *testing.T) {
	input := "test"
	expected := "098f6bcd4621d373cade4e832627b4f6" // MD5 of empty string
	result := generateMD5(input)
	assert.Equal(t, expected, result)
}

func TestBase64Encode(t *testing.T) {
	data := "test"
	expected := base64.StdEncoding.EncodeToString([]byte(data))
	result := string(base64Encode(data))
	assert.Equal(t, expected, result)
}

func TestCaesarCipher(t *testing.T) {
	tests := []struct {
		input  string
		offset int
		output string
	}{
		{"abc", 3, "dfh"},
		{"ABC", 3, "DFH"},
		{"xyz", 3, "ace"},
		{"XYZ", 3, "ACE"},
	}

	for _, test := range tests {
		result := caesarCipher(test.input, test.offset)
		assert.Equal(t, test.output, result)
	}
}

func TestGetPodRunningTime(t *testing.T) {
	pod := &v1.Pod{
		Status: v1.PodStatus{
			ContainerStatuses: []v1.ContainerStatus{
				{
					State: v1.ContainerState{
						Running: &v1.ContainerStateRunning{
							StartedAt: metav1.Now(),
						},
					},
				},
			},
		},
	}
	runningTime := getPodRunningTime(pod)
	assert.True(t, runningTime.IsZero() == false) // Ensure running time is set
}

func TestGetPodCompleteTime(t *testing.T) {
	pod := &v1.Pod{
		Status: v1.PodStatus{
			ContainerStatuses: []v1.ContainerStatus{
				{
					Name: "test-container",
					State: v1.ContainerState{
						Terminated: &v1.ContainerStateTerminated{
							FinishedAt: metav1.Now(),
							ExitCode:   0,
						},
					},
				},
			},
		},
	}
	completedTime, phase := getPodComplateTime(pod, true)
	assert.True(t, completedTime.IsZero() == false) // Ensure complete time is set
	assert.Equal(t, v1.PodSucceeded, phase)
}

func TestGetPhase(t *testing.T) {
	tests := []struct {
		vcCompleted bool
		expected    v1.PodPhase
	}{
		{true, v1.PodSucceeded},
		{false, v1.PodPhase("Terminated")},
	}

	for _, test := range tests {
		result := getPhase(test.vcCompleted)
		assert.Equal(t, test.expected, result)
	}
}

func TestSplit(t *testing.T) {
	data := []byte("Hello, World!")
	limit := 5
	expected := [][]byte{
		[]byte("Hello"),
		[]byte(", Wor"),
		[]byte("ld!"),
	}
	result := split(data, limit)
	assert.Equal(t, expected, result)
}
