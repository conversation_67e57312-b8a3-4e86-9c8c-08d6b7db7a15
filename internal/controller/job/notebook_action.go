//nolint:all
package job

import (
	"context"
	"fmt"
	"strings"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	event2 "hero.ai/hero-controllers/internal/controller/event"
	"hero.ai/hero-controllers/internal/controller/job/plugins"
	"hero.ai/hero-controllers/internal/controller/job/plugins/customize"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	vnc "hero.ai/hero-controllers/internal/controller/job/plugins/vnc"
	"hero.ai/hero-controllers/internal/controller/job/state"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	commandvc "volcano.sh/apis/pkg/apis/bus/v1alpha1"
	"volcano.sh/apis/pkg/apis/helpers"
	vjschedpg "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

func (r *NotebookReconciler) syncJobByVcjob(ctx context.Context, notebookjob *systemv1alpha1.Notebook, updateJobStatus state.UpdateNotebookJobStatusFn) error {
	oldState := notebookjob.Status.State
	vj, err := r.getVcjobByNotebook(ctx, notebookjob)
	if err != nil {
		if apierrors.IsNotFound(err) && notebookjob.Status.State == systemv1alpha1.NotebookStateStopping {
			r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventStopped, systemv1alpha1.EventStoppedMessage)
			notebookjob.Status.State = systemv1alpha1.NotebookStateStopped
			notebookjob.Status.StoppedTime = metav1.Now().String()
			r.updatePodConds(notebookjob)
			lastTransitionTime := metav1.Now()
			notebookCondition := r.newCondition(notebookjob.Status.State, &lastTransitionTime)
			notebookjob.Status.Conditions = append(notebookjob.Status.Conditions, notebookCondition)
			return r.UpdateNotebookjobStatus(ctx, notebookjob)
		}
		return err
	}

	if updateJobStatus(&notebookjob.Status) {
		pg, err := r.getPodGroupByNotebook(ctx, notebookjob)
		if err != nil {
			return err
		}

		for _, condition := range pg.Status.Conditions {
			if condition.Type == vjschedpg.PodGroupUnschedulableType {
				r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeWarning, systemv1alpha1.EventScheduledFailed, condition.Message)
			} else if condition.Type == vjschedpg.PodGroupScheduled {
				r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventScheduledSuccess, systemv1alpha1.EventScheduledSuccessMessage)
				if err := updateJobAnnos(ctx, vj, notebookjob, r.Client, func() error {
					return r.UpdateNotebookjob(ctx, notebookjob)
				}); err != nil {
					return err
				}
			}
		}

		pods, err := getPodListByVcjob(ctx, vj, r.Client)
		if err != nil {
			return err
		}

		for _, pod := range pods.Items {
			for _, containerStatus := range pod.Status.ContainerStatuses {
				if containerStatus.Name == "sidecar" {
					continue
				}
				if containerStatus.State.Waiting != nil {
					if strings.Contains(containerStatus.State.Waiting.Reason, "ImagePullBackOff") || strings.Contains(containerStatus.State.Waiting.Reason, "ErrImagePull") {
						r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeWarning, systemv1alpha1.EventImagePullFailed, containerStatus.State.Waiting.Message)
						// 更新cr状态为失败，释放pod资源
						defer func() {
							r.Delete(ctx, vj)
						}()
						notebookjob.Status.State = systemv1alpha1.NotebookStateStartFailed
					}
				} else if containerStatus.State.Running != nil {
					r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventImagePullSuccess, systemv1alpha1.EventImagePullSuccessMessage)
				}
			}
		}
	}

	_, ok := notebookjob.Annotations[stopAnnotation]
	if ok && notebookjob.Status.State != systemv1alpha1.NotebookStateStopping && notebookjob.Annotations[stopAnnotation] == stopAction {
		err = r.createVjCommandBusIfNotExist(notebookjob, vj)
		if err != nil {
			return err
		}

		notebookjob.Status.State = systemv1alpha1.NotebookStateStopping
		lastTransitionTime := metav1.Now()
		notebookCondition := r.newCondition(notebookjob.Status.State, &lastTransitionTime)
		notebookjob.Status.Conditions = append(notebookjob.Status.Conditions, notebookCondition)
		r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventStopping, systemv1alpha1.EventStoppingMessage)
		return r.UpdateNotebookjobStatus(ctx, notebookjob)
	}

	if vj.Status.State.Phase == batchvc.Running && notebookjob.Status.State != systemv1alpha1.NotebookStateStopping {
		r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventRunningSuccess, systemv1alpha1.EventRunningSuccessMessage)
		notebookjob.Status.StartTime = vj.Status.State.LastTransitionTime.String()
		notebookjob.Status.State = systemv1alpha1.NotebookState(vj.Status.State.Phase)
		// 最大运行时长处理
		if notebookjob.Spec.MaxRunTime != 0 {
			r.setMaxRunTime(notebookjob, vj.Status.State.LastTransitionTime)
		}
	} else if vj.Status.State.Phase == batchvc.Aborted || vj.Status.State.Phase == batchvc.Completed {
		r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventStopped, systemv1alpha1.EventStoppedMessage)
		notebookjob.Status.State = systemv1alpha1.NotebookStateStopped
		notebookjob.Status.StoppedTime = metav1.Now().String()
	} else if vj.Status.State.Phase == batchvc.Failed {
		r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeWarning, systemv1alpha1.EventRunFailed, fmt.Sprintf(systemv1alpha1.EventRunFailedMessage, vj.Status.State.Reason))
		notebookjob.Status.State = systemv1alpha1.NotebookState(vj.Status.State.Phase)
	} else if vj.Status.State.Phase == batchvc.Terminating || (vj.Status.State.Phase == batchvc.Terminated && notebookjob.Status.State != systemv1alpha1.NotebookStateStopping) {
		r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventStopping, systemv1alpha1.EventStoppingMessage)
		notebookjob.Status.State = systemv1alpha1.NotebookStateStopping
	} else if vj.Status.State.Phase == batchvc.Terminated && notebookjob.Status.State == systemv1alpha1.NotebookStateStopping {
		r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventStopped, systemv1alpha1.EventStoppedMessage)
		notebookjob.Status.State = systemv1alpha1.NotebookStateStopped
	}

	if notebookjob.Status.State == oldState {
		return nil
	}

	lastTransitionTime := metav1.Now()
	notebookCondition := r.newCondition(notebookjob.Status.State, &lastTransitionTime)
	notebookjob.Status.Conditions = append(notebookjob.Status.Conditions, notebookCondition)
	err = r.UpdateNotebookjobStatus(ctx, notebookjob)
	if err != nil {
		klog.Errorf("update trainingjob %s err: %s", notebookjob.Name, err.Error())
		return err
	}

	return nil
}

func (r *NotebookReconciler) createNotebookJob(ctx context.Context, notebookjob *systemv1alpha1.Notebook, updateJobStatus state.UpdateNotebookJobStatusFn) error {
	_, ok := notebookjob.Annotations[stopAnnotation]
	if ok && notebookjob.Annotations[stopAnnotation] == stopAction {
		notebookjob.Status.State = systemv1alpha1.NotebookStateStopping
		r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventStopping, systemv1alpha1.EventStoppingMessage)
		return r.UpdateNotebookjobStatus(ctx, notebookjob)
	}

	notebookJob, isNoNeedToCreateVc, err := r.initNotebookjobStatus(ctx, notebookjob)
	if err != nil {
		return err
	}

	if isNoNeedToCreateVc {
		return nil
	}

	var secretName = secretName
	if len(notebookJob.Spec.ImageSecret.Username) != 0 {
		secretName, err = CreateRegistrySecret(notebookJob.Namespace, notebookJob.Spec.ImageSecret.Username, notebookJob.Spec.ImageSecret.Password,
			notebookJob.Spec.ImageUrl, r.Client, notebookJob, r.Scheme)
		if err != nil {
			return err
		}
	}

	r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventStarting, systemv1alpha1.EventStartingMessage)
	vcjob := vcjobbuilder.NewVcJobBuilder().BuildByNotebook(notebookJob, r.Scheme, secretName, r.LocalURL)

	// 插件加载(训练框架拼接vcjob plugin参数)
	vcjobbuilder.InitConfig()
	vcjobbuilder.InitRegisterConfig()
	err = r.pluginOnVolcanojobCreate(notebookJob, vcjob)
	if err != nil {
		return err
	}

	if err = r.customizeServiceAndIngress(notebookJob); err != nil {
		return err
	}

	if err = r.preResourceApply(notebookJob, vcjob); err != nil {
		return err
	}

	// 更新vcjobcr
	vcjobbuilder.BuildCmd(notebookJob, vcjob)
	klog.Infof("vc-job FINAL config is: %s", vcjobbuilder.ToJSON(vcjob))
	err = r.UpdateNotebookjob(ctx, notebookJob)
	if err != nil {
		return err
	}
	err = r.Create(ctx, vcjob)
	if err != nil {
		r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventCreateVolcanoFailed, fmt.Sprintf(systemv1alpha1.EventCreateVolcanoFailedMessage, err.Error()))
		klog.Errorf("notebookJob %s create vcjob %v err: %s", notebookJob.Name, *vcjob, err.Error())
		return err
	}

	klog.Infof("notebookjob is %s", vcjobbuilder.ToJSON(notebookjob))

	r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventCreateVolcanoJobSuccess, systemv1alpha1.EventCreateVolcanoJobSuccessMessage)
	// 更新notebookjob状态
	if updateJobStatus != nil {
		if updateJobStatus(&notebookJob.Status) {
			lastTransitionTime := metav1.Now()
			notebookCondition := r.newCondition(notebookjob.Status.State, &lastTransitionTime)
			notebookjob.Status.Conditions = append(notebookjob.Status.Conditions, notebookCondition)
			err := r.UpdateNotebookjobStatus(ctx, notebookJob)
			if err != nil {
				klog.Errorf("update noteookJob %s err: %s", notebookJob.Name, err.Error())
				return err
			}
		}
	}

	r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventWaitingScheduler, systemv1alpha1.EventWaitingSchedulerMessage)
	return nil
}

func (r *NotebookReconciler) initNotebookjobStatus(ctx context.Context, newNotebookjob *systemv1alpha1.Notebook) (*systemv1alpha1.Notebook, bool, error) {
	var isNoNeedToCreateVc bool
	if newNotebookjob.Status.State == systemv1alpha1.NotebookStatePending {
		var volcanoJob = batchvc.Job{}
		err := r.Get(ctx, types.NamespacedName{
			Namespace: newNotebookjob.Namespace,
			Name:      vcjobbuilder.VcjobNameNotebookPrefix + newNotebookjob.Name,
		}, &volcanoJob)
		if !apierrors.IsNotFound(err) {
			isNoNeedToCreateVc = true
		}

		return newNotebookjob, isNoNeedToCreateVc, nil

	}

	if newNotebookjob.Status.State != "" {
		isNoNeedToCreateVc = true
		return newNotebookjob, isNoNeedToCreateVc, nil
	}

	newNotebookjob.Status.State = systemv1alpha1.NotebookStatePending
	newNotebookjob.Status.CreateTime = metav1.Now().String()
	lastTransitionTime := metav1.Now()
	notebookCondition := r.newCondition(newNotebookjob.Status.State, &lastTransitionTime)
	newNotebookjob.Status.Conditions = append(newNotebookjob.Status.Conditions, notebookCondition)
	err := r.UpdateNotebookjobStatus(ctx, newNotebookjob)
	if err != nil {
		return nil, isNoNeedToCreateVc, err
	}

	return newNotebookjob, isNoNeedToCreateVc, nil
}

func (r *NotebookReconciler) UpdateNotebookjobStatus(ctx context.Context, notebookjob *systemv1alpha1.Notebook) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		notebook := &systemv1alpha1.Notebook{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: notebookjob.Name, Namespace: notebookjob.Namespace}, notebook); err != nil {
			return
		}

		klog.Infof("notebook %s src status %s dst status %s", notebookjob.Name, notebook.Status.State, notebookjob.Status.State)
		notebook.Status = notebookjob.Status
		return r.Status().Update(ctx, notebook)
	})
}

func (r *NotebookReconciler) UpdateNotebookjobPodStatus(ctx context.Context, notebookjob *systemv1alpha1.Notebook) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		notebook := &systemv1alpha1.Notebook{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: notebookjob.Name, Namespace: notebookjob.Namespace}, notebook); err != nil {
			return
		}

		// 只改podstatus
		notebook.Status.PodDurations = notebookjob.Status.PodDurations
		return r.Status().Update(ctx, notebook)
	})
}

func (r *NotebookReconciler) UpdateNotebookjob(ctx context.Context, notebookjob *systemv1alpha1.Notebook) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		rp := &systemv1alpha1.Notebook{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: notebookjob.Name, Namespace: notebookjob.Namespace}, rp); err != nil {
			return
		}

		rp.Finalizers = notebookjob.ObjectMeta.Finalizers
		annotations := rp.GetAnnotations()
		if annotations == nil {
			annotations = make(map[string]string)
		}
		annotations[nodeAnnotation] = notebookjob.Annotations[nodeAnnotation]
		rp.SetAnnotations(annotations)

		return r.Update(ctx, rp)
	})
}

func (r *NotebookReconciler) customizeServiceAndIngress(notebookjob *systemv1alpha1.Notebook) error {
	if notebookjob.Spec.CustomizePorts == nil || len(notebookjob.Spec.CustomizePorts) == 0 {
		return nil
	}
	client := pluginsinterface.PluginClientset{
		KubeClients:   r.Client,
		IsMultiApp:    false,
		IngressConfig: r.IngressConfig,
		VncBaseURL:    r.VncBaseURL,
		TokenPath:     r.TokenPath,
	}
	pb, found := plugins.GetPluginBuilder(customize.PluginName)
	if !found {
		err := fmt.Errorf("failed to get plugin %s", customize.PluginName)
		klog.Error(err)
		return err
	}
	klog.Infof("Starting to execute plugin at <customizeServiceAndIngress>: %s on plugin name: %s", notebookjob.Name, customize.PluginName)
	if err := pb(&client).CreateVJNotebookPlugins(notebookjob, nil); err != nil {
		klog.Errorf("Failed to process on notebookjob create customize plugin %s, err %v.", notebookjob.Name, err)
		return err
	}
	return nil
}

func (r *NotebookReconciler) pluginOnVolcanojobCreate(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	client := pluginsinterface.PluginClientset{
		KubeClients:   r.Client,
		IsMultiApp:    false,
		IngressConfig: r.IngressConfig,
		VncBaseURL:    r.VncBaseURL,
		TokenPath:     r.TokenPath,
	}

	for _, name := range notebookjob.Spec.Plugins {
		pb, found := plugins.GetPluginBuilder(name)
		if !found {
			err := fmt.Errorf("failed to get plugin %s", name)
			klog.Error(err)
			return err
		}
		// pytorch,tensorflow只拼接volcanojob plugins参数
		klog.Infof("Starting to execute plugin at <pluginOnVolcanojobCreate>: %s on job: <%s/%s>", name, vcjob.Namespace, vcjob.Name)
		if err := pb(&client).CreateVJNotebookPlugins(notebookjob, vcjob); err != nil {
			klog.Errorf("Failed to process on volcanojob create plugin %s, err %v.", name, err)
			return err
		}
	}

	if client.IsMultiApp {
		// create supervisor环境
		vcjobbuilder.BuildVcjobExtendCap(vcjob, notebookjob.Spec.Command, r.InitSupervisorImage)
	}

	return nil
}

func (r *NotebookReconciler) getVcjobByNotebook(ctx context.Context, notebookjob *systemv1alpha1.Notebook) (*batchvc.Job, error) {
	vj := &batchvc.Job{}
	err := r.Client.Get(ctx, types.NamespacedName{
		Namespace: notebookjob.Namespace,
		Name:      vcjobbuilder.VcjobNameNotebookPrefix + notebookjob.Name,
	}, vj)

	if err != nil {
		if apierrors.IsNotFound(err) {
			err = r.Client.Get(ctx, types.NamespacedName{
				Namespace: notebookjob.Namespace,
				Name:      "notebook-" + notebookjob.Name,
			}, vj)
			if err != nil {
				return vj, err
			}
		}
		return vj, err
	}

	return vj, nil
}

func (r *NotebookReconciler) UpdatePod(ctx context.Context, pods *v1.Pod, notebook *systemv1alpha1.Notebook) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		var newPod = &v1.Pod{}
		if err := r.Get(ctx, client.ObjectKeyFromObject(pods), newPod); err != nil {
			return err
		}

		patch := client.MergeFrom(newPod.DeepCopy())

		if !pods.DeletionTimestamp.IsZero() {
			if controllerutil.ContainsFinalizer(pods, Finalizer) {
				controllerutil.RemoveFinalizer(pods, Finalizer)
			}
		} else {
			if !controllerutil.ContainsFinalizer(pods, Finalizer) {
				controllerutil.AddFinalizer(pods, Finalizer)
			}
		}

		// 提交更新的 patch
		return r.Patch(ctx, pods, patch)
	})
}

func (r *NotebookReconciler) syncNotebookjobPodConditions(ctx context.Context, notebookjob *systemv1alpha1.Notebook) {
	if notebookjob.Status.State == systemv1alpha1.NotebookStatePending || notebookjob.Status.State == "" {
		return
	}

	if notebookjob.Status.PodDurations == nil {
		notebookjob.Status.PodDurations = make(systemv1alpha1.TrainingJobDurations)
	}

	var podList v1.PodList
	err := r.Client.List(ctx, &podList, &client.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set{
			systemv1alpha1.NotebookNameLabels:      notebookjob.Name,
			systemv1alpha1.NotebookNamespaceLabels: notebookjob.Namespace,
		}),
	})
	if err != nil {
		klog.Errorf("get podlist by notebookjob %s err: %s", notebookjob.Name, err.Error())
		return
	}
	if len(podList.Items) != 0 {
		// 判断是否需要删除vj
		vj, err := r.getVcjobByNotebook(ctx, notebookjob)
		if err != nil {
			klog.Errorf("get vcjob by notebook %s err: %s", notebookjob.Name, err.Error())
		}
		err = DeleteVolcanoJob(ctx, r.Client, vcjobbuilder.VcjobNameNotebookPrefix, notebookjob, vj)
		if err != nil {
			klog.Errorf("delete vcjob by trainingjob %s err: %s", notebookjob.Name, err.Error())
		}
	} else {
		if !notebookjob.GetDeletionTimestamp().IsZero() {
			if err := RemoveFinalizer(ctx, r.Client, notebookjob); err != nil {
				klog.Errorf("update notebookjob %s err: %s", notebookjob.GetName(), err.Error())
				return
			}
		}
	}
	for index := range podList.Items {
		pods := podList.Items[index]
		var launchedTime metav1.Time
		var startTime string
		var completedTime string
		switch pods.Status.Phase {
		case v1.PodRunning:
			launchedTime = getPodRunningTime(&pods)
		case v1.PodSucceeded, v1.PodFailed:
			if launchedTime.IsZero() {
				launchedTime = getPodRunningTime(&pods)
			}
		}
		if !launchedTime.IsZero() {
			startTime = launchedTime.String()
		}

		if err := r.UpdatePod(ctx, &pods, notebookjob); err != nil {
			klog.Errorf("update pod failed: %s", err)
		}

		compTime, phase := getPodComplateTime(&pods, false)
		if !compTime.IsZero() {
			completedTime = compTime.String()
			pods.Status.Phase = phase
		}

		if compTime.IsZero() && pods.DeletionTimestamp != nil {
			completedTime = pods.DeletionTimestamp.Rfc3339Copy().String()
			pods.Status.Phase = v1.PodPhase("Terminated") // v1.PodSucceeded
			// 删除pod Finalizer
		}

		if _, ok := notebookjob.Status.PodDurations[string(pods.UID)]; !ok {
			notebookjob.Status.PodDurations[string(pods.UID)] = &systemv1alpha1.TrainingJobPodDurations{
				Name:          pods.Name,
				TaskName:      notebookjob.Name,
				LaunchedTime:  startTime,
				CompletedTime: completedTime,
				Phase:         pods.Status.Phase,
				NodeName:      pods.Spec.NodeName,
			}
		} else {
			if len(notebookjob.Status.PodDurations[string(pods.UID)].CompletedTime) > 0 {
				completedTime = notebookjob.Status.PodDurations[string(pods.UID)].CompletedTime
			}
			if len(notebookjob.Status.PodDurations[string(pods.UID)].LaunchedTime) > 0 {
				startTime = notebookjob.Status.PodDurations[string(pods.UID)].LaunchedTime
			}
			if notebookjob.Status.PodDurations[string(pods.UID)].Phase == v1.PodSucceeded || notebookjob.Status.PodDurations[string(pods.UID)].Phase == v1.PodPhase("Terminated") {
				pods.Status.Phase = notebookjob.Status.PodDurations[string(pods.UID)].Phase
			}

			notebookjob.Status.PodDurations[string(pods.UID)] = &systemv1alpha1.TrainingJobPodDurations{
				Name:          pods.Name,
				TaskName:      notebookjob.Name,
				LaunchedTime:  startTime,
				CompletedTime: completedTime,
				Phase:         pods.Status.Phase,
				NodeName:      pods.Spec.NodeName,
			}
		}
	}

	err = r.UpdateNotebookjobPodStatus(ctx, notebookjob)
	if err != nil {
		klog.Errorf("update notebookjob %s err: %s", notebookjob.Name, err.Error())
		return
	}

}

func (r *NotebookReconciler) getPodGroupByNotebook(ctx context.Context, notebookjob *systemv1alpha1.Notebook) (*vjschedpg.PodGroup, error) {
	pgs := vjschedpg.PodGroupList{}
	pg := vjschedpg.PodGroup{}
	err := r.Client.List(ctx, &pgs, &client.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set{
			systemv1alpha1.VcjobNameLabels: notebookjob.Name,
			systemv1alpha1.VcjobNsLabels:   notebookjob.Namespace,
		}),
	})

	if err != nil || len(pgs.Items) == 0 {
		return &pg, err
	}

	pg = pgs.Items[0]

	return &pg, nil
}

func (r *NotebookReconciler) setMaxRunTime(notebookjob *systemv1alpha1.Notebook, runTime metav1.Time) {
	cycleTime := -time.Since(runTime.Time) + time.Minute*time.Duration(notebookjob.Spec.MaxRunTime)

	if cycleTime < 0 {
		klog.Infof("runTime %s lower since now %s", time.Duration(notebookjob.Spec.MaxRunTime), -time.Since(runTime.Time)*time.Minute)
		// 已经超时的作业立即停止
		cycleTime = time.Second
	}

	RegisterTimerTaskHandle(notebookjob.Name, TimerTaskFunc(func() {
		r.EventRecord.EventRecord(context.TODO(), notebookjob, v1.EventTypeNormal, systemv1alpha1.EventMaxRunTimeSetting, systemv1alpha1.EventMaxRunTimeSettingMessage)
		var notebook = &systemv1alpha1.Notebook{}
		if err := r.Get(context.TODO(), types.NamespacedName{
			Namespace: notebookjob.Namespace,
			Name:      notebookjob.Name,
		}, notebook); err != nil {
			klog.Warningf("notebook %s has been deleted, err: %s", notebookjob.Name, err.Error())
			return
		}

		if notebook.Annotations == nil {
			notebook.Annotations = make(map[string]string)
		}

		notebook.Annotations[stopAnnotation] = stopAction
		if err := r.Update(context.TODO(), notebook); err != nil {
			klog.Errorf("update notebook failed: %s", err.Error())
			return
		}

	}), cycleTime)

	klog.Infof("notebook %s set max time success ", notebookjob.Name)
}

func (r *NotebookReconciler) createVjCommandBusIfNotExist(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	var bus commandvc.Command
	err := r.Get(context.TODO(), types.NamespacedName{
		Namespace: notebookjob.Namespace,
		Name:      notebookjob.Name,
	}, &bus)
	if !apierrors.IsNotFound(err) {
		return nil
	}

	bus = commandvc.Command{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Command",
			APIVersion: "bus.volcano.sh/v1alpha1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      notebookjob.Name,
			Namespace: notebookjob.Namespace,
		},
		Action:       string(commandvc.AbortJobAction),
		TargetObject: metav1.NewControllerRef(vcjob, helpers.JobKind),
	}

	if err := controllerutil.SetControllerReference(notebookjob, &bus, r.Scheme); err != nil {
		return err
	}

	return r.Create(context.TODO(), &bus)
}

func (r *NotebookReconciler) updatePodConds(notebookjob *systemv1alpha1.Notebook) {
	for _, v := range notebookjob.Status.PodDurations {
		v.Phase = v1.PodSucceeded
	}
}

func (r *NotebookReconciler) newCondition(status systemv1alpha1.NotebookState, lastTransitionTime *metav1.Time) systemv1alpha1.NotebookCondition {
	return systemv1alpha1.NotebookCondition{
		Status:             status,
		LastTransitionTime: lastTransitionTime,
	}
}

func (r *NotebookReconciler) deleteNotebook(ctx context.Context, notebookjob *systemv1alpha1.Notebook) error {
	if !notebookjob.DeletionTimestamp.IsZero() {

		if vnc.ContainsString(notebookjob.ObjectMeta.Finalizers, vnc.VncFinalizerName) {

			if err := vnc.DleleToken(notebookjob.Namespace, notebookjob.Name, r.TokenPath); err != nil {
				return err
			}
			notebookjob.ObjectMeta.Finalizers = vnc.RemoveString(notebookjob.ObjectMeta.Finalizers, vnc.VncFinalizerName)

			return r.UpdateNotebookjob(ctx, notebookjob)
		}
	}
	return nil
}

func (r *NotebookReconciler) preResourceApply(notebookjob *systemv1alpha1.Notebook, vj *batchvc.Job) error {
	// 使用华为昇腾 NPU 时，需要先创建 hccl configmap 挂载到 pod 中
	if npu, ok := vj.Labels[vcjobbuilder.AscendRingControllerAtlasLabelKey]; ok {
		hcclCm := vcjobbuilder.BuildAscendNPUHCCLConfigMap(vj.Name, vj.Namespace, npu)
		if err := controllerutil.SetControllerReference(notebookjob, hcclCm, r.Scheme); err != nil {
			return err
		}
		if err := r.Create(context.TODO(), hcclCm); err != nil && !apierrors.IsNotFound(err) {
			return err
		}
	}

	return nil
}

func StartUpdateURLStatus(cli client.Client, scheme *runtime.Scheme, namespace string) {
	event := event2.NewEventRecord(cli, scheme)
	for {
		select {
		case jobInfo := <-statusChan:
			updateURLStatus(cli, jobInfo.jobname, jobInfo.plugin, jobInfo.state, namespace, event)
		}
	}
}

func updateURLStatus(cli client.Client, name, plugin, state, namespace string, event *event2.EventRecord) error {

	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		notebookjob := &systemv1alpha1.Notebook{}
		if err = cli.Get(context.TODO(), client.ObjectKey{Name: name, Namespace: namespace}, notebookjob); err != nil {
			return
		}
		klog.Infof("update plugin status, plugin: %s, state: %s", plugin, state)
		var pluginName string
		switch plugin {
		case "jupyter":
			pluginName = "jupyter"
			notebookjob.Status.Jupyter.State = getState(state)
		case "vnc":
			pluginName = "vnc"
			notebookjob.Status.Vnc.State = getState(state)
		case "gotty":
			pluginName = "sh"
			notebookjob.Status.WebTerminal.State = getState(state)
		case "vscode":
			pluginName = "vscode"
			notebookjob.Status.Vscode.State = getState(state)
		case "ssh":
			pluginName = "ssh"
			notebookjob.Status.SSH.State = getState(state)
		default:
			return nil
		}

		if !getState(state) {
			// 支持多个reason
			event.SetReason(pluginName)
			if err := event.EventRecord(context.TODO(), notebookjob, v1.EventTypeWarning, systemv1alpha1.EventEnvCheck, fmt.Sprintf(systemv1alpha1.EventEnvCheckMessage, pluginName)); err != nil {
				return err
			}
		}
		return cli.Status().Update(context.TODO(), notebookjob)
	})
}

func getState(state string) bool {
	return state == "open"
}
