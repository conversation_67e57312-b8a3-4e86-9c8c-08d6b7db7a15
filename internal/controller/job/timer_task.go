package job

import (
	"sync"
	"time"

	"k8s.io/klog"
)

type TimerTaskHandler interface {
	ServeTimer()
}

type TimerTaskFunc func()

func (t TimerTaskFunc) ServeTimer() {
	t()
}

type timerTaskHandle struct {
	handler      TimerTaskHandler
	intervalTime time.Duration
	timer        *time.Ticker
}

var (
	timerHandlePoolMu sync.Mutex
	timerHandlePool   = make(map[string]*timerTaskHandle)
)

func RegisterTimerTaskHandle(id string, handler TimerTaskHandler, intervalTime time.Duration) {
	timerHandlePoolMu.Lock()
	defer timerHandlePoolMu.Unlock()

	if handle, exists := timerHandlePool[id]; exists {
		stopExistingTasks(id)
		stopTaskTimer(handle.timer)
	}

	newHandle := &timerTaskHandle{
		handler:      handler,
		intervalTime: intervalTime,
		timer:        time.NewTicker(intervalTime),
	}
	timerHandlePool[id] = newHandle

	go runTimerTask(id, newHandle)
}

var (
	timerTaskPoolMu sync.Mutex
	timerTaskPool   = make(map[string]*TimerTask)
)

type TimerTask struct {
	Id           string //nolint
	Handler      TimerTaskHandler
	intervalTime time.Duration
	stopChan     chan struct{}
	isFinished   chan struct{}
}

func newTimerTask(tType string, handle *timerTaskHandle) *TimerTask {
	return &TimerTask{
		Id:           tType,
		Handler:      handle.handler,
		intervalTime: handle.intervalTime,
		stopChan:     make(chan struct{}),
		isFinished:   make(chan struct{}),
	}
}

func stopExistingTasks(id string) {
	timerTaskPoolMu.Lock()
	defer timerTaskPoolMu.Unlock()

	for _, task := range timerTaskPool {
		if task.Id == id {
			task.stopChan <- struct{}{}
		}
	}
}

func (t *TimerTask) Run() {
	go func() {
		t.Handler.ServeTimer()
		t.isFinished <- struct{}{}
	}()

	select {
	case <-t.isFinished:
		klog.Infof("Task %s has finished", t.Id)
	case <-t.stopChan:
		klog.Infof("Task %s has been stopped", t.Id)
	}

	timerTaskPoolMu.Lock()
	delete(timerTaskPool, t.Id)
	timerTaskPoolMu.Unlock()
}

func stopTaskTimer(t *time.Ticker) {
	t.Stop()
}

func runTimerTask(id string, handle *timerTaskHandle) {
	for range handle.timer.C {
		task := newTimerTask(id, handle)

		timerTaskPoolMu.Lock()
		timerTaskPool[task.Id] = task
		timerTaskPoolMu.Unlock()

		go task.Run()

		break
	}
}
