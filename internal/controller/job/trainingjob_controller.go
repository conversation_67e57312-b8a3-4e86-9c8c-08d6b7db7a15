/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package job

import (
	"context"
	"encoding/json"
	"runtime/debug"

	"hero.ai/hero-controllers/internal/controller/actions/logexport/common"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/lokiclient"
	"hero.ai/hero-controllers/internal/controller/config"
	"hero.ai/hero-controllers/internal/controller/event"
	"hero.ai/hero-controllers/internal/controller/job/faulttolerance"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog"
	ctrl "sigs.k8s.io/controller-runtime"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/job/state"
	v1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/source"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	vjschedpg "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

// TrainingJobReconciler reconciles a TrainingJob object
type TrainingJobReconciler struct {
	client.Client
	Scheme         *runtime.Scheme
	EventRecord    *event.EventRecord
	Namespace      string
	IngressConfig  config.IngressConfig
	Context        context.Context
	restartingJobs map[types.NamespacedName]*restartingJob
	healthCheckers []faulttolerance.HealthChecker
}

func (r *TrainingJobReconciler) Initialize() {
	state.CreateJob = r.createVcJob
	state.SyncJobByVcJob = r.syncJobByVcjob
	state.RestartJob = r.restartJob
	r.initFaultTolerance()
}

//+kubebuilder:rbac:groups=system.hero.ai,resources=trainingjobs,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=trainingjobs/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=trainingjobs/finalizers,verbs=update
//+kubebuilder:rbac:groups=scheduling.volcano.sh,resources=podgroups,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=batch.volcano.sh,resources=jobs,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=events,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=configmaps,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=bus.volcano.sh,resources=commands,verbs=get;list;watch;create;update;patch;delete

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the TrainingJob object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.14.4/pkg/reconcile
func (r *TrainingJobReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)

	// TODO(user): your logic here
	var trainjob = systemv1alpha1.TrainingJob{}
	err := r.Get(ctx, req.NamespacedName, &trainjob)
	if err != nil {
		// 获取trainingjob下面的所有pod情况，同步到status
		// r.syncTrainjobPodConditions(ctx, nil)
		klog.Errorf("TrainingJob <%s> is terminating, skip management process.", req.NamespacedName)
		return ctrl.Result{}, nil
	}

	if err := AddFinalizer(ctx, r.Client, &trainjob); err != nil {
		klog.Errorf("add Finalizer  of Job <%s> failed: %s", trainjob.Name, err.Error())
		return ctrl.Result{}, err
	}

	// 获取trainingjob下面的所有pod情况，同步到status
	r.syncTrainjobPodConditions(ctx, &trainjob)

	// 状态
	st := state.NewState(&trainjob)
	if st == nil {
		return ctrl.Result{}, nil
	}

	if err := st.Execute(ctx); err != nil {
		tjStr, _ := json.Marshal(trainjob)
		klog.Errorf("%s: %s. trainingjob is %s", err, string(debug.Stack()), string(tjStr))
		return ctrl.Result{}, err
	}
	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *TrainingJobReconciler) SetupWithManager(mgr ctrl.Manager) error {
	r.Initialize()
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.TrainingJob{}).
		Owns(&batchvc.Job{}).
		Owns(&v1.ConfigMap{}).
		Watches(&source.Kind{Type: &vjschedpg.PodGroup{ObjectMeta: metav1.ObjectMeta{Namespace: r.Namespace}}}, &enqueueRequestForExtendRes{labelKey: systemv1alpha1.VcjobTjLabels, resourceVersion: true}).
		Watches(&source.Kind{Type: &v1.Pod{ObjectMeta: metav1.ObjectMeta{Namespace: r.Namespace}}}, &enqueueRequestForExtendRes{labelKey: systemv1alpha1.VcjobNameLabels}).
		Complete(r)
}

func (r *TrainingJobReconciler) initFaultTolerance() {
	r.restartingJobs = make(map[types.NamespacedName]*restartingJob)
	r.healthCheckers = make([]faulttolerance.HealthChecker, 0)
	r.healthCheckers = append(r.healthCheckers,
		faulttolerance.NewJobHangChecker(
			r.Client,
			&lokiclient.LokiClient{C: common.NewHTTPClient(), Host: config.SC.LokiURL},
			r.EventRecord,
		),
	)
	r.healthCheckers = append(r.healthCheckers,
		faulttolerance.NewFailedJobRestartableChecker(
			r.Client,
			&lokiclient.LokiClient{C: common.NewHTTPClient(), Host: config.SC.LokiURL},
			r.EventRecord,
			config.SC.FaultTolerance.FailedJobRestart,
		),
	)
	r.healthCheckers = append(r.healthCheckers, faulttolerance.NewNodeHealthChecker(r.EventRecord))
}
