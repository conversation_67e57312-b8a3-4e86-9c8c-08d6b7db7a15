package job

import (
	"context"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

func TestInitRsaKey(t *testing.T) {
	private := "test_private_key"
	public := "test_public_key"

	InitRsaKey(private, public)

	expectedPrivate := "-----BEGIN RSA PRIVATE KEY-----\ntest_private_key\n-----END RSA PRIVATE KEY-----"
	expectedPublic := "-----BEGIN PUBLIC KEY-----\ntest_public_key\n-----END PUBLIC KEY-----"

	assert.Equal(t, expectedPrivate, privateKey)
	assert.Equal(t, expectedPublic, publicKey)
}

func TestGetPodListByVcjob(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	vcjob := &v1alpha1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
	}

	pod := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
			Labels: map[string]string{
				"volcano.sh/job-name":      "test-job",
				"volcano.sh/job-namespace": "default",
			},
		},
		Spec: v1.PodSpec{
			NodeName: "test-node",
		},
	}

	_ = k8sFakeClient.Create(context.TODO(), pod)

	ctx := context.TODO()
	podList, err := getPodListByVcjob(ctx, vcjob, k8sFakeClient)
	assert.NoError(t, err)
	assert.Len(t, podList.Items, 1)
	assert.Equal(t, "test-pod", podList.Items[0].Name)
}

func TestUpdateJobAnnos(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	_ = v1alpha1.AddToScheme(scheme)
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	vcjob := &v1alpha1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
	}
	k8sFakeClient.Create(context.TODO(), vcjob)

	pod := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
			Labels: map[string]string{
				"volcano.sh/job-name":      "test-job",
				"volcano.sh/job-namespace": "default",
			},
		},
		Spec: v1.PodSpec{
			NodeName: "test-node",
		},
	}
	k8sFakeClient.Create(context.TODO(), pod)

	obj := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:        "test-job-obj",
			Namespace:   "default",
			Annotations: make(map[string]string),
		},
	}
	k8sFakeClient.Create(context.TODO(), obj)

	updateFunc := func() error {
		return k8sFakeClient.Update(context.TODO(), obj)
	}

	ct := context.Background()
	err := updateJobAnnos(ct, vcjob, obj, k8sFakeClient, updateFunc)
	assert.NoError(t, err)
	assert.NotNil(t, obj.GetAnnotations())
	assert.Equal(t, "test-node", obj.GetAnnotations()[nodeAnnotation])
}
