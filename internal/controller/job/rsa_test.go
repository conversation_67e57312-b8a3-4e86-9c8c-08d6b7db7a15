package job

// import (
// 	"testing"
// 	"time"
// )

// func TestRsaPublicKeyEncryt(t *testing.T) {
// 	source, err := RsaPublicKeyEncryt("Zcy441WQeQKx9y/bVCm06/ixnklqkk4bvJiflvbnYEt+jYgB6JHRh8pVdOptEAoViMbrsatjjq+8nXaDgwhO9froMq/1FqW2N7q4SqqUHFLRRS/FuqoBKwRkXAElfC9MzgJqbUh5mZDYNB6vFtrJEYsm3xSQ5YU8BcqgF5CgrE8=")
// 	if err != nil {
// 		t.<PERSON>rrorf("err: %s", err.Error())
// 	}

// 	timeStr := "00:00:31"
// 	duration, err := time.ParseDuration(timeStr)
// 	if err != nil {
// 		t.Logf("err: %s", err)
// 	}

// 	t.Logf("source: %s, %s", source, duration)
// }
