/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package job

import (
	"context"

	"hero.ai/hero-controllers/internal/controller/event"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/source"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"hero.ai/hero-controllers/internal/controller/job/state"
	vjschedpg "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

// NotebookReconciler reconciles a Notebook object
type NotebookReconciler struct {
	client.Client
	Scheme              *runtime.Scheme
	EventRecord         *event.EventRecord
	Namespace           string
	LocalURL            string
	VncBaseURL          string
	TokenPath           string
	InitSupervisorImage string
	IngressConfig       config.IngressConfig
}

func (r *NotebookReconciler) Initialize() {
	state.CreateJobByNotebook = r.createNotebookJob
	state.SyncJobByNotebook = r.syncJobByVcjob
}

//+kubebuilder:rbac:groups=system.hero.ai,resources=notebooks,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=system.hero.ai,resources=notebooks/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=system.hero.ai,resources=notebooks/finalizers,verbs=update
//+kubebuilder:rbac:groups=scheduling.volcano.sh,resources=podgroups,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=batch.volcano.sh,resources=jobs,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=services,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=networking.k8s.io,resources=ingresses,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=bus.volcano.sh,resources=commands,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=events,verbs=get;list;watch;create;update;patch;delete

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the Notebook object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.14.4/pkg/reconcile
func (r *NotebookReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)

	// TODO(user): your logic here
	var notebookJob = systemv1alpha1.Notebook{}
	err := r.Get(ctx, req.NamespacedName, &notebookJob)
	if err != nil {
		klog.Errorf("NotebookJob <%s> is terminating, skip management process.", req.NamespacedName)
		return ctrl.Result{}, nil
	}
	if err := AddFinalizer(ctx, r.Client, &notebookJob); err != nil {
		klog.Errorf("add Finalizer  of Job <%s> failed: %s", notebookJob.Name, err.Error())
		return ctrl.Result{}, err
	}

	r.syncNotebookjobPodConditions(ctx, &notebookJob)

	if err := r.deleteNotebook(ctx, &notebookJob); err != nil {
		return ctrl.Result{}, err
	}

	st := state.NewNotebookJobState(&notebookJob)
	if st == nil {
		return ctrl.Result{}, nil
	}

	if err := st.ExecuteNotebook(ctx); err != nil {
		return ctrl.Result{}, err
	}

	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *NotebookReconciler) SetupWithManager(mgr ctrl.Manager) error {
	r.Initialize()
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.Notebook{}).
		Owns(&batchvc.Job{}).
		Watches(&source.Kind{Type: &vjschedpg.PodGroup{ObjectMeta: metav1.ObjectMeta{Namespace: r.Namespace}}}, &enqueueRequestForExtendRes{labelKey: systemv1alpha1.VcjobNbLabels, resourceVersion: true}).
		Watches(&source.Kind{Type: &v1.Pod{ObjectMeta: metav1.ObjectMeta{Namespace: r.Namespace}}}, &enqueueRequestForExtendRes{labelKey: systemv1alpha1.NotebookNameLabels}).
		Complete(r)
}
