package faulttolerance

import (
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"time"

	"hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/lokiclient"
	"hero.ai/hero-controllers/internal/controller/config"
	"hero.ai/hero-controllers/internal/controller/event"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

type FailedJobRestartableChecker struct {
	client.Client
	lokiClient *lokiclient.LokiClient
	recorder   *event.EventRecord

	// 任务失败后的诊断重启配置
	ftConfig config.FailedJobRestart
	// 默认失败的训练任务不支持重启的 ExitCode
	UnrestartableExitCodes []int32
}

func NewFailedJobRestartableChecker(client client.Client, lokiClient *lokiclient.LokiClient, recorder *event.EventRecord, ftConfig config.FailedJobRestart) *FailedJobRestartableChecker {
	var UnrestartableExitCodes []int32
	for _, codes := range ftConfig.UnrestartableExitCodeRanges {
		if len(codes) == 1 {
			UnrestartableExitCodes = append(UnrestartableExitCodes, codes[0])
		} else if len(codes) == 2 {
			for i := codes[0]; i <= codes[1]; i++ {
				UnrestartableExitCodes = append(UnrestartableExitCodes, i)
			}
		}
	}
	return &FailedJobRestartableChecker{
		Client:                 client,
		lokiClient:             lokiClient,
		recorder:               recorder,
		ftConfig:               ftConfig,
		UnrestartableExitCodes: UnrestartableExitCodes,
	}
}

func (c *FailedJobRestartableChecker) CheckOnJobCreate(tj *v1alpha1.TrainingJob, vj *batchvc.Job) {
}

func (c *FailedJobRestartableChecker) AsyncCheck(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) {
}

func (c *FailedJobRestartableChecker) StopAsyncCheck(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) {
}

func (c *FailedJobRestartableChecker) Check(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) bool {
	if vj.Status.State.Phase != batchvc.Failed || !c.hasSpec(tj) || c.isDone(tj) {
		return false
	}
	klog.Infof("Checking if failed volcanojob %s/%s of trainingjob %s at version %d restartable",
		vj.Namespace, vj.Name, tj.Name, tj.Status.Version)
	c.recorder.Eventf(tj, corev1.EventTypeNormal, v1alpha1.EventFailedJobRestartableChecking,
		v1alpha1.EventFailedJobRestartableCheckingMessage, tj.Status.Version)

	var args v1alpha1.FailedJobRestartableCheckArgs
	for _, hc := range tj.Spec.FaultTolerance.HealthChecks {
		if hc.Name == v1alpha1.FailedJobRestartableCheck {
			if err := json.Unmarshal(hc.Args.Raw, &args); err != nil {
				klog.Warningf("Invalid FailedJobRestartable healthCheck args of trainingjob %s/%s: %s",
					tj.Namespace, tj.Name, err)
				return false
			}
			break
		}
	}

	restartable := false
	switch args.Policy {
	case v1alpha1.OnFailureRestartable:
		restartable = true
	case v1alpha1.ExitCodeAndErrorMsgRestartable:
		restartable = c.checkByExitCodeAndErrorMsg(tj, vj, pods, args)
	case v1alpha1.AdvancedRestartable:
		restartable = c.checkByAdvancedPolicy(tj, vj, pods, args)
	default:
		klog.Warningf("Invalid FailedJobRestartable healthCheck policy %s of trainingjob %s/%s",
			args.Policy, tj.Namespace, tj.Name)
		return false
	}

	var message string
	var resultName v1alpha1.HealthCheckResultName
	if restartable {
		resultName = v1alpha1.FailedJobRestartable
		message = fmt.Sprintf("Failed job is restartable at version %d, checked by %s policy",
			tj.Status.Version, args.Policy)
		c.recorder.Eventf(tj, corev1.EventTypeNormal, v1alpha1.EventFailedJobRestartable,
			v1alpha1.EventFailedJobRestartableMessage, tj.Status.Version, args.Policy)
	} else {
		resultName = v1alpha1.FailedJobUnrestartable
		message = fmt.Sprintf("Failed job is unrestartable at version %d, checked by %s policy",
			tj.Status.Version, args.Policy)
		c.recorder.Eventf(tj, corev1.EventTypeWarning, v1alpha1.EventFailedJobUnrestartable,
			v1alpha1.EventFailedJobUnrestartableMessage, tj.Status.Version, args.Policy)
	}

	result := v1alpha1.HealthCheckResult{
		Name:    v1alpha1.FailedJobRestartableCheck,
		Result:  resultName,
		Message: message,
		Time:    metav1.Now(),
	}
	version := strconv.Itoa(int(tj.Status.Version))
	ft := tj.Status.FaultTolerance[version]
	ft.HealthChecks = append(ft.HealthChecks, result)
	tj.Status.FaultTolerance[version] = ft

	return restartable
}

func (c *FailedJobRestartableChecker) hasSpec(tj *v1alpha1.TrainingJob) bool {
	if tj.Spec.FaultTolerance == nil {
		return false
	}
	for _, hc := range tj.Spec.FaultTolerance.HealthChecks {
		if hc.Name == v1alpha1.FailedJobRestartableCheck {
			return true
		}
	}
	return false
}

func (c *FailedJobRestartableChecker) isDone(tj *v1alpha1.TrainingJob) bool {
	version := strconv.Itoa(int(tj.Status.Version))
	for _, result := range tj.Status.FaultTolerance[version].HealthChecks {
		if result.Name == v1alpha1.FailedJobRestartableCheck {
			return true
		}
	}
	return false
}

func (c *FailedJobRestartableChecker) checkByExitCodeAndErrorMsg(
	tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod, args v1alpha1.FailedJobRestartableCheckArgs) bool {

	// loki 采集日志有延迟，等待日志全部归档
	time.Sleep(10 * time.Second)

	var limit int64
	if c.ftConfig.LatestLogs == nil {
		limit = config.DefaultFailedJobLatestLogs
	} else {
		limit = int64(*c.ftConfig.LatestLogs)
	}

	// 只要有一个 pod 可重启，则 job 可重启
	for i := range pods {
		if len(args.LogRules) == 0 {
			continue
		}

		logs, err := FetchPodLatestLogs(&pods[i], limit, c.lokiClient)
		if err != nil {
			klog.Errorf("Failed to fetch pod %s/%s latest logs: %v", pods[i].Namespace, pods[i].Name, err)
			continue
		}

		// 只要满足任意一条可重启的 policy 即可重启
		for j := range args.LogRules {
			if len(args.LogRules[j]) == 0 {
				continue
			}
			for _, l := range logs {
				if ok, _ := ContainsAll(l.Line, args.LogRules[j]); ok {
					return true
				}
			}
		}
	}
	return false
}

func (c *FailedJobRestartableChecker) checkByAdvancedPolicy(
	tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod, args v1alpha1.FailedJobRestartableCheckArgs) bool {
	// loki 采集日志有延迟，等待日志全部归档
	time.Sleep(10 * time.Second)

	limit := c.getLogLimit()

	// 只要有一个 pod 可重启，则 job 可重启
	for i := range pods {
		if c.isPodRestartable(&pods[i], limit, args) {
			return true
		}
	}
	return false
}

// getLogLimit returns the configured log limit
func (c *FailedJobRestartableChecker) getLogLimit() int64 {
	if c.ftConfig.LatestLogs == nil {
		return config.DefaultFailedJobLatestLogs
	}
	return int64(*c.ftConfig.LatestLogs)
}

// isPodRestartable checks if a pod is restartable based on exit code and logs
func (c *FailedJobRestartableChecker) isPodRestartable(pod *corev1.Pod, limit int64, args v1alpha1.FailedJobRestartableCheckArgs) bool {
	exitCode := c.getPodExitCode(pod)

	// exitCode 是不可重启的情况
	if slices.Contains(c.UnrestartableExitCodes, exitCode) {
		return false
	}

	logs, err := FetchPodLatestLogs(pod, limit, c.lokiClient)
	if err != nil {
		klog.Errorf("Failed to fetch pod %s/%s latest logs: %v", pod.Namespace, pod.Name, err)
		return false
	}

	// 检查平台内置的重启策略
	if c.matchesAdvancedRestartPolicies(logs, exitCode) {
		return true
	}

	// 检查用户自定义的重启规则
	if c.matchesLogRules(logs, args.LogRules) {
		return true
	}

	return false
}

// getPodExitCode extracts the exit code from the pod's container status
func (c *FailedJobRestartableChecker) getPodExitCode(pod *corev1.Pod) int32 {
	for _, cont := range pod.Status.ContainerStatuses {
		if cont.Name == v1alpha1.DefaultUserContainerName {
			return cont.State.Terminated.ExitCode
		}
	}
	return 0
}

// matchesAdvancedRestartPolicies checks if the logs match any of the restart policies
func (c *FailedJobRestartableChecker) matchesAdvancedRestartPolicies(logs []lokiclient.Entry, exitCode int32) bool {
	restartPolicies := c.ftConfig.Policies
	for j := range restartPolicies {
		if restartPolicies[j].ExitCode != nil && exitCode != *restartPolicies[j].ExitCode {
			continue
		}
		if len(restartPolicies[j].LogExps) == 0 {
			continue
		}

		for _, l := range logs {
			if ok, _ := ContainsAll(l.Line, restartPolicies[j].LogExps); ok {
				return true
			}
		}
	}
	return false
}

// matchesLogRules checks if the logs match any of the log rules
func (c *FailedJobRestartableChecker) matchesLogRules(logs []lokiclient.Entry, logRules [][]string) bool {
	for j := range logRules {
		if len(logRules[j]) == 0 {
			continue
		}
		for _, l := range logs {
			if ok, _ := ContainsAll(l.Line, logRules[j]); ok {
				return true
			}
		}
	}
	return false
}
