package faulttolerance

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/lokiclient"
	"hero.ai/hero-controllers/internal/controller/event"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

type JobHangChecker struct {
	mutex sync.RWMutex

	client.Client
	lokiClient *lokiclient.LokiClient
	recorder   *event.EventRecord
	jobs       map[types.NamespacedName]*jobHangCheckingStatus
}

type jobHangCheckingStatus struct {
	job            *v1alpha1.TrainingJob
	version        int32
	maxLogInterval int32
	lastLogTime    time.Time
	stopChan       chan struct{}
}

func NewJobHangChecker(client client.Client, lokiClient *lokiclient.LokiClient, recorder *event.EventRecord) *JobHangChecker {
	return &JobHangChecker{
		Client:     client,
		lokiClient: lokiClient,
		recorder:   recorder,
		jobs:       make(map[types.NamespacedName]*jobHangCheckingStatus),
	}
}

func (c *JobHangChecker) CheckOnJobCreate(tj *v1alpha1.TrainingJob, vj *batchvc.Job) {}

func (c *JobHangChecker) Check(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) bool {
	return false
}

func (c *JobHangChecker) AsyncCheck(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) {
	if tj.Status.State.Phase != v1alpha1.Running || !c.hasSpec(tj) || c.isDone(tj) {
		return
	}

	jobKey := types.NamespacedName{Name: tj.Name, Namespace: tj.Namespace}
	if _, ok := c.jobs[jobKey]; ok {
		return
	}

	var hangArgs v1alpha1.JobHangCheckArgs
	for _, hc := range tj.Spec.FaultTolerance.HealthChecks {
		if hc.Name == v1alpha1.JobHangCheck {
			if err := json.Unmarshal(hc.Args.Raw, &hangArgs); err != nil {
				klog.Warningf("Invalid JobHang healthCheck args of trainingjob %s/%s: %s",
					tj.Namespace, tj.Name, err)
				return
			}
			break
		}
	}

	c.mutex.Lock()
	c.jobs[jobKey] = &jobHangCheckingStatus{
		job:            tj,
		version:        tj.Status.Version,
		maxLogInterval: hangArgs.MaxLogInterval,
		stopChan:       make(chan struct{}),
	}
	c.mutex.Unlock()

	go c.Start(jobKey)
}

func (c *JobHangChecker) Start(jobKey types.NamespacedName) {
	job := c.jobs[jobKey].job.DeepCopy()
	version := strconv.Itoa(int(job.Status.Version))
	klog.Infof("trainingjob %s of version %s hang health check start", jobKey, version)
	c.recorder.Eventf(job, corev1.EventTypeNormal, v1alpha1.EventJobHangHealthCheckStarted,
		v1alpha1.EventJobHangHealthCheckStartedMessage, version)

	wait.Until(func() {
		job := c.jobs[jobKey].job
		now := time.Now()
		maxLogDuration := time.Duration(c.jobs[jobKey].maxLogInterval) * time.Second

		ftStatus, ok := job.Status.FaultTolerance[version]
		if !ok {
			c.Stop(jobKey)
			return
		}

		if now.After(c.jobs[jobKey].lastLogTime.Add(maxLogDuration)) &&
			now.After(ftStatus.StartTime.Time.Add(maxLogDuration)) {
			c.verifyingHang(jobKey)
		}
	}, 20*time.Second, c.jobs[jobKey].stopChan)

	klog.Infof("trainingjob %+v hang health check stopped at version %s", jobKey, version)
	c.recorder.Eventf(job, corev1.EventTypeNormal, v1alpha1.EventJobHangHealthCheckStopped,
		v1alpha1.EventJobHangHealthCheckStoppedMessage, version)
}

func (c *JobHangChecker) verifyingHang(jobKey types.NamespacedName) {
	// 查询 tj 最新状态，停止检查已结束的作业
	newTj := v1alpha1.TrainingJob{}
	err := c.Client.Get(context.TODO(), jobKey, &newTj)
	if err != nil && !apierrors.IsNotFound(err) {
		klog.Errorf("get trainingjob %s error: %s", jobKey, err)
		return
	}

	// 如果当前作业已停止或已检测到 hang 住，停止检查
	if apierrors.IsNotFound(err) ||
		newTj.Status.State.Phase != v1alpha1.Running ||
		c.isDone(&newTj) {
		c.Stop(jobKey)
		return
	}

	// 从 loki 查最近 maxLogInterval 秒时间范围内最新1条日志
	version := strconv.Itoa(int(newTj.Status.Version))
	klog.Infof("Fetching log for trainingjob %+v of version %s", jobKey, version)
	podNames, err := c.getPodNamesOfTrainingJob(newTj.Namespace, newTj.Name, version)
	if err != nil {
		klog.Error(err)
		return
	}
	if len(podNames) == 0 {
		return
	}

	filter := lokiclient.SearchFilter{
		NamespaceFilter: []string{newTj.Namespace},
		PodFilter:       podNames,
		ContainerFilter: []string{v1alpha1.DefaultUserContainerName},
		Starttime:       time.Now().Add(time.Duration(-c.jobs[jobKey].maxLogInterval) * time.Second),
		Endtime:         time.Now(),
		Size:            1,
		Sort:            "desc",
	}
	queryStr, err := lokiclient.BuildQueryString(filter)
	if err != nil {
		klog.Error(err)
	}
	logs, err := c.lokiClient.SearchFullRange(
		queryStr, filter.Size, filter.Starttime, filter.Endtime, filter.Sort, "", "")
	if err != nil {
		klog.Error(err)
	}

	// hang 住，更新 status，停止检查
	if len(logs.Logs) == 0 {
		klog.Infof("trainingjob %+v of version %s hang detected", jobKey, version)
		c.recorder.Eventf(&newTj, corev1.EventTypeWarning, v1alpha1.EventJobHangDetected,
			v1alpha1.EventJobHangDetectedMessage, newTj.Status.Version, c.jobs[jobKey].maxLogInterval)
		if err := c.updateJobStatus(&newTj); err != nil {
			klog.Errorf("update trainingjob %s status for hang health check result failed: %s", jobKey, err)
		} else {
			c.Stop(jobKey)
		}
		return
	}

	// 更新检测到的最新的日志时间
	c.jobs[jobKey].job = &newTj
	c.jobs[jobKey].lastLogTime = logs.Logs[0].Timestamp
	klog.V(4).Infof("trainingjob %+v at version %s latest log at %s",
		jobKey, version, c.jobs[jobKey].lastLogTime)
}

func (c *JobHangChecker) Stop(jobKey types.NamespacedName) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	if _, ok := c.jobs[jobKey]; ok {
		close(c.jobs[jobKey].stopChan)
		delete(c.jobs, jobKey)
	}
}

func (c *JobHangChecker) getPodNamesOfTrainingJob(namespace, name, version string) ([]string, error) {
	var names []string
	pods, err := GetPodListOfTrainingJob(namespace, name, version, c.Client)
	if err != nil {
		return names, err
	}
	for i := range pods.Items {
		names = append(names, pods.Items[i].Name)
	}
	return names, nil
}

func (c *JobHangChecker) updateJobStatus(newTj *v1alpha1.TrainingJob) error {
	jobKey := types.NamespacedName{Namespace: newTj.Namespace, Name: newTj.Name}
	version := strconv.Itoa(int(newTj.Status.Version))
	hangResult := v1alpha1.HealthCheckResult{
		Name:    v1alpha1.JobHangCheck,
		Result:  v1alpha1.JobHang,
		Message: fmt.Sprintf("No logs more than %d seconds", c.jobs[jobKey].maxLogInterval),
		Time:    metav1.Now(),
	}
	ft := newTj.Status.FaultTolerance[version]
	ft.HealthChecks = append(ft.HealthChecks, hangResult)
	newTj.Status.FaultTolerance[version] = ft
	return c.Status().Update(context.TODO(), newTj)
}

func (c *JobHangChecker) hasSpec(tj *v1alpha1.TrainingJob) bool {
	if tj.Spec.FaultTolerance == nil {
		return false
	}
	for _, hc := range tj.Spec.FaultTolerance.HealthChecks {
		if hc.Name == v1alpha1.JobHangCheck {
			return true
		}
	}
	return false
}

func (c *JobHangChecker) isDone(tj *v1alpha1.TrainingJob) bool {
	version := strconv.Itoa(int(tj.Status.Version))
	for _, result := range tj.Status.FaultTolerance[version].HealthChecks {
		if result.Name == v1alpha1.JobHangCheck {
			return true
		}
	}
	return false
}

func (c *JobHangChecker) StopAsyncCheck(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) {
	jobKey := types.NamespacedName{Namespace: tj.Namespace, Name: tj.Name}
	c.mutex.Lock()
	defer c.mutex.Unlock()
	if job, ok := c.jobs[jobKey]; ok {
		if job.version != tj.Status.Version {
			return
		}
		close(c.jobs[jobKey].stopChan)
		delete(c.jobs, jobKey)
	}
}
