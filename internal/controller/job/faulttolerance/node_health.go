package faulttolerance

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	"hero.ai/hero-controllers/internal/controller/event"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

type NodeHealthChecker struct {
	recorder *event.EventRecord
}

type nodeHealthResultItem struct {
	CheckType string `json:"checkType"`
	Result    string `json:"result"`
	Message   string `json:"message"`
}

func NewNodeHealthChecker(recorder *event.EventRecord) *NodeHealthChecker {
	return &NodeHealthChecker{
		recorder: recorder,
	}
}

func (c *NodeHealthChecker) AsyncCheck(tj *v1alpha1.<PERSON><PERSON><PERSON>, vj *batchvc.Job, pods []corev1.Pod) {
}

func (c *NodeHealthChecker) StopAsyncCheck(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) {
}

func (c *NodeHealthChecker) CheckOnJobCreate(tj *v1alpha1.TrainingJob, vj *batchvc.Job) {
	if !c.hasSpec(tj) || len(vj.Spec.Tasks) == 0 {
		return
	}
	klog.Infof("Injecting initcontainer %q for trainingjob %s/%s at version %d",
		v1alpha1.InitNodeHealthCheckContainerName, tj.Namespace, tj.Name, tj.Status.Version)

	// TODO(wuyiqiang): 考虑不同 task 资源配置不相同的情况

	masterPodName := "tj-" + tj.Name + "-" + vj.Spec.Tasks[0].Name + "-0"
	taskRankIndex := 0
	taskWorldSize := c.getTaskWorldSize(tj)
	for i := range vj.Spec.Tasks {
		task := &vj.Spec.Tasks[i]
		_, podDeviceeNum, podResource := c.getTaskResourceInfo(&tj.Spec.Tasks[i])
		Container := corev1.Container{
			Name:            v1alpha1.InitNodeHealthCheckContainerName,
			Image:           config.SC.FaultTolerance.HealthCheckImage,
			ImagePullPolicy: corev1.PullAlways,
			Command: []string{
				"/bin/sh",
				"-c",
				// "sleep infinity",
				fmt.Sprintf("bash /app/checker.sh %s %s", masterPodName, tj.Namespace),
			},
			Env: []corev1.EnvVar{
				{Name: "TASK_WORLD_SIZE", Value: strconv.Itoa(taskWorldSize)},
				{Name: "TASK_RANK_INDEX", Value: strconv.Itoa(taskRankIndex)},
				{Name: "NAMESPACE", Value: tj.Namespace},
				{Name: "POD_DEVICE_NUM", Value: strconv.Itoa(podDeviceeNum)},
				{Name: "LOCAL_POD_IP", ValueFrom: &corev1.EnvVarSource{
					FieldRef: &corev1.ObjectFieldSelector{
						FieldPath: "status.podIP",
					}}},
			},
			Resources: corev1.ResourceRequirements{Requests: podResource, Limits: podResource},
		}
		taskRankIndex += int(task.Replicas)

		task.Template.Spec.ServiceAccountName = "trainingjob-health-checker" // 增加 pod 的操作权限
		task.Template.Spec.InitContainers = append(task.Template.Spec.InitContainers, Container)

	}
}

func (c *NodeHealthChecker) Check(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) bool {
	if !c.hasSpec(tj) || c.isDone(tj) || !c.isAllInitDone(pods) {
		return false
	}

	results := make(map[string][]string, 1)
	for i := range pods {
		pod := &pods[i]
		for key, value := range pod.Annotations {
			if key != "system.hero.ai/node-health-check-result" {
				continue
			}
			healthInfo := make([]nodeHealthResultItem, 0)
			if err := json.Unmarshal([]byte(value), &healthInfo); err != nil {
				klog.Errorf("Invalid node health check result for pod %s of trainingjob %s/%s at version %d: %s",
					pod.Name, tj.Namespace, tj.Name, tj.Status.Version, err)
			}
			for _, info := range healthInfo {
				issue := fmt.Sprintf("%s/%s", info.CheckType, info.Result)
				podNode := fmt.Sprintf("%s/%s", pod.Name, pod.Spec.NodeName)
				if _, ok := results[issue]; !ok {
					results[issue] = []string{podNode}
				} else {
					results[issue] = append(results[issue], podNode)
				}
			}
		}
	}

	version := strconv.Itoa(int(tj.Status.Version))
	statusResult := v1alpha1.HealthCheckResult{
		Name:    v1alpha1.NodeHealthBeforeStartCheck,
		Result:  v1alpha1.NodeHealthyBeforeStart,
		Message: "All nodes are healthy",
		Time:    metav1.Now(),
	}
	if len(results) > 0 {
		statusResult.Result = v1alpha1.NodeUnhealthyBeforeStart
		msgBytes, err := json.Marshal(results)
		if err != nil {
			statusResult.Message = "Internal Error"
			klog.Errorf("Failed to marshal node health check result of trainingjob %s/%s version %d: %v",
				tj.Namespace, tj.Name, tj.Status.Version, err)
		}
		statusResult.Message = string(msgBytes)
		c.recorder.Eventf(tj, corev1.EventTypeWarning, v1alpha1.EventNodeUnhealthyBeforeStart,
			v1alpha1.EventNodeUnhealthyBeforeStartMessage, tj.Status.Version)
	} else {
		c.recorder.Eventf(tj, corev1.EventTypeNormal, v1alpha1.EventNodeHealthyBeforeStart,
			v1alpha1.EventNodeHealthyBeforeStartMessage, tj.Status.Version)
	}
	ft := tj.Status.FaultTolerance[version]
	ft.HealthChecks = append(ft.HealthChecks, statusResult)
	tj.Status.FaultTolerance[version] = ft
	return len(results) > 0
}

func (c *NodeHealthChecker) hasSpec(tj *v1alpha1.TrainingJob) bool {
	if tj.Spec.FaultTolerance == nil {
		return false
	}
	for _, hc := range tj.Spec.FaultTolerance.HealthChecks {
		if hc.Name == v1alpha1.NodeHealthBeforeStartCheck {
			return true
		}
	}
	return false
}

func (c *NodeHealthChecker) isDone(tj *v1alpha1.TrainingJob) bool {
	version := strconv.Itoa(int(tj.Status.Version))
	for _, result := range tj.Status.FaultTolerance[version].HealthChecks {
		if result.Name == v1alpha1.NodeHealthBeforeStartCheck {
			return true
		}
	}
	return false
}

func (c *NodeHealthChecker) isAllInitDone(pods []corev1.Pod) bool {
	if len(pods) == 0 {
		return false
	}
	var terminatedNum int
	for i := range pods {
		pod := &pods[i]
		for _, cont := range pod.Status.InitContainerStatuses {
			if cont.Name == v1alpha1.InitNodeHealthCheckContainerName && cont.State.Terminated != nil {
				terminatedNum += 1
			}
		}
	}
	return len(pods) == terminatedNum
}

func (c *NodeHealthChecker) getTaskWorldSize(tj *v1alpha1.TrainingJob) int {
	var wordSize int
	for _, taskSpc := range tj.Spec.Tasks {
		wordSize += int(taskSpc.Replicas)
	}
	return wordSize
}

func (c *NodeHealthChecker) getTaskResourceInfo(taskSpc *v1alpha1.Task) (string, int, corev1.ResourceList) {
	podDeviceeNum := 0
	accDeviceType := "CPU"
	for resourceName, quantity := range taskSpc.Resource {
		if strings.Contains(resourceName.String(), "nvidia.com") && quantity.Value() > 0 {
			accDeviceType = "NVIDIA_GPU"
			podDeviceeNum = int(quantity.Value())
			break
		}
		if strings.Contains(resourceName.String(), "huawei.com") && quantity.Value() > 0 {
			accDeviceType = "ASCEND_NPU"
			podDeviceeNum = int(quantity.Value())
			break
		}
	}
	return accDeviceType, podDeviceeNum, taskSpc.Resource
}
