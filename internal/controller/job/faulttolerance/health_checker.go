package faulttolerance

import (
	"context"
	"regexp"
	"strings"
	"time"

	"hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/actions/logexport/lokiclient"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"sigs.k8s.io/controller-runtime/pkg/client"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const (
	VcJobNameLabel      = "volcano.sh/job-name"
	VcJobNamespaceLabel = "volcano.sh/job-namespace"
)

type HealthChecker interface {
	CheckOnJobCreate(tj *v1alpha1.TrainingJob, vj *batchvc.Job)
	Check(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod) bool
	AsyncCheck(tj *v1alpha1.<PERSON><PERSON>ob, vj *batchvc.Job, pods []corev1.Pod)
	StopAsyncCheck(tj *v1alpha1.TrainingJob, vj *batchvc.Job, pods []corev1.Pod)
}

func GetPodListOfTrainingJob(namespace, name, version string, c client.Client) (corev1.PodList, error) {
	var podList corev1.PodList
	err := c.List(context.TODO(), &podList, &client.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set{
			VcJobNameLabel:                       vcjobbuilder.VcjobNameTrainjobPrefix + name,
			VcJobNamespaceLabel:                  namespace,
			vcjobbuilder.TrainingJobVersionLabel: version,
		}),
	})
	return podList, err
}

func FetchPodLatestLogs(pod *corev1.Pod, limit int64, client *lokiclient.LokiClient) ([]lokiclient.Entry, error) {
	var endTime time.Time
	for _, cs := range pod.Status.ContainerStatuses {
		if cs.Name == v1alpha1.DefaultUserContainerName && cs.LastTerminationState.Terminated != nil {
			endTime = cs.LastTerminationState.Terminated.FinishedAt.Time
			break
		}
	}
	if endTime.IsZero() {
		endTime = time.Now()
	}

	filter := lokiclient.SearchFilter{
		NamespaceFilter: []string{pod.Namespace},
		PodFilter:       []string{pod.Name},
		ContainerFilter: []string{v1alpha1.DefaultUserContainerName},
		Starttime:       pod.Status.StartTime.Time,
		Endtime:         endTime,
		Size:            limit,
		Sort:            "desc",
	}
	return client.SearchLogs(filter)
}

// ContainsAll 检查目标字符串是否包含所有给定的模式
// patterns 可以是普通字符串或正则表达式模式
func ContainsAll(text string, patterns []string) (bool, error) {
	for _, pattern := range patterns {
		// 尝试将模式编译为正则表达式
		reg, err := regexp.Compile(pattern)
		if err != nil {
			// 如果不是有效的正则表达式，则作为普通字符串处理
			if !strings.Contains(text, pattern) {
				return false, nil
			}
		} else {
			// 使用正则表达式匹配
			if !reg.MatchString(text) {
				return false, nil
			}
		}
	}
	return true, nil
}
