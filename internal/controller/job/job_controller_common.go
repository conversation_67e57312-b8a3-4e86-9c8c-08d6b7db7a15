package job

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"strings"

	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/retry"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

var (
	privateKey string
	publicKey  string
)

func AddFinalizer(ctx context.Context, cli client.Client, obj client.Object) error {
	if obj.GetDeletionTimestamp().IsZero() {
		if !controllerutil.ContainsFinalizer(obj, Finalizer) {
			controllerutil.AddFinalizer(obj, Finalizer)
			if err := cli.Update(ctx, obj); err != nil {
				return err
			}
		}
	}
	return nil
}

func RemoveFinalizer(ctx context.Context, cli client.Client, obj client.Object) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		if err := cli.Get(ctx, client.ObjectKeyFromObject(obj), obj); err != nil {
			return err
		}

		// pod停止，删除Finalizer
		if controllerutil.ContainsFinalizer(obj, Finalizer) {
			controllerutil.RemoveFinalizer(obj, Finalizer)
		}
		return cli.Update(ctx, obj)
	})
}

func DeleteVolcanoJob(ctx context.Context, cli client.Client, prefix string, obj client.Object, vcjob *batchvc.Job) error {
	if !obj.GetDeletionTimestamp().IsZero() {
		return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
			vj := &batchvc.Job{}
			if err = cli.Get(ctx, client.ObjectKey{Name: prefix + obj.GetName(), Namespace: obj.GetNamespace()}, vj); err != nil {
				return
			}

			return cli.Delete(ctx, vj)
		})
	}

	return nil
}

func InitRsaKey(private, public string) {
	// block := &pem.Block{
	// 	Type:  "RSA PUBLIC KEY",
	// 	Bytes: []byte(public),
	// }
	// publicKey = string(pem.EncodeToMemory(block))
	publicKey = fmt.Sprintf("-----BEGIN PUBLIC KEY-----\n%s\n-----END PUBLIC KEY-----", public)
	privateKey = fmt.Sprintf("-----BEGIN RSA PRIVATE KEY-----\n%s\n-----END RSA PRIVATE KEY-----", private)
	// block2 := &pem.Block{
	// 	Type:  "RSA PRIVATE KEY",
	// 	Bytes: []byte(private),
	// }
	// privateKey = string(pem.EncodeToMemory(block2))
}

// 创建私有镜像secret
func CreateRegistrySecret(ns, username, password, registryName string, cli client.Client, obj metav1.Object, scheme *runtime.Scheme) (string, error) {
	pwd, err := RsaPublicKeyEncryt(password)
	if err != nil {
		return "", err
	}
	secretName := generateMD5(username + pwd + strings.Split(registryName, "/")[0])
	var secret v1.Secret
	err = cli.Get(context.TODO(), types.NamespacedName{
		Name:      secretName,
		Namespace: ns,
	}, &secret)

	if !apierrors.IsNotFound(err) {
		return secretName, nil
	}

	auth := base64Encode(fmt.Sprintf("%s:%s", username, pwd))
	value := fmt.Sprintf(`{"auths":{"%s":{"auth":"%s"}}}`, strings.Split(registryName, "/")[0], string(auth))

	secretPara := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      secretName,
			Namespace: ns,
		},
		Data: map[string][]byte{
			v1.DockerConfigJsonKey: []byte(value),
		},
		Type: v1.SecretTypeDockerConfigJson,
	}

	if err := controllerutil.SetControllerReference(obj, secretPara, scheme); err != nil {
		return secretName, err
	}

	if err := cli.Create(context.TODO(), secretPara); err != nil {
		return secretName, err
	}

	return secretName, nil
}

func RsaPublicKeyEncryt(pwd string) (string, error) {
	block, _ := pem.Decode([]byte(privateKey))

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes) // ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		fmt.Println("ParsePKCS1PrivateKey err:", err)
		return "", err
	}

	block1, _ := pem.Decode([]byte(publicKey))
	if block1 == nil {
		return "", nil
	}

	pubInterface, err := x509.ParsePKIXPublicKey(block1.Bytes)
	if err != nil {
		return "", err
	}
	pubKey := pubInterface.(*rsa.PublicKey)

	var r = XRsa{
		privateKey: privateKey.(*rsa.PrivateKey),
		publicKey:  pubKey,
	}

	plainText, err := r.PrivateDecrypt(pwd)
	if err != nil {
		return "", err
	}
	return plainText, nil
}

func getPodListByVcjob(ctx context.Context, vcjob *batchvc.Job, c client.Client) (v1.PodList, error) {
	var podList v1.PodList
	err := c.List(ctx, &podList, &client.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set{
			vcjobPodJobNameLabels:   vcjob.Name,
			vcjobPodNamespaceLabels: vcjob.Namespace,
		}),
	})
	if err != nil {
		return podList, err
	}

	return podList, nil
}

func updateJobAnnos(ctx context.Context, vcjob *batchvc.Job, obj client.Object, c client.Client, updateFunc func() error) error {
	pods, err := getPodListByVcjob(ctx, vcjob, c)
	if err != nil {
		return err
	}
	var nodeMap = make(map[string]struct{})
	for _, pod := range pods.Items {
		if len(pod.Spec.NodeName) > 0 {
			nodeMap[pod.Spec.NodeName] = struct{}{}
		}
	}

	var nodes []string
	for node := range nodeMap {
		nodes = append(nodes, node)
	}

	annotations := obj.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}
	annotations[nodeAnnotation] = strings.Join(nodes, ",")
	obj.SetAnnotations(annotations)

	return updateFunc()
}

func IsVolcanoJobFinished(vcjob *batchvc.Job) bool {
	if vcjob == nil {
		return true
	}
	phase := vcjob.Status.State.Phase
	if phase == batchvc.Aborted ||
		phase == batchvc.Completed ||
		phase == batchvc.Failed ||
		phase == batchvc.Terminated {
		return true
	}
	return false
}
