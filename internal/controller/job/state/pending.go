package state

import (
	"context"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

type pendingState struct {
	trainjob    *systemv1alpha1.TrainingJob
	notebookjob *systemv1alpha1.Notebook
}

func (ps *pendingState) Execute(ctx context.Context) error {
	return CreateJob(ctx, ps.trainjob, func(jobstatus *systemv1alpha1.TrainingJobStatus) bool {
		jobstatus.State.Phase = systemv1alpha1.Queuing
		return true
	})
}

func (ps *pendingState) ExecuteNotebook(ctx context.Context) error {
	return CreateJobByNotebook(ctx, ps.notebookjob, func(jobstatus *systemv1alpha1.NotebookStatus) bool {
		jobstatus.State = systemv1alpha1.NotebookStateStartUp
		return true
	})
}
