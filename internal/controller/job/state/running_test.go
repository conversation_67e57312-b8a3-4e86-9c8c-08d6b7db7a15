package state

import (
	"context"
	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"testing"
)

func TestRunningState_Execute(t *testing.T) {
	// Mock data
	trainjob := &systemv1alpha1.TrainingJob{
		Status: systemv1alpha1.TrainingJobStatus{
			State: systemv1alpha1.TrainingJobState{
				Phase: systemv1alpha1.Running,
			},
		},
	}

	// Mock implementation for SyncJobByVcJob
	SyncJobByVcJob = func(ctx context.Context, job *systemv1alpha1.TrainingJob, fn UpdateJobStatusFn) error {
		if fn != nil {
			fn(&job.Status)
		}
		return nil
	}

	// Create a runningState
	rs := &runningState{
		trainjob:    trainjob,
		recordEvent: true,
	}

	// Execute the state transition
	err := rs.Execute(context.TODO())

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.Running, trainjob.Status.State.Phase, "The TrainingJob phase should remain Running")
}

func TestRunningState_ExecuteNotebook(t *testing.T) {
	// Mock data
	notebook := &systemv1alpha1.Notebook{
		Status: systemv1alpha1.NotebookStatus{
			State: systemv1alpha1.NotebookStateRunning,
		},
	}

	// Mock implementation for SyncJobByNotebook
	SyncJobByNotebook = func(ctx context.Context, job *systemv1alpha1.Notebook, fn UpdateNotebookJobStatusFn) error {
		if fn != nil {
			fn(&job.Status)
		}
		return nil
	}

	// Create a runningState
	rs := &runningState{
		notebookjob: notebook,
		recordEvent: true,
	}

	// Execute the state transition
	err := rs.ExecuteNotebook(context.TODO())

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.NotebookStateRunning, notebook.Status.State, "The Notebook state should remain Running")
}
