package state

import (
	"context"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

type runningState struct {
	trainjob    *systemv1alpha1.TrainingJob
	notebookjob *systemv1alpha1.Notebook
	recordEvent bool
}

func (rs *runningState) Execute(ctx context.Context) error {
	return SyncJobByVcJob(ctx, rs.trainjob, func(jobstatus *systemv1alpha1.TrainingJobStatus) bool {
		return rs.recordEvent
	})
}

func (rs *runningState) ExecuteNotebook(ctx context.Context) error {
	return SyncJobByNotebook(ctx, rs.notebookjob, func(jobstatus *systemv1alpha1.NotebookStatus) bool {
		return rs.recordEvent
	})
}
