package state

import (
	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"testing"
)

func TestNewState(t *testing.T) {
	// Test cases for different states of the TrainingJob
	testCases := []struct {
		name     string
		phase    systemv1alpha1.TrainingJobPhase
		expected State
	}{
		{"Pending", systemv1alpha1.Pending, &pendingState{}},
		{"Queuing", systemv1alpha1.Queuing, &runningState{recordEvent: true}},
		{"Running", systemv1alpha1.Running, &runningState{}},
		{"Stopping", systemv1alpha1.Stopping, &runningState{}},
		{"Completed", systemv1alpha1.Completed, nil},
		{"Failed", systemv1alpha1.Failed, nil},
		{"Stopped", systemv1alpha1.Stopped, nil},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			trainjob := &systemv1alpha1.TrainingJob{
				Status: systemv1alpha1.TrainingJobStatus{
					State: systemv1alpha1.TrainingJobState{
						Phase: tc.phase,
					},
				},
			}
			state := NewState(trainjob)
			assert.IsType(t, tc.expected, state)
		})
	}
}

func TestNewNotebookJobState(t *testing.T) {
	// Test cases for different states of the Notebook
	testCases := []struct {
		name     string
		state    systemv1alpha1.NotebookState
		expected State
	}{
		{"Pending", systemv1alpha1.NotebookStatePending, &pendingState{}},
		{"StartUp", systemv1alpha1.NotebookStateStartUp, &runningState{recordEvent: true}},
		{"Running", systemv1alpha1.NotebookStateRunning, &runningState{}},
		{"Stopping", systemv1alpha1.NotebookStateStopping, &runningState{}},
		{"StartFailed", systemv1alpha1.NotebookStateStartFailed, nil},
		{"Stopped", systemv1alpha1.NotebookStateStopped, nil},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			notebook := &systemv1alpha1.Notebook{
				Status: systemv1alpha1.NotebookStatus{
					State: tc.state,
				},
			}
			state := NewNotebookJobState(notebook)
			assert.IsType(t, tc.expected, state)
		})
	}
}
