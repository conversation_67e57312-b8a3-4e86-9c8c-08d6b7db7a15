package state

import (
	"context"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

type UpdateJobStatusFn func(jobStatus *systemv1alpha1.TrainingJobStatus) (jobPhaseChanged bool)
type UpdateNotebookJobStatusFn func(jobStatus *systemv1alpha1.NotebookStatus) (jobPhaseChanged bool)

type ActionJobFn func(ctx context.Context, job *systemv1alpha1.TrainingJob, fn UpdateJobStatusFn) error
type ActionByVcFn func(ctx context.Context, job *systemv1alpha1.TrainingJob, fn UpdateJobStatusFn) error
type RestartJobFn func(ctx context.Context, job *systemv1alpha1.TrainingJob, fn UpdateJobStatusFn) error

type ActionFn func(ctx context.Context, job *systemv1alpha1.Notebook, fn UpdateNotebookJobStatusFn) error
type ActionVcFn func(ctx context.Context, job *systemv1alpha1.Notebook, fn UpdateNotebookJobStatusFn) error

var (
	SyncJobByVcJob ActionByVcFn
	CreateJob      ActionJobFn
	RestartJob     RestartJobFn

	CreateJobByNotebook ActionFn
	SyncJobByNotebook   ActionVcFn
)

func NewState(trainjob *systemv1alpha1.TrainingJob) State {
	switch trainjob.Status.State.Phase {
	case systemv1alpha1.Pending:
		return &pendingState{
			trainjob: trainjob,
		}
	case systemv1alpha1.Queuing:
		return &runningState{
			trainjob:    trainjob,
			recordEvent: true,
		}
	case systemv1alpha1.Running, systemv1alpha1.Stopping, systemv1alpha1.NodeHealthChecking:
		return &runningState{
			trainjob: trainjob,
		}
	case systemv1alpha1.Completed, systemv1alpha1.Failed, systemv1alpha1.Stopped:
		// ...终态compelted,stopped,failed,只能同步任务详情状态
		return nil
	case systemv1alpha1.Restarting:
		return &restartingState{
			trainjob:    trainjob,
			recordEvent: true,
		}
	default:
		return &pendingState{
			trainjob: trainjob,
		}
	}
}

func NewNotebookJobState(notebookjob *systemv1alpha1.Notebook) State {
	switch notebookjob.Status.State {
	case systemv1alpha1.NotebookStatePending:
		return &pendingState{
			notebookjob: notebookjob,
		}
	case systemv1alpha1.NotebookStateStartUp:
		return &runningState{
			notebookjob: notebookjob,
			recordEvent: true,
		}
	case systemv1alpha1.NotebookStateRunning, systemv1alpha1.NotebookStateStopping:
		return &runningState{
			notebookjob: notebookjob,
		}
	case systemv1alpha1.NotebookStateStartFailed, systemv1alpha1.NotebookStateStopped:
		return nil // ...终态compelted,stopped,failed
	default:
		return &pendingState{
			notebookjob: notebookjob,
		}
	}
}
