package state

import (
	"context"
	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"testing"
)

func TestPendingState_Execute(t *testing.T) {
	// Mock data
	trainjob := &systemv1alpha1.TrainingJob{
		Status: systemv1alpha1.TrainingJobStatus{
			State: systemv1alpha1.TrainingJobState{
				Phase: systemv1alpha1.Pending,
			},
		},
	}

	// Mock implementation for CreateJob
	CreateJob = func(ctx context.Context, job *systemv1alpha1.TrainingJob, fn UpdateJobStatusFn) error {
		if fn != nil {
			fn(&job.Status)
		}
		return nil
	}

	// Create a pendingState
	ps := &pendingState{
		trainjob: trainjob,
	}

	// Execute the state transition
	err := ps.Execute(context.TODO())

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.Queuing, trainjob.Status.State.Phase, "The TrainingJob phase should be updated to Queuing")
}

func TestPendingState_ExecuteNotebook(t *testing.T) {
	// Mock data
	notebook := &systemv1alpha1.Notebook{
		Status: systemv1alpha1.NotebookStatus{
			State: systemv1alpha1.NotebookStatePending,
		},
	}

	// Mock implementation for CreateJobByNotebook
	CreateJobByNotebook = func(ctx context.Context, job *systemv1alpha1.Notebook, fn UpdateNotebookJobStatusFn) error {
		if fn != nil {
			fn(&job.Status)
		}
		return nil
	}

	// Create a pendingState
	ps := &pendingState{
		notebookjob: notebook,
	}

	// Execute the state transition
	err := ps.ExecuteNotebook(context.TODO())

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, systemv1alpha1.NotebookStateStartUp, notebook.Status.State, "The Notebook state should be updated to StartUp")
}
