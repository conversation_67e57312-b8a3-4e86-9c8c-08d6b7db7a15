package state

import (
	"context"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

type restartingState struct {
	trainjob *systemv1alpha1.TrainingJob
	// notebookjob *systemv1alpha1.Notebook
	recordEvent bool
}

func (rs *restartingState) Execute(ctx context.Context) error {
	return RestartJob(ctx, rs.trainjob, func(jobstatus *systemv1alpha1.TrainingJobStatus) bool {
		return rs.recordEvent
	})
}

func (rs *restartingState) ExecuteNotebook(ctx context.Context) error {
	return nil
}
