package job

import (
	"context"
	"github.com/stretchr/testify/assert"
	applicationv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"testing"
	"time"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

func TestInitNotebookjobStatus(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{
		Client: k8sFakeClient,
	}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	notebook, isNoNeedToCreateVc, err := r.initNotebookjobStatus(context.TODO(), notebook)
	assert.NoError(t, err)
	assert.False(t, isNoNeedToCreateVc)
	assert.Equal(t, notebook, notebook)
}

func TestUpdateNotebookjobStatus(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
		Status: applicationv1alpha1.NotebookStatus{
			State: applicationv1alpha1.NotebookStatePending,
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	err = r.UpdateNotebookjobStatus(context.TODO(), notebook)
	assert.NoError(t, err)
}

func TestUpdateNotebookjob(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
		Status: applicationv1alpha1.NotebookStatus{
			State: applicationv1alpha1.NotebookStatePending,
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	err = r.UpdateNotebookjob(context.TODO(), notebook)
	assert.NoError(t, err)
}

func TestGetVcjobByNotebook(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	_, err = r.getVcjobByNotebook(context.TODO(), notebook)
	assert.Error(t, err)
	if !errors.IsNotFound(err) {
		t.Errorf("Expected NotFound error, got: %v", err)
	}
}

func TestNotebookActionSyncNotebookjobPodConditions(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	r.syncNotebookjobPodConditions(context.TODO(), notebook)
}

func TestGetPodGroupByNotebook(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	_, err = r.getPodGroupByNotebook(context.TODO(), notebook)
	assert.Error(t, err)
}

func TestSetMaxRunTime(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
		Spec: applicationv1alpha1.NotebookSpec{
			MaxRunTime: 10,
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	runTime := metav1.Time{Time: time.Now()}
	r.setMaxRunTime(notebook, runTime)
}

func TestCreateVjCommandBusIfNotExist(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	vcjob := &batchvc.Job{}
	err = r.createVjCommandBusIfNotExist(notebook, vcjob)
	assert.NoError(t, err)
}

func TestUpdatePodConds(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
		Status: applicationv1alpha1.NotebookStatus{
			PodDurations: map[string]*applicationv1alpha1.TrainingJobPodDurations{
				"uid1": {
					Name:          "pod1",
					TaskName:      "task1",
					LaunchedTime:  "2023-01-01T00:00:00Z",
					CompletedTime: "2023-01-02T00:00:00Z",
					Phase:         v1.PodRunning,
					NodeName:      "node1",
				},
			},
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	r.updatePodConds(notebook)
	assert.Equal(t, v1.PodSucceeded, notebook.Status.PodDurations["uid1"].Phase)
}

func TestNewCondition(t *testing.T) {
	status := applicationv1alpha1.NotebookStateRunning
	lastTransitionTime := &metav1.Time{Time: time.Now()}
	r := &NotebookReconciler{}
	condition := r.newCondition(status, lastTransitionTime)
	assert.Equal(t, status, condition.Status)
	assert.Equal(t, lastTransitionTime, condition.LastTransitionTime)
}

func TestNotebookActionDeleteNotebook(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = applicationv1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &NotebookReconciler{Client: k8sFakeClient}
	notebook := &applicationv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	err = r.deleteNotebook(context.TODO(), notebook)
	assert.NoError(t, err)
}
