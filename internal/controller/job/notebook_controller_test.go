package job

import (
	"context"
	"hero.ai/hero-controllers/internal/controller/job/state"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

// TestNotebookReconcilerReconcile tests the Reconcile method
func TestNotebookReconcilerReconcile(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	r := &NotebookReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a Notebook object for testing
	notebook := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
		Status: systemv1alpha1.NotebookStatus{
			State: systemv1alpha1.NotebookStatePending,
		},
	}

	// Mock implementation for CreateJobByNotebook
	state.CreateJobByNotebook = func(ctx context.Context, job *systemv1alpha1.Notebook, fn state.UpdateNotebookJobStatusFn) error {
		if fn != nil {
			fn(&job.Status)
		}
		return nil
	}

	// Create the Notebook in the fake client
	err := r.Create(context.TODO(), notebook)
	assert.NoError(t, err)

	// Test Reconcile
	req := ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}

	_, err = r.Reconcile(context.TODO(), req)
	assert.NoError(t, err)
}

// TestDeleteNotebook tests the deleteNotebook method
func TestDeleteNotebook(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	r := &NotebookReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a Notebook object for testing
	notebook := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}

	// Call deleteNotebook function
	err := r.deleteNotebook(context.TODO(), notebook)
	assert.NoError(t, err)

	// Check if the Notebook was deleted
	err = r.Get(context.TODO(), types.NamespacedName{Name: "test-notebook", Namespace: "default"}, notebook)
	assert.Error(t, err)
}

// TestSyncNotebookjobPodConditions tests the syncNotebookjobPodConditions method
func TestSyncNotebookjobPodConditions(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()

	r := &NotebookReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a Notebook object for testing
	notebook := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}

	// Call syncNotebookjobPodConditions function
	r.syncNotebookjobPodConditions(context.TODO(), notebook)

	// Since the function doesn't return anything, check the notebook's status to validate
	assert.NotNil(t, notebook.Status)
}
