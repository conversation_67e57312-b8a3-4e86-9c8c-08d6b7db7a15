package job

import (
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	vjschedpg "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

type enqueueRequestForExtendRes struct {
	labelKey        string
	resourceVersion bool
}

// 转换，需要换成notebook或者trainingjob添加到队列
func (e *enqueueRequestForExtendRes) Create(evt event.CreateEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "CreateEvent received with no metadata", "event", evt)
		return
	}

	if _, found := evt.Object.GetLabels()[e.labelKey]; !found {
		return
	}
	q.Add(reconcile.Request{NamespacedName: types.NamespacedName{
		Name:      evt.Object.GetLabels()[e.labelKey],
		Namespace: evt.Object.GetNamespace(),
	}})
}

// Update implements EventHandler.
func (e *enqueueRequestForExtendRes) Update(evt event.UpdateEvent, q workqueue.RateLimitingInterface) {
	if e.resourceVersion {
		var newpg *vjschedpg.PodGroup
		var oldpg *vjschedpg.PodGroup
		var ok bool
		if newpg, ok = evt.ObjectNew.(*vjschedpg.PodGroup); !ok {
			return
		}
		if oldpg, ok = evt.ObjectOld.(*vjschedpg.PodGroup); !ok {
			return
		}
		if newpg.Status.Phase == vjschedpg.PodGroupRunning && newpg.Status.Phase == oldpg.Status.Phase {
			return
		}
	}
	switch {
	case evt.ObjectNew != nil:
		if _, found := evt.ObjectNew.GetLabels()[e.labelKey]; !found {
			return
		}
		q.Add(reconcile.Request{NamespacedName: types.NamespacedName{
			Name:      evt.ObjectNew.GetLabels()[e.labelKey],
			Namespace: evt.ObjectNew.GetNamespace(),
		}})
	case evt.ObjectOld != nil:
		if _, found := evt.ObjectOld.GetLabels()[e.labelKey]; !found {
			return
		}
		q.Add(reconcile.Request{NamespacedName: types.NamespacedName{
			Name:      evt.ObjectOld.GetLabels()[e.labelKey],
			Namespace: evt.ObjectOld.GetNamespace(),
		}})
	default:
		klog.Error(nil, "UpdateEvent received with no metadata", "event", evt)
	}
}

// Delete implements EventHandler.
func (e *enqueueRequestForExtendRes) Delete(evt event.DeleteEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "DeleteEvent received with no metadata", "event", evt)
		return
	}
	if _, found := evt.Object.GetLabels()[e.labelKey]; !found {
		return
	}
	q.Add(reconcile.Request{NamespacedName: types.NamespacedName{
		Name:      evt.Object.GetLabels()[e.labelKey],
		Namespace: evt.Object.GetNamespace(),
	}})
}

// Generic implements EventHandler.
func (e *enqueueRequestForExtendRes) Generic(evt event.GenericEvent, q workqueue.RateLimitingInterface) {
	if evt.Object == nil {
		klog.Error(nil, "GenericEvent received with no metadata", "event", evt)
		return
	}
	if _, found := evt.Object.GetLabels()[e.labelKey]; !found {
		return
	}
	q.Add(reconcile.Request{NamespacedName: types.NamespacedName{
		Name:      evt.Object.GetLabels()[e.labelKey],
		Namespace: evt.Object.GetNamespace(),
	}})
}
