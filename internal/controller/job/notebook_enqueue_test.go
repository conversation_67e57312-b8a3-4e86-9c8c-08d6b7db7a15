package job

import (
	"testing"

	vjschedpg "volcano.sh/apis/pkg/apis/scheduling/v1beta1"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/util/workqueue"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

const (
	labelKey        = "app"
	resourceVersion = true
)

func TestEnqueueRequestForExtendResCreate(t *testing.T) {
	queue := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	enqueueHandler := &enqueueRequestForExtendRes{
		labelKey:        labelKey,
		resourceVersion: resourceVersion,
	}

	// Test case where object has the required label
	obj := &vjschedpg.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-podgroup",
			Namespace: "default",
			Labels: map[string]string{
				labelKey: "test-label-value",
			},
		},
	}
	createEvent := event.CreateEvent{
		Object: obj,
	}
	enqueueHandler.Create(createEvent, queue)

	// Assert the queue length
	assert.Equal(t, 1, queue.Len())

	// Assert the request added to the queue
	req, _ := queue.Get()
	reconcileReq, ok := req.(reconcile.Request)
	assert.True(t, ok)
	assert.Equal(t, "test-label-value", reconcileReq.Name)
	assert.Equal(t, "default", reconcileReq.Namespace)

	// Test case where object does not have the required label
	objWithoutLabel := &vjschedpg.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-podgroup-no-label",
			Namespace: "default",
		},
	}
	createEventWithoutLabel := event.CreateEvent{
		Object: objWithoutLabel,
	}
	enqueueHandler.Create(createEventWithoutLabel, queue)

	// Assert the queue length remains unchanged
	assert.Equal(t, 0, queue.Len())
}

func TestEnqueueRequestForExtendResUpdate(t *testing.T) {
	queue := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	enqueueHandler := &enqueueRequestForExtendRes{
		labelKey:        labelKey,
		resourceVersion: resourceVersion,
	}

	// Test case where new object has the required label
	newObj := &vjschedpg.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "new-podgroup",
			Namespace: "default",
			Labels: map[string]string{
				labelKey: "test-new-label",
			},
		},
		Status: vjschedpg.PodGroupStatus{
			Phase: vjschedpg.PodGroupPending,
		},
	}
	oldObj := &vjschedpg.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "old-podgroup",
			Namespace: "default",
			Labels: map[string]string{
				labelKey: "test-old-label",
			},
		},
		Status: vjschedpg.PodGroupStatus{
			Phase: vjschedpg.PodGroupRunning,
		},
	}
	updateEvent := event.UpdateEvent{
		ObjectNew: newObj,
		ObjectOld: oldObj,
	}
	enqueueHandler.Update(updateEvent, queue)

	// Assert the queue length
	assert.Equal(t, 1, queue.Len())

	// Assert the request added to the queue
	req, _ := queue.Get()
	reconcileReq, ok := req.(reconcile.Request)
	assert.True(t, ok)
	assert.Equal(t, "test-new-label", reconcileReq.Name)
	assert.Equal(t, "default", reconcileReq.Namespace)

	// Test case where new object does not have the required label
	newObjNoLabel := &vjschedpg.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "new-podgroup-no-label",
			Namespace: "default",
		},
	}
	updateEventNoLabel := event.UpdateEvent{
		ObjectNew: newObjNoLabel,
		ObjectOld: oldObj,
	}
	enqueueHandler.Update(updateEventNoLabel, queue)

	// Assert the queue length remains unchanged
	assert.Equal(t, 0, queue.Len())
}

func TestEnqueueRequestForExtendResDelete(t *testing.T) {
	queue := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	enqueueHandler := &enqueueRequestForExtendRes{
		labelKey:        labelKey,
		resourceVersion: resourceVersion,
	}

	// Test case where object has the required label
	obj := &vjschedpg.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "delete-podgroup",
			Namespace: "default",
			Labels: map[string]string{
				labelKey: "test-delete-label",
			},
		},
	}
	deleteEvent := event.DeleteEvent{
		Object: obj,
	}
	enqueueHandler.Delete(deleteEvent, queue)

	// Assert the queue length
	assert.Equal(t, 1, queue.Len())

	// Assert the request added to the queue
	req, _ := queue.Get()
	reconcileReq, ok := req.(reconcile.Request)
	assert.True(t, ok)
	assert.Equal(t, "test-delete-label", reconcileReq.Name)
	assert.Equal(t, "default", reconcileReq.Namespace)
}

func TestEnqueueRequestForExtendResGeneric(t *testing.T) {
	queue := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	enqueueHandler := &enqueueRequestForExtendRes{
		labelKey:        labelKey,
		resourceVersion: resourceVersion,
	}

	// Test case where object has the required label
	obj := &vjschedpg.PodGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "generic-podgroup",
			Namespace: "default",
			Labels: map[string]string{
				labelKey: "test-generic-label",
			},
		},
	}
	genericEvent := event.GenericEvent{
		Object: obj,
	}
	enqueueHandler.Generic(genericEvent, queue)

	// Assert the queue length
	assert.Equal(t, 1, queue.Len())

	// Assert the request added to the queue
	req, _ := queue.Get()
	reconcileReq, ok := req.(reconcile.Request)
	assert.True(t, ok)
	assert.Equal(t, "test-generic-label", reconcileReq.Name)
	assert.Equal(t, "default", reconcileReq.Namespace)
}

func TestConstants(t *testing.T) {
	assert.Equal(t, labelKey, "app")
	assert.Equal(t, resourceVersion, true)
}
