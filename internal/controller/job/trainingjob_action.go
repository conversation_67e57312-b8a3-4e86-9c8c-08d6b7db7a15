//nolint:all
package job

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/job/plugins"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/state"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"

	"k8s.io/client-go/util/retry"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	commandvc "volcano.sh/apis/pkg/apis/bus/v1alpha1"
	"volcano.sh/apis/pkg/apis/helpers"
	vjschedpg "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

var (
	nodeAnnotation          = "nodes.system.hero.ai"
	stopAnnotation          = "command.system.hero.ai"
	stopAction              = "stop"
	vcjobPodJobNameLabels   = "volcano.sh/job-name"
	vcjobPodTaskNameLabels  = "volcano.sh/task-spec"
	vcjobPodNamespaceLabels = "volcano.sh/job-namespace"
	secretName              = "image-secret"
	Finalizer               = "system.hero.ai/finalizer"
	restartingJobsMutex     sync.Mutex
	mountPath               = "/hero-app/config"
)

type restartingJob struct {
	Namespace   string
	Name        string
	StoppedTime *metav1.Time
	Delay       int32
	Version     int32
	StopChan    chan struct{}
}

func (r *TrainingJobReconciler) syncJobByVcjob(ctx context.Context, trainjob *systemv1alpha1.TrainingJob, updateStatus state.UpdateJobStatusFn) error {
	// 根据vj的状态来更新trainjob状态，上报事件
	oldState := trainjob.Status.State.Phase
	vj, err := r.getVcjobByTrainjob(ctx, trainjob)
	if err != nil {
		// TODO: 状态修改挪到下面统一处理的地方，前面只管同步状态、上报事件等
		action := trainjob.Annotations[stopAnnotation]
		if (apierrors.IsNotFound(err) && trainjob.Status.State.Phase == systemv1alpha1.Stopping) ||
			(apierrors.IsNotFound(err) && action == stopAction && trainjob.Status.State.Phase == systemv1alpha1.Restarting) {

			r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventStopped, systemv1alpha1.EventStoppedMessage)
			trainjob.Status.State.Phase = systemv1alpha1.Stopped
			trainjob.Status.StoppedTime = metav1.Now().String()
			r.updatePodConds(trainjob)
			lastTransitionTime := metav1.Now()
			trainjobCondition := r.newCondition(trainjob.Status.State.Phase, &lastTransitionTime)
			trainjob.Status.Conditions = append(trainjob.Status.Conditions, trainjobCondition)
			return r.UpdateTrainjobStatus(ctx, trainjob)
		}
		if apierrors.IsNotFound(err) {
			return nil
		}
		return err
	}

	// scheduler event上报
	if updateStatus(&trainjob.Status) {
		pg, err := r.getPodGroupByTrainjob(ctx, trainjob)
		if err != nil {
			return err
		}

		for _, condition := range pg.Status.Conditions {
			if condition.Type == vjschedpg.PodGroupUnschedulableType {
				r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeWarning, systemv1alpha1.EventScheduledFailed, condition.Message)
			} else if condition.Type == vjschedpg.PodGroupScheduled {
				r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventScheduledSuccess, systemv1alpha1.EventScheduledSuccessMessage)
				if err := updateJobAnnos(ctx, vj, trainjob, r.Client, func() error {
					return r.UpdateTrainjob(ctx, trainjob)
				}); err != nil {
					return err
				}
			}
		}
	}

	_, ok := trainjob.Annotations[stopAnnotation]
	if ok && trainjob.Status.State.Phase != systemv1alpha1.Stopping && trainjob.Annotations[stopAnnotation] == stopAction {
		return r.stopJob(ctx, trainjob, vj)
	}

	pods, err := getPodListByVcjob(ctx, vj, r.Client)
	if err != nil {
		return err
	}
	r.checkFailedPodStatus(ctx, trainjob, vj, pods.Items)

	if trainjob.Status.State.Phase == systemv1alpha1.Restarting {
		// 状态更新逻辑在 r.restartJob() 中
	} else if trainjob.Status.State.Phase == systemv1alpha1.Stopping {
		if r.hasImagePullFailedPod(trainjob) {
			// 临时方案
			trainjob.Status.State.Phase = systemv1alpha1.Failed
			trainjob.Status.CompletionTime = metav1.Now().String()
		} else if IsVolcanoJobFinished(vj) {
			trainjob.Status.State.Phase = systemv1alpha1.Stopped
			trainjob.Status.StoppedTime = metav1.Now().String()
			r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventStopped, systemv1alpha1.EventStoppedMessage)
		}
	} else {
		switch vj.Status.State.Phase {
		case batchvc.Pending:
			trainjob.Status.State.Phase = systemv1alpha1.Queuing
			ok, reason, message := r.checkImagePullStatus(ctx, trainjob, pods.Items)
			if !ok {
				trainjob.Status.State.Reason = reason
				trainjob.Status.State.Message = message
				klog.Infof("trainingjob %s/%s, vcjob %s %s, image pull failed %s: %s",
					trainjob.Namespace, trainjob.Name, vj.Name, vj.Status.State.Phase, reason, message)
				return r.stopJob(ctx, trainjob, vj)
			} else if r.shouldBeNodeHealthChecking(trainjob, vj, pods.Items) {
				trainjob.Status.State.Phase = systemv1alpha1.NodeHealthChecking
				if trainjob.Status.State.Phase != oldState {
					r.EventRecord.Eventf(trainjob, v1.EventTypeNormal, systemv1alpha1.EventNodeHealthCheckingBeforeStart,
						systemv1alpha1.EventNodeHealthCheckingBeforeStartMessage, trainjob.Status.Version)
				}
			}
		case batchvc.Running:
			trainjob.Status.State.Phase = systemv1alpha1.Running
			trainjob.Status.StartTime = vj.Status.State.LastTransitionTime.String()
			if trainjob.Status.State.Phase != oldState {
				r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventRunningSuccess, systemv1alpha1.EventRunningSuccessMessage)
			}
		case batchvc.Completed, batchvc.Terminated:
			trainjob.Status.State.Phase = systemv1alpha1.Completed
			trainjob.Status.CompletionTime = metav1.Now().String()
			r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventCompeleted, systemv1alpha1.EventCompeletedMessage)
		case batchvc.Failed:
			trainjob.Status.State.Phase = systemv1alpha1.Failed
			trainjob.Status.CompletionTime = metav1.Now().String()
			r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeWarning, systemv1alpha1.EventRunFailed,
				fmt.Sprintf(systemv1alpha1.EventRunFailedMessage, vj.Status.State.Reason))
		case batchvc.Completing, batchvc.Terminating, batchvc.Aborting:
			trainjob.Status.State.Phase = systemv1alpha1.Stopping
		case batchvc.Aborted:
			trainjob.Status.State.Phase = systemv1alpha1.Stopped
			trainjob.Status.StoppedTime = metav1.Now().String()
			// 临时方案
			if r.hasImagePullFailedPod(trainjob) {
				trainjob.Status.State.Phase = systemv1alpha1.Failed
				trainjob.Status.CompletionTime = metav1.Now().String()
				trainjob.Status.StoppedTime = ""
			}
		case batchvc.Restarting:
			klog.Warningf("trainingjob %s/%s, vcjob %s status %s",
				trainjob.Namespace, trainjob.Name, vj.Name, vj.Status.State.Phase)
		default:
			klog.Errorf("trainingjob %s/%s, vcjob %s status %s is not expected",
				trainjob.Namespace, trainjob.Name, vj.Name, vj.Status.State.Phase)
		}
	}

	// 任务健康状态更新与是否重启检查
	r.faultToleranceSinceVcJobCreated(trainjob, vj, pods.Items)

	if trainjob.Status.State.Phase != oldState {
		lastTransitionTime := metav1.Now()
		trainjobCondition := r.newCondition(trainjob.Status.State.Phase, &lastTransitionTime)
		trainjob.Status.Conditions = append(trainjob.Status.Conditions, trainjobCondition)
	}

	err = r.UpdateTrainjobStatus(ctx, trainjob)
	if err != nil {
		klog.Errorf("update trainingjob %s err: %s", trainjob.Name, err.Error())
		return err
	}

	return nil
}

// Pending----->Queuing
func (r *TrainingJobReconciler) createVcJob(ctx context.Context, trainjob *systemv1alpha1.TrainingJob, updateStatus state.UpdateJobStatusFn) error {
	_, ok := trainjob.Annotations[stopAnnotation]
	if ok && trainjob.Annotations[stopAnnotation] == stopAction {
		trainjob.Status.State.Phase = systemv1alpha1.Stopping
		r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventStopping, systemv1alpha1.EventStoppingMessage)
		return r.UpdateTrainjobStatus(ctx, trainjob)
	}
	trainjob, isNoNeedToCreateVc, err := r.initTrainjobStatus(ctx, trainjob)
	if err != nil {
		return err
	}
	if isNoNeedToCreateVc {
		return nil
	}

	r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventStarting, systemv1alpha1.EventStartingMessage)

	// create private image secret
	var secretName = secretName
	if len(trainjob.Spec.ImageSecret.Username) != 0 {
		secretName, err = CreateRegistrySecret(trainjob.Namespace, trainjob.Spec.ImageSecret.Username, trainjob.Spec.ImageSecret.Password,
			trainjob.Spec.ImageUrl, r.Client, trainjob, r.Scheme)
		if err != nil {
			return err
		}
	}

	vcjob := vcjobbuilder.NewVcJobBuilder().Build(trainjob, r.Scheme, secretName)
	err = r.pluginOnVolcanojobCreate(trainjob, vcjob)
	if err != nil {
		return err
	}

	if err = r.preResourceApply(trainjob, vcjob); err != nil {
		return err
	}

	// 任务诊断与容错在作业创建阶段的配置
	// 比如：initContainer 进行环境诊断
	if err = r.faultToleranceOnJobCreate(trainjob, vcjob); err != nil {
		return err
	}

	err = r.Create(ctx, vcjob)
	if err != nil {
		r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeWarning, systemv1alpha1.EventCreateVolcanoFailed, fmt.Sprintf(systemv1alpha1.EventCreateVolcanoFailedMessage, err.Error()))
		klog.Errorf("trainjob %s create vcjob %v err: %s", trainjob.Name, *vcjob, err.Error())
		return err
	}

	r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventCreateVolcanoJobSuccess, systemv1alpha1.EventCreateVolcanoJobSuccessMessage)
	// 更新condition
	if updateStatus != nil {
		if updateStatus(&trainjob.Status) {
			lastTransitionTime := metav1.Now()
			trainjobCondition := r.newCondition(trainjob.Status.State.Phase, &lastTransitionTime)
			trainjob.Status.Conditions = append(trainjob.Status.Conditions, trainjobCondition)
			trainjob.Status.State.LastTransitionTime = metav1.Now().String()
		}

		err := r.UpdateTrainjobStatus(ctx, trainjob)
		if err != nil {
			klog.Errorf("update trainingjob %s err: %s", trainjob.Name, err.Error())
			return err
		}
	}

	r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventWaitingScheduler, systemv1alpha1.EventWaitingSchedulerMessage)
	return nil
}

func (r *TrainingJobReconciler) UpdateTrainjob(ctx context.Context, trainjob *systemv1alpha1.TrainingJob) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		trainingjob := &systemv1alpha1.TrainingJob{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: trainjob.Name, Namespace: trainjob.Namespace}, trainingjob); err != nil {
			return
		}

		annotations := trainingjob.GetAnnotations()
		if annotations == nil {
			annotations = make(map[string]string)
		}
		annotations[nodeAnnotation] = trainjob.Annotations[nodeAnnotation]
		trainingjob.SetAnnotations(annotations)
		return r.Update(ctx, trainingjob)
	})
}

func (r *TrainingJobReconciler) UpdateTrainjobStatus(ctx context.Context, trainjob *systemv1alpha1.TrainingJob) error {
	r.dropPreviousVersionedStatus(trainjob)

	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		trainingjob := &systemv1alpha1.TrainingJob{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: trainjob.Name, Namespace: trainjob.Namespace}, trainingjob); err != nil {
			return
		}

		trainingjob.Status = trainjob.Status
		return r.Status().Update(ctx, trainingjob)
	})
}

func (r *TrainingJobReconciler) UpdateTrainjobPodStatus(ctx context.Context, trainjob *systemv1alpha1.TrainingJob) error {
	r.dropPreviousVersionedStatus(trainjob)

	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		trainingjob := &systemv1alpha1.TrainingJob{}
		if err = r.Client.Get(ctx, client.ObjectKey{Name: trainjob.Name, Namespace: trainjob.Namespace}, trainingjob); err != nil {
			return
		}

		trainingjob.Status.PodDurations = trainjob.Status.PodDurations
		return r.Status().Update(ctx, trainingjob)
	})
}

func (r *TrainingJobReconciler) initTrainjobStatus(ctx context.Context, newTrainjob *systemv1alpha1.TrainingJob) (*systemv1alpha1.TrainingJob, bool, error) {
	var isNoNeedToCreateVc bool
	if newTrainjob.Status.State.Phase == systemv1alpha1.Pending {
		var volcanoJob = batchvc.Job{}
		err := r.Get(ctx, types.NamespacedName{
			Namespace: newTrainjob.Namespace,
			Name:      vcjobbuilder.VcjobNameTrainjobPrefix + newTrainjob.Name,
		}, &volcanoJob)
		if !apierrors.IsNotFound(err) {
			isNoNeedToCreateVc = true
		}

		return newTrainjob, isNoNeedToCreateVc, nil

	}
	if newTrainjob.Status.State.Phase != "" {
		isNoNeedToCreateVc = true
		return newTrainjob, isNoNeedToCreateVc, nil
	}

	newTrainjob.Status.State.Phase = systemv1alpha1.Pending
	if len(newTrainjob.Status.CreateTime) == 0 {
		newTrainjob.Status.CreateTime = metav1.Now().String()
	}
	lastTransitionTime := metav1.Now()
	trainjobCondition := r.newCondition(newTrainjob.Status.State.Phase, &lastTransitionTime)
	newTrainjob.Status.Conditions = append(newTrainjob.Status.Conditions, trainjobCondition)
	err := r.UpdateTrainjobStatus(ctx, newTrainjob)
	if err != nil {
		klog.Errorf("update trainingjob %s err: %s", newTrainjob.Name, err.Error())
		return nil, isNoNeedToCreateVc, err
	}

	return newTrainjob, isNoNeedToCreateVc, nil
}

func (r *TrainingJobReconciler) UpdatePod(ctx context.Context, pods *v1.Pod) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		// 获取最新的 Pod 对象
		var newPod = &v1.Pod{}
		if err := r.Get(ctx, client.ObjectKeyFromObject(pods), newPod); err != nil {
			return err
		}

		patch := client.MergeFrom(newPod.DeepCopy())

		if !pods.DeletionTimestamp.IsZero() {
			if controllerutil.ContainsFinalizer(pods, Finalizer) {
				controllerutil.RemoveFinalizer(pods, Finalizer)
			}
		} else {
			if !controllerutil.ContainsFinalizer(pods, Finalizer) {
				controllerutil.AddFinalizer(pods, Finalizer)
			}
		}

		// 提交更新的 patch
		return r.Patch(ctx, pods, patch)
	})
}

func deleteSharedFile(filePath string) error {
	err := os.Remove(filePath)
	if err != nil && !os.IsNotExist(err) {
		return err
	}
	return nil
}

func (r *TrainingJobReconciler) syncTrainjobPodConditions(ctx context.Context, trainjob *systemv1alpha1.TrainingJob) {
	if trainjob.Status.State.Phase == systemv1alpha1.Pending || trainjob.Status.State.Phase == "" {
		return
	}

	var vcCompelted bool
	vj, err := r.getVcjobByTrainjob(ctx, trainjob)
	if err != nil && !apierrors.IsNotFound(err) {
		klog.Errorf("get vcjob by trainingjob %s err: %s", trainjob.Name, err.Error())
	} else {
		// 删除volcanojob
		err = DeleteVolcanoJob(ctx, r.Client, vcjobbuilder.VcjobNameTrainjobPrefix, trainjob, vj)
		if err != nil && !apierrors.IsNotFound(err) {
			klog.Errorf("delete vcjob by trainingjob %s err: %s", trainjob.Name, err.Error())
		}
		for _, policy := range vj.Spec.Policies {
			if policy.Action == commandvc.CompleteJobAction {
				vcCompelted = true
				continue
			}
		}
		//删除分布式作业配置文件
		if getReplicasCount(trainjob) > 1 && !vj.DeletionTimestamp.IsZero() {
			deleteSharedFile(filepath.Join(mountPath, trainjob.Name))
			deleteSharedFile(filepath.Join(mountPath, trainjob.Name+".lock"))
		}
	}

	if getReplicasCount(trainjob) > 1 {
		if trainjob.Status.State.Phase == systemv1alpha1.Completed ||
			trainjob.Status.State.Phase == systemv1alpha1.Running ||
			trainjob.Status.State.Phase == systemv1alpha1.Stopped ||
			trainjob.Status.State.Phase == systemv1alpha1.Failed {
			deleteSharedFile(filepath.Join(mountPath, trainjob.Name))
			deleteSharedFile(filepath.Join(mountPath, trainjob.Name+".lock"))
		}
	}

	if trainjob.Status.PodDurations == nil {
		trainjob.Status.PodDurations = make(systemv1alpha1.TrainingJobDurations)
	}

	for _, task := range trainjob.Spec.Tasks {
		var podList v1.PodList
		err := r.Client.List(ctx, &podList, &client.ListOptions{
			LabelSelector: labels.SelectorFromSet(labels.Set{
				systemv1alpha1.VcjobNameLabels: trainjob.Name,
				systemv1alpha1.VcjobNsLabels:   trainjob.Namespace,
				vcjobPodTaskNameLabels:         task.Name,
			}),
		})
		if err != nil {
			klog.Errorf("get podlist by vcjob %s err: %s", trainjob.Name, err.Error())
			return
		}

		if len(podList.Items) == 0 {
			if !trainjob.GetDeletionTimestamp().IsZero() {
				if err := RemoveFinalizer(ctx, r.Client, trainjob); err != nil {
					klog.Errorf("update trainingjob %s err: %s", trainjob.GetName(), err.Error())
					return
				}
			}
		}

		for _, pods := range podList.Items {
			var launchedTime metav1.Time
			var startTime string
			var completedTime string
			switch pods.Status.Phase {
			case v1.PodRunning:
				launchedTime = getPodRunningTime(&pods)
			case v1.PodSucceeded, v1.PodFailed:
				if launchedTime.IsZero() {
					launchedTime = getPodRunningTime(&pods)
				}
			}
			if !launchedTime.IsZero() {
				startTime = launchedTime.String()
			}

			if err := r.UpdatePod(ctx, &pods); err != nil {
				klog.Errorf("update pod failed: %s", err)
			}

			compTime, phase := getPodComplateTime(&pods, vcCompelted)
			if !compTime.IsZero() {
				completedTime = compTime.String()
				pods.Status.Phase = phase
			}

			if compTime.IsZero() && pods.DeletionTimestamp != nil {
				completedTime = pods.DeletionTimestamp.Rfc3339Copy().String()
				pods.Status.Phase = getPhase(vcCompelted)
				if compTime.IsZero() {
					compTime = metav1.Now()
					completedTime = compTime.String()
				}
			}

			anno := trainjob.GetAnnotations()
			if _, found := anno[stopAnnotation]; found {
				if pods.Status.Phase == v1.PodPending {
					pods.Status.Phase = getPhase(vcCompelted)
					if compTime.IsZero() {
						compTime = metav1.Now()
						completedTime = compTime.String()
					}
				}
			}

			trainingJobPodDurations := &systemv1alpha1.TrainingJobPodDurations{}
			if _, ok := trainjob.Status.PodDurations[string(pods.UID)]; !ok {
				trainingJobPodDurations.Name = pods.Name
				trainingJobPodDurations.TaskName = task.Name
				trainingJobPodDurations.LaunchedTime = startTime
				trainingJobPodDurations.CompletedTime = completedTime
				trainingJobPodDurations.Phase = pods.Status.Phase
				trainingJobPodDurations.NodeName = pods.Spec.NodeName
			} else {
				if len(trainjob.Status.PodDurations[string(pods.UID)].CompletedTime) > 0 {
					completedTime = trainjob.Status.PodDurations[string(pods.UID)].CompletedTime
				}
				if len(trainjob.Status.PodDurations[string(pods.UID)].LaunchedTime) > 0 {
					startTime = trainjob.Status.PodDurations[string(pods.UID)].LaunchedTime
				}
				// 终态pod不允许再次更新
				if trainjob.Status.PodDurations[string(pods.UID)].Phase == v1.PodSucceeded || trainjob.Status.PodDurations[string(pods.UID)].Phase == v1.PodPhase("Terminated") {
					pods.Status.Phase = trainjob.Status.PodDurations[string(pods.UID)].Phase
				}
				trainingJobPodDurations.Name = pods.Name
				trainingJobPodDurations.LaunchedTime = startTime
				trainingJobPodDurations.CompletedTime = completedTime
				trainingJobPodDurations.Phase = pods.Status.Phase
				trainingJobPodDurations.TaskName = task.Name
				trainingJobPodDurations.NodeName = pods.Spec.NodeName
				trainingJobPodDurations.Reason = trainjob.Status.PodDurations[string(pods.UID)].Reason
				trainingJobPodDurations.Message = trainjob.Status.PodDurations[string(pods.UID)].Message
			}

			if version, ok := pods.Labels[vcjobbuilder.TrainingJobVersionLabel]; ok {
				v, _ := strconv.Atoi(version)
				trainingJobPodDurations.Version = int32(v)
			} else {
				trainingJobPodDurations.Version = 0
			}

			// 启用web-terminal插件时，操作
			if vcjobbuilder.ContainsStr(trainjob.Spec.Plugins, "web-terminal") {
				podsNameSplit := strings.Split(pods.Name, "-")
				podSuffix := podsNameSplit[2] + "-" + podsNameSplit[3]
				jobId := podsNameSplit[1]
				patch := []byte(fmt.Sprintf(`{"metadata":{"labels":{"podSuffixName": "%s"}}}`, podSuffix))
				_ = r.Patch(ctx, &v1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: trainjob.Namespace,
						Name:      pods.Name,
					},
				}, client.RawPatch(types.StrategicMergePatchType, patch))
				terminalUrl := r.IngressConfig.Domain + "/" + "wb" + "/" + jobId + "/" + podSuffix + "/"
				trainingJobPodDurations.WebTerminalUrl = terminalUrl
			}

			trainjob.Status.PodDurations[string(pods.UID)] = trainingJobPodDurations
		}
	}

	err = r.UpdateTrainjobPodStatus(ctx, trainjob)
	if err != nil {
		klog.Errorf("update trainingjob %s err: %s", trainjob.Name, err.Error())
		return
	}

}

func (r *TrainingJobReconciler) getVcjobByTrainjob(ctx context.Context, trainjob *systemv1alpha1.TrainingJob) (*batchvc.Job, error) {
	vj := &batchvc.Job{}
	err := r.Client.Get(ctx, types.NamespacedName{
		Namespace: trainjob.Namespace,
		Name:      vcjobbuilder.VcjobNameTrainjobPrefix + trainjob.Name,
	}, vj)

	if err != nil {
		// not found-->use trainingjob
		if apierrors.IsNotFound(err) {
			err = r.Client.Get(ctx, types.NamespacedName{
				Namespace: trainjob.Namespace,
				Name:      "trainingjob-" + trainjob.Name,
			}, vj)
			if err != nil {
				return vj, err
			}
		}
		return vj, err
	}

	return vj, nil
}

func (r *TrainingJobReconciler) getPodGroupByTrainjob(ctx context.Context, trainjob *systemv1alpha1.TrainingJob) (*vjschedpg.PodGroup, error) {
	pgs := vjschedpg.PodGroupList{}
	pg := vjschedpg.PodGroup{}
	err := r.Client.List(ctx, &pgs, &client.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set{
			systemv1alpha1.VcjobNameLabels: trainjob.Name,
			systemv1alpha1.VcjobNsLabels:   trainjob.Namespace,
		}),
	})

	if err != nil || len(pgs.Items) == 0 {
		return &pg, err
	}

	pg = pgs.Items[0]

	return &pg, nil
}

func (r *TrainingJobReconciler) pluginOnVolcanojobCreate(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	client := pluginsinterface.PluginClientset{
		KubeClients:   r.Client,
		IsMultiApp:    false,
		IngressConfig: r.IngressConfig,
	}
	for _, name := range trainjob.Spec.Plugins {
		pb, found := plugins.GetPluginBuilder(name)
		if !found {
			return fmt.Errorf("failed to get plugin %s", name)
		}

		klog.Infof("Starting to execute plugin at <pluginOnVolcanojobCreate>: %s on job: <%s/%s>", name, vcjob.Namespace, vcjob.Name)
		if err := pb(&client).CreateVolcanojobPlugins(trainjob, vcjob); err != nil {
			if apierrors.IsAlreadyExists(err) {
				klog.Infof("plugin %s is already exists while trainingjob %s/%s creating", name, trainjob.Namespace, trainjob.Name)
			} else {
				klog.Errorf("Failed to process on volcanojob create plugin %s, err %v.", name, err)
				return err
			}
		}
	}

	if client.IsMultiApp {
		vcjobbuilder.BuildVcjobWebterminal(vcjob)
	}

	return nil
}

func (r *TrainingJobReconciler) abortVcJob(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	var bus commandvc.Command
	err := r.Get(context.TODO(), types.NamespacedName{
		Namespace: trainjob.Namespace,
		Name:      trainjob.Name,
	}, &bus)
	if !apierrors.IsNotFound(err) {
		return nil
	}

	if vcjob == nil {
		vcjob = &batchvc.Job{}
		err = r.Get(context.TODO(), types.NamespacedName{
			Name:      vcjobbuilder.VcjobNameTrainjobPrefix + trainjob.Name,
			Namespace: trainjob.Namespace,
		}, vcjob)
		if err != nil {
			if apierrors.IsNotFound(err) {
				return nil
			}
			return err
		}
		if IsVolcanoJobFinished(vcjob) {
			return nil
		}
	}

	bus = commandvc.Command{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Command",
			APIVersion: "bus.volcano.sh/v1alpha1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      trainjob.Name,
			Namespace: trainjob.Namespace,
		},
		Action:       string(commandvc.AbortJobAction),
		TargetObject: metav1.NewControllerRef(vcjob, helpers.JobKind),
	}

	if err := controllerutil.SetControllerReference(trainjob, &bus, r.Scheme); err != nil {
		return err
	}
	klog.Infof("Aborting vcjob %s/%s of trainingjob %s of version %d",
		vcjob.Namespace, vcjob.Name, trainjob.Name, trainjob.Status.Version)
	return r.Create(context.TODO(), &bus)
}

func (r *TrainingJobReconciler) updatePodConds(trainjob *systemv1alpha1.TrainingJob) {
	for _, v := range trainjob.Status.PodDurations {
		if v.Phase == v1.PodSucceeded || v.Phase == v1.PodFailed {
			continue
		}
		v.Phase = v1.PodSucceeded
	}
}

func (r *TrainingJobReconciler) newCondition(status systemv1alpha1.TrainingJobPhase, lastTransitionTime *metav1.Time) systemv1alpha1.TrainingJobCondition {
	return systemv1alpha1.TrainingJobCondition{
		Status:             status,
		LastTransitionTime: lastTransitionTime,
	}
}

func (r *TrainingJobReconciler) preResourceApply(tj *systemv1alpha1.TrainingJob, vj *batchvc.Job) error {
	// 使用华为昇腾 NPU 时，需要先创建 hccl configmap 挂载到 pod 中
	if npu, ok := vj.Labels[vcjobbuilder.AscendRingControllerAtlasLabelKey]; ok {
		hcclCm := vcjobbuilder.BuildAscendNPUHCCLConfigMap(vj.Name, vj.Namespace, npu)
		if err := controllerutil.SetControllerReference(tj, hcclCm, r.Scheme); err != nil {
			return err
		}
		if err := r.Create(context.TODO(), hcclCm); err != nil && !apierrors.IsAlreadyExists(err) {
			return err
		}
	}

	return nil
}

func (r *TrainingJobReconciler) faultToleranceOnJobCreate(tj *systemv1alpha1.TrainingJob, vj *batchvc.Job) error {
	if tj.Spec.FaultTolerance == nil {
		return nil
	}
	for _, checker := range r.healthCheckers {
		checker.CheckOnJobCreate(tj, vj)
	}
	return nil
}

func (r *TrainingJobReconciler) faultToleranceSinceVcJobCreated(tj *systemv1alpha1.TrainingJob, vj *batchvc.Job, pods []v1.Pod) {
	if tj.Spec.FaultTolerance == nil ||
		len(tj.Spec.FaultTolerance.HealthChecks) == 0 {
		return
	}
	r.initFaultToleranceStatus(tj, vj)

	if action, ok := tj.Annotations[stopAnnotation]; ok && action == stopAction {
		for _, checker := range r.healthCheckers {
			checker.StopAsyncCheck(tj, vj, pods)
		}
		return
	}

	// 启动异步诊断项
	for _, checker := range r.healthCheckers {
		checker.AsyncCheck(tj, vj, pods)
	}

	// 进行同步诊断项
	for _, checker := range r.healthCheckers {
		needRestart := checker.Check(tj, vj, pods)
		if needRestart {
			break
		}
	}

	// 判断是否需要重启，更新诊断结果和状态
	if tj.Status.State.Phase != systemv1alpha1.Restarting {
		restartReasons := r.checkTrainingJobRestartable(tj, vj, pods)
		if len(restartReasons) > 0 {
			r.EventRecord.Eventf(tj, v1.EventTypeNormal, systemv1alpha1.EventRestarting,
				systemv1alpha1.EventRestartingMessage, tj.Status.Version, restartReasons)

			version := strconv.Itoa(int(tj.Status.Version))
			ftStatus := tj.Status.FaultTolerance[version]
			ftStatus.RestartReasons = restartReasons
			tj.Status.FaultTolerance[version] = ftStatus

			tj.Status.State.Phase = systemv1alpha1.Restarting
			tj.Status.CompletionTime = ""
			tj.Status.StoppedTime = ""
		} else {
			if tj.Spec.FaultTolerance != nil &&
				tj.Spec.FaultTolerance.RestartPolicy != nil &&
				len(tj.Spec.FaultTolerance.RestartPolicy.Policies) != 0 &&
				(vj.Status.State.Phase == batchvc.Failed || vj.Status.State.Phase == batchvc.Aborted) {
				r.EventRecord.Eventf(tj, v1.EventTypeNormal, systemv1alpha1.EventUnrestartable,
					systemv1alpha1.EventUnrestartableMessage, tj.Status.Version)
			}
		}
	}

	// 如果 volcanojob 已停止，停止异步的健康检查
	if IsVolcanoJobFinished(vj) {
		for _, checker := range r.healthCheckers {
			checker.StopAsyncCheck(tj, vj, pods)
		}
	}
}

func (r *TrainingJobReconciler) initFaultToleranceStatus(tj *systemv1alpha1.TrainingJob, vj *batchvc.Job) {
	if tj.Status.FaultTolerance == nil {
		tj.Status.FaultTolerance = map[string]systemv1alpha1.FaultToleranceStatus{}
	}
	version := strconv.Itoa(int(tj.Status.Version))
	ftStatus, ok := tj.Status.FaultTolerance[version]
	if !ok {
		ftStatus = systemv1alpha1.FaultToleranceStatus{}
	}
	if ftStatus.CreateTime == nil {
		ftStatus.CreateTime = &vj.CreationTimestamp
	}
	if ftStatus.StartTime == nil {
		for _, condition := range vj.Status.Conditions {
			if condition.Status == batchvc.Running {
				ftStatus.StartTime = condition.LastTransitionTime
				break
			}
		}
	}
	if ftStatus.CompletionTime == nil {
		for _, condition := range vj.Status.Conditions {
			if condition.Status == batchvc.Failed || condition.Status == batchvc.Aborted ||
				condition.Status == batchvc.Completed || condition.Status == batchvc.Terminated {
				ftStatus.CompletionTime = condition.LastTransitionTime
				break
			}
		}
	}
	if tj.Status.State.Phase == systemv1alpha1.NodeHealthChecking {
		ftStatus.Phase = systemv1alpha1.NodeHealthChecking
	} else {
		switch vj.Status.State.Phase {
		case batchvc.Pending:
			ftStatus.Phase = systemv1alpha1.Queuing
		case batchvc.Aborting, batchvc.Completing, batchvc.Terminating:
			ftStatus.Phase = systemv1alpha1.Stopping
		case batchvc.Aborted, batchvc.Completed, batchvc.Terminated:
			ftStatus.Phase = systemv1alpha1.Stopped
		case batchvc.Running:
			ftStatus.Phase = systemv1alpha1.Running
		case batchvc.Restarting:
			ftStatus.Phase = systemv1alpha1.Restarting
		case batchvc.Failed:
			ftStatus.Phase = systemv1alpha1.Failed
		}
	}
	tj.Status.FaultTolerance[version] = ftStatus
}

func (r *TrainingJobReconciler) restartJob(ctx context.Context, job *systemv1alpha1.TrainingJob, updateStatus state.UpdateJobStatusFn) error {
	if err := r.abortVcJob(job, nil); err != nil {
		return err
	}

	if err := r.syncJobByVcjob(ctx, job, updateStatus); err != nil {
		return err
	}

	var delay int32
	if job.Spec.FaultTolerance != nil && job.Spec.FaultTolerance.RestartPolicy != nil {
		delay = job.Spec.FaultTolerance.RestartPolicy.Delay
	}

	key := types.NamespacedName{Namespace: job.Namespace, Name: job.Name}

	restartingJobsMutex.Lock()
	defer restartingJobsMutex.Unlock()
	if _, ok := r.restartingJobs[key]; !ok {
		r.restartingJobs[key] = &restartingJob{
			Namespace: job.Namespace,
			Name:      job.Name,
			Delay:     delay,
			Version:   job.Status.Version,
			StopChan:  make(chan struct{}),
		}
		go r.handleJobRestarting(key)
	}

	return nil
}

func (r *TrainingJobReconciler) handleJobRestarting(jobKey types.NamespacedName) {
	version := r.restartingJobs[jobKey].Version
	klog.Infof("Handling trainingjob %s of version %d restarting, wait job finish and restart", jobKey, version)
	stopFn := func() {
		restartingJobsMutex.Lock()
		defer restartingJobsMutex.Unlock()
		close(r.restartingJobs[jobKey].StopChan)
		delete(r.restartingJobs, jobKey)
	}

	wait.Until(func() {
		reJob := r.restartingJobs[jobKey]
		delay := time.Duration(reJob.Delay) * time.Second
		if reJob.StoppedTime != nil && reJob.StoppedTime.Add(delay).After(time.Now()) {
			return
		}

		tj := &systemv1alpha1.TrainingJob{}
		err := r.Get(context.TODO(), jobKey, tj)
		if err != nil && !apierrors.IsNotFound(err) {
			klog.Errorf("%s: %s", jobKey, err)
			return
		}

		// 不再需要重启的情况
		command, _ := tj.Annotations[stopAnnotation]
		if apierrors.IsNotFound(err) ||
			tj.Status.State.Phase != systemv1alpha1.Restarting ||
			tj.Status.Version != r.restartingJobs[jobKey].Version ||
			command == stopAction {
			stopFn()
			return
		}

		if tj.Status.FaultTolerance[strconv.Itoa(int(version))].CompletionTime == nil {
			return
		}

		// 到了重启时间，删除 volcanojob，以便创建同名 vj
		// vj 已经删除后，修改 tj 状态为 Pending, Version++
		reJob.StoppedTime = tj.Status.FaultTolerance[strconv.Itoa(int(version))].CompletionTime
		if reJob.StoppedTime.Add(delay).Before(time.Now()) {
			vj, err := r.getVcjobByTrainjob(r.Context, tj)
			if err != nil && !apierrors.IsNotFound(err) {
				klog.Errorf("%s: %s", jobKey, err)
				return
			}
			if !apierrors.IsNotFound(err) {
				klog.Infof("Handling trainingjob %s of version %d restarting, delete vcjob", jobKey, tj.Status.Version)
				if err = r.Delete(context.TODO(), vj); err != nil && !apierrors.IsNotFound(err) {
					klog.Errorf("%s: %s", jobKey, err)
				}
				return
			}

			tj.Status.State.Phase = systemv1alpha1.Pending
			tj.Status.Version++
			tj.Status.RetryCount++
			lastTransitionTime := metav1.Now()
			cond := r.newCondition(tj.Status.State.Phase, &lastTransitionTime)
			tj.Status.Conditions = append(tj.Status.Conditions, cond)
			if r.UpdateTrainjobStatus(r.Context, tj) != nil {
				klog.Errorf("%s: %s", jobKey, err)
			} else {
				klog.Infof("Handling trainingjob %s of version %d restarting, change status to Pending, job version bumped", jobKey, tj.Status.Version)
				stopFn()
			}
		}
	}, 10*time.Second, r.restartingJobs[jobKey].StopChan)
	klog.Infof("Handling trainingjob %s of version %d restarting stopped", jobKey, version)
}

func (r *TrainingJobReconciler) shouldBeNodeHealthChecking(tj *systemv1alpha1.TrainingJob, vj *batchvc.Job, pods []v1.Pod) bool {
	// 是否有配置 NodeHealthBeforeStartCheck
	if tj.Spec.FaultTolerance == nil || len(tj.Spec.FaultTolerance.HealthChecks) == 0 {
		return false
	}
	hasSpec := false
	for _, checker := range tj.Spec.FaultTolerance.HealthChecks {
		if checker.Name == systemv1alpha1.NodeHealthBeforeStartCheck {
			hasSpec = true
			break
		}
	}
	if !hasSpec {
		return false
	}

	// 任何一个 pod init container 的 state 是 waiting 或 running 则代表环境检测进行中
	for i := range pods {
		pod := &pods[i]
		for _, cont := range pod.Status.InitContainerStatuses {
			if cont.Name != systemv1alpha1.InitNodeHealthCheckContainerName {
				continue
			}
			if cont.State.Waiting != nil || cont.State.Running != nil {
				return true
			}
		}
	}
	return false
}

// dropPreviousVersionedStatus 把 status 中的太早的记录删除，避免 trainingjob CR 过大，etcd 默认不能超过 1.5M
// 删除的 status 包括 conditions、faultTolerance、PodDurations，最多只保留最近 3 个 versions.
func (r *TrainingJobReconciler) dropPreviousVersionedStatus(tj *systemv1alpha1.TrainingJob) {
	// conditions
	if len(tj.Status.Conditions) > 24 {
		tj.Status.Conditions = tj.Status.Conditions[len(tj.Status.Conditions)-24:]
	}

	// faultTolerance
	if tj.Status.FaultTolerance != nil {
		versions := make([]int, 0)
		for v := range tj.Status.FaultTolerance {
			n, _ := strconv.Atoi(v)
			versions = append(versions, n)
		}
		sort.Ints(versions)
		for i := 0; i < len(versions)-3; i++ {
			delete(tj.Status.FaultTolerance, strconv.Itoa(versions[i]))
		}
	}

	// PodDurations / serverDurations
	removePodID := make([]string, 0)
	for p := range tj.Status.PodDurations {
		if tj.Status.Version-tj.Status.PodDurations[p].Version >= 3 {
			removePodID = append(removePodID, p)
		}
	}
	for _, p := range removePodID {
		delete(tj.Status.PodDurations, p)
	}
}

func (r *TrainingJobReconciler) checkImagePullStatus(ctx context.Context, trainjob *systemv1alpha1.TrainingJob, pods []v1.Pod) (ok bool, reason string, message string) {
	ok = true
	for i := range pods {
		pod := &pods[i]
		for j := range pod.Status.ContainerStatuses {
			containerStatus := &pod.Status.ContainerStatuses[j]
			if containerStatus.State.Waiting != nil {
				if strings.Contains(containerStatus.State.Waiting.Reason, "ImagePullBackOff") || strings.Contains(containerStatus.State.Waiting.Reason, "ErrImagePull") {
					r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeWarning, systemv1alpha1.EventImagePullFailed, containerStatus.State.Waiting.Message)
					ok = false
					reason = containerStatus.State.Waiting.Reason
					message = containerStatus.State.Waiting.Message
					_, exists := trainjob.Status.PodDurations[string(pod.UID)]
					if exists {
						// podDuration.Reason = reason
						// podDuration.Message = message
						trainjob.Status.PodDurations[string(pod.UID)].Reason = reason
						trainjob.Status.PodDurations[string(pod.UID)].Message = message
						klog.Infof("Trainingjob %s of version %d has image pull failed pod, pod %s reason: %s, message: %s",
							trainjob.Name, trainjob.Status.Version, pod.Name, reason, message)
					}
				}
			} else if containerStatus.State.Running != nil {
				r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventImagePullSuccess, systemv1alpha1.EventImagePullSuccessMessage)
			}
		}
	}
	return
}

func (r *TrainingJobReconciler) hasImagePullFailedPod(trainjob *systemv1alpha1.TrainingJob) bool {
	if trainjob == nil || trainjob.Status.PodDurations == nil {
		return false
	}
	for _, podDuration := range trainjob.Status.PodDurations {
		if podDuration.Version == trainjob.Status.Version {
			if podDuration.Reason == "ImagePullBackOff" || podDuration.Reason == "ErrImagePull" {
				return true
			}
		}
	}
	return false
}

func (r *TrainingJobReconciler) checkFailedPodStatus(ctx context.Context, trainjob *systemv1alpha1.TrainingJob, vj *batchvc.Job, pods []v1.Pod) {
	for i := range pods {
		pod := &pods[i]

		if pod.Status.Phase != v1.PodFailed {
			continue
		}

		if vj != nil && vj.Status.State.Phase != batchvc.Aborted {
			r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeWarning, systemv1alpha1.EventRunFailed,
				fmt.Sprintf(systemv1alpha1.EventDistributedErrMessage, pod.Labels[vcjobPodTaskNameLabels], pod.Name, pod.Status.Message))
		}

		reason, message := r.podFailedReason(pod)
		_, exists := trainjob.Status.PodDurations[string(pod.UID)]
		if exists {
			// podDuration.Reason = reason
			// podDuration.Message = message
			trainjob.Status.PodDurations[string(pod.UID)].Reason = reason
			trainjob.Status.PodDurations[string(pod.UID)].Message = message
			klog.Infof("Trainingjob %s of version %d has pod failed, pod %s reason: %s, message: %s",
				trainjob.Name, trainjob.Status.Version, pod.Name, reason, message)
		}
	}
}

func (r *TrainingJobReconciler) podFailedReason(pod *v1.Pod) (reason, message string) {
	// 已停止的用户容器
	var containerStatus *v1.ContainerStatus
	for i, status := range pod.Status.ContainerStatuses {
		if status.Name == systemv1alpha1.DefaultUserContainerName && status.State.Terminated != nil {
			containerStatus = &pod.Status.ContainerStatuses[i]
		}
	}
	if containerStatus == nil {
		return
	}

	// 默认 reason
	reason = containerStatus.State.Terminated.Reason
	message = containerStatus.State.Terminated.Message

	if reason == "ContainerCannotRun" {
		// NVIDIA CUDA Driver 版本不满足
		// https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/latest/docker-specialized.html#nvidia-require-constraints

		// 示例：OCI runtime create failed: container_linux.go:380: starting container process caused:
		// process_linux.go:545: container init caused: Running hook #0:: error running hook: exit status 1,
		// stdout: , stderr: nvidia-container-cli: requirement error: unsatisfied condition: cuda>=11.8,
		// please update your driver to a newer version, or use an earlier cuda container: unknown
		cudaKeywords := "unsatisfied condition: cuda"
		// 示例：failed to create task for container: failed to create shim task: OCI runtime create failed:
		// runc create failed: unable to start container process: error during container init: error running hook #0:
		// error running hook: exit status 1, stdout: , stderr: Auto-detected mode as 'legacy'
		// nvidia-container-cli: requirement error: unsatisfied condition: driver>=650: unknown
		driverKeywords := "unsatisfied condition: driver"

		if strings.Contains(message, cudaKeywords) {
			reason = "NvidiaCudaVersionUnsatisfied"
			if index := strings.Index(message, cudaKeywords); index != -1 {
				message = message[index:]
			}
		} else if strings.Contains(message, driverKeywords) {
			reason = "NvidiaDriverVersionUnsatisfied"
			if index := strings.Index(message, driverKeywords); index != -1 {
				message = message[index:]
			}
		}
	}

	return
}

func (r *TrainingJobReconciler) stopJob(ctx context.Context, trainjob *systemv1alpha1.TrainingJob, vj *batchvc.Job) error {
	if err := r.abortVcJob(trainjob, vj); err != nil {
		return err
	}

	trainjob.Status.State.Phase = systemv1alpha1.Stopping
	r.EventRecord.EventRecord(ctx, trainjob, v1.EventTypeNormal, systemv1alpha1.EventStopping, systemv1alpha1.EventStoppingMessage)
	lastTransitionTime := metav1.Now()
	trainjobCondition := r.newCondition(trainjob.Status.State.Phase, &lastTransitionTime)
	trainjob.Status.Conditions = append(trainjob.Status.Conditions, trainjobCondition)
	return r.UpdateTrainjobStatus(ctx, trainjob)
}

func (r *TrainingJobReconciler) checkTrainingJobRestartable(tj *systemv1alpha1.TrainingJob, vj *batchvc.Job, pods []v1.Pod) (restartReasons []string) {
	if tj == nil ||
		tj.Spec.FaultTolerance == nil ||
		tj.Spec.FaultTolerance.RestartPolicy == nil ||
		len(tj.Spec.FaultTolerance.RestartPolicy.Policies) == 0 ||
		tj.Status.RetryCount >= tj.Spec.FaultTolerance.RestartPolicy.MaxRetry ||
		tj.Status.State.Phase == systemv1alpha1.Stopping ||
		tj.Status.State.Phase == systemv1alpha1.Restarting {
		return
	}

	ftStatus, ok := tj.Status.FaultTolerance[strconv.Itoa(int(tj.Status.Version))]
	if !ok {
		ftStatus = systemv1alpha1.FaultToleranceStatus{}
	}
	for _, checkResult := range ftStatus.HealthChecks {
		for _, restartPolicy := range tj.Spec.FaultTolerance.RestartPolicy.Policies {
			if checkResult.Result == restartPolicy {
				restartReasons = append(restartReasons, string(restartPolicy))
			}
		}
	}

	// volcanojob 创建 pod 失败，重试几次后会直接失败
	// vj 没有 running 就 aborted 或 terminated 了，属于意外停止，需要重启
	if vj != nil && IsVolcanoJobFinished(vj) && tj.Annotations[stopAnnotation] != stopAction {
		hasRunning := false
		for _, condition := range vj.Status.Conditions {
			if condition.Status == batchvc.Running {
				hasRunning = true
				break
			}
		}
		if !hasRunning {
			klog.Warningf("Trainingjob %s of version %d is unexpected stopped without vj running phase", tj.Name, tj.Status.Version)
			restartReasons = append(restartReasons, string(systemv1alpha1.UnexpectedInternalError))
		}
	}

	if r.hasImagePullFailedPod(tj) {
		klog.Warningf("Trainingjob %s of version %d has image pull failed pod, skip restart", tj.Name, tj.Status.Version)
		restartReasons = make([]string, 0)
	}

	return
}

func getReplicasCount(job *systemv1alpha1.TrainingJob) int32 {
	var count int32
	for _, task := range job.Spec.Tasks {
		count += task.Replicas
	}

	return count
}
