package job

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

func TestTrainingjobActionConstants(t *testing.T) {
	// Test constants defined in the original code
	assert.Equal(t, "nodes.system.hero.ai", nodeAnnotation)
	assert.Equal(t, "command.system.hero.ai", stopAnnotation)
	assert.Equal(t, "stop", stopAction)
	assert.Equal(t, "volcano.sh/job-name", vcjobPodJobNameLabels)
	assert.Equal(t, "volcano.sh/task-spec", vcjobPodTaskNameLabels)
	assert.Equal(t, "volcano.sh/job-namespace", vcjobPodNamespaceLabels)
	assert.Equal(t, "image-secret", secretName)
}

func TestUpdateTrainjob(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}
	ctx := context.Background()

	trainjob := &v1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
		Status: v1alpha1.TrainingJobStatus{
			State: v1alpha1.TrainingJobState{
				Phase: v1alpha1.Pending,
			},
		},
	}
	err := r.Client.Create(context.TODO(), trainjob)
	assert.NoError(t, err)

	err = r.UpdateTrainjob(ctx, trainjob)
	assert.NoError(t, err)
}

func TestUpdateTrainjobStatus(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}
	ctx := context.Background()

	trainjob := &v1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
		Status: v1alpha1.TrainingJobStatus{
			State: v1alpha1.TrainingJobState{
				Phase: v1alpha1.Pending,
			},
		},
	}
	err := r.Client.Create(context.TODO(), trainjob)
	assert.NoError(t, err)

	err = r.UpdateTrainjobStatus(ctx, trainjob)
	assert.NoError(t, err)
}

func TestInitTrainjobStatus(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}
	ctx := context.Background()

	trainjob := &v1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
		Status: v1alpha1.TrainingJobStatus{
			State: v1alpha1.TrainingJobState{
				Phase: v1alpha1.Pending,
			},
		},
	}
	err := r.Client.Create(context.TODO(), trainjob)
	assert.NoError(t, err)

	newTrainjob, isNoNeedToCreateVc, err := r.initTrainjobStatus(ctx, trainjob)
	assert.NoError(t, err)
	assert.False(t, isNoNeedToCreateVc)
	assert.Equal(t, v1alpha1.Pending, newTrainjob.Status.State.Phase)
}

func TestSyncTrainjobPodConditions(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}
	ctx := context.Background()

	trainjob := &v1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
		Status: v1alpha1.TrainingJobStatus{
			State: v1alpha1.TrainingJobState{
				Phase: v1alpha1.Pending,
			},
		},
	}
	err := r.Client.Create(context.TODO(), trainjob)
	assert.NoError(t, err)

	r.syncTrainjobPodConditions(ctx, trainjob)
	// Add more specific assertions as needed
}

func TestGetVcjobByTrainjob(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}
	ctx := context.Background()

	trainjob := &v1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
	}
	err := r.Client.Create(context.TODO(), trainjob)
	assert.NoError(t, err)

	_, err = r.getVcjobByTrainjob(ctx, trainjob)
	assert.Error(t, err)
	// Since we haven't created a corresponding Volcano job, an error is expected.
}

func TestGetPodGroupByTrainjob(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}
	ctx := context.Background()

	trainjob := &v1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
	}
	err := r.Client.Create(context.TODO(), trainjob)
	assert.NoError(t, err)

	_, err = r.getPodGroupByTrainjob(ctx, trainjob)
	assert.Error(t, err)
	// Since we haven't created any relevant PodGroup, an error is expected.
}

func TestPluginOnVolcanojobCreate(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	trainjob := &v1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
		Spec: v1alpha1.TrainingJobSpec{
			Plugins: []string{"test-plugin"},
		},
	}

	vcjob := &batchvc.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-vcjob",
			Namespace: "default",
		},
	}

	err := r.pluginOnVolcanojobCreate(trainjob, vcjob)
	assert.Error(t, err)
	// Since there is no real plugin named "test-plugin", an error is expected.
}

func TestTrainingjobActionCreateVjCommandBusIfNotExist(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	trainjob := &v1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
	}

	vcjob := &batchvc.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-vcjob",
			Namespace: "default",
		},
	}

	err := r.abortVcJob(trainjob, vcjob)
	assert.NoError(t, err)
}

func TestTrainingjobActionUpdatePodConds(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1alpha1.AddToScheme(scheme)
	_ = batchvc.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	trainjob := &v1alpha1.TrainingJob{
		Status: v1alpha1.TrainingJobStatus{
			PodDurations: map[string]*v1alpha1.TrainingJobPodDurations{
				"test-pod-uid": {
					Name:     "test-pod",
					Phase:    v1.PodRunning,
					TaskName: "test-task",
				},
			},
		},
	}

	r.updatePodConds(trainjob)
	assert.Equal(t, v1.PodSucceeded, trainjob.Status.PodDurations["test-pod-uid"].Phase)
}

func TestTrainingjobActionNewCondition(t *testing.T) {
	scheme := runtime.NewScheme()
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	status := v1alpha1.TrainingJobPhase("Running")
	lastTransitionTime := metav1.NewTime(time.Now())

	condition := r.newCondition(status, &lastTransitionTime)
	assert.Equal(t, status, condition.Status)
	assert.Equal(t, lastTransitionTime, *condition.LastTransitionTime)
}
