package vcjobbuilder

import (
	"fmt"
	"sync"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

var (
	containerName = "sidecar"
	pluginMutex   sync.Mutex
	pluginCmd     = make(map[string]int)
	cmd           = "/usr/bin/check.sh"
	//template      = "which %s > /dev/null 2>&1 || echo %s is not installed | wget '%s/api/v1/plugin-status/update?jobname=%s&plugin=%s'"
	//template = "nc -zv $(POD_IP) %d || echo %s is not installed | wget '%s/api/v1/plugin-status/update?jobname=%s&plugin=%s'"
	image = "registry.cnbita.com:5000/cloud-images/hero-user/updatenb-tool:v1.0.8"
)

type SidecarContainer struct {
}

func NewSidecarContainer() *SidecarContainer {
	return &SidecarContainer{}
}

func RegisterCheckCmd(pluginName string, port int) {
	pluginMutex.Lock()
	defer pluginMutex.Unlock()
	pluginCmd[pluginName] = port
}

func InitConfig() {
	pluginCmd = make(map[string]int)
}

func (sc *SidecarContainer) Build(job *systemv1alpha1.Notebook, localURL string) *v1.Container {
	containerSpec := v1.Container{}

	containerSpec.Image = image
	containerSpec.Name = containerName

	containerSpec.Command = append(containerSpec.Args, TerminalMode...)
	containerSpec.Env = []v1.EnvVar{{
		ValueFrom: &v1.EnvVarSource{
			FieldRef: &v1.ObjectFieldSelector{
				FieldPath: "status.podIP",
			},
		},
		Name: "POD_IP",
	}}
	containerSpec.Env = append(containerSpec.Env, v1.EnvVar{
		Name:  "JOBNAME",
		Value: job.Name,
	}, v1.EnvVar{
		Name:  "LOCALURL",
		Value: localURL,
	})
	return &containerSpec
}

func BuildCmd(job *systemv1alpha1.Notebook, vcjob *batchvc.Job) {
	for k, task := range vcjob.Spec.Tasks {
		for kk := range task.Template.Spec.Containers {

			if task.Template.Spec.Containers[kk].Name == containerName {
				var cmdArgs = cmd
				if len(pluginCmd) > 0 {
					for _, port := range pluginCmd {
						cmdArgs += " "
						cmdArgs += fmt.Sprint(port)
					}
				}
				vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args = append(vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args, cmdArgs)
			}
		}
	}

}
