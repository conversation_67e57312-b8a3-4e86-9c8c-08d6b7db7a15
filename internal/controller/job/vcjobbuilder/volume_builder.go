package vcjobbuilder

import (
	"path/filepath"
	"sync"

	v1 "k8s.io/api/core/v1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
)

const (
	localDiskMountName = "mount-local-disk"
	localDiskMountPath = "/localdisk-tmp"
)

var initOnce sync.Once

func getCommonMountPath() string {
	path := "/user-storage/gbasic-component/gdata/job-controllers"
	initOnce.Do(
		func() {
			if len(config.SC.SharedVolumeHostPath) > 0 {
				path = config.SC.SharedVolumeHostPath
			}
		},
	)

	return path
}

type VolumeBuilder struct {
}

func NewVolumeBuilder() IStorageBuilder {
	return &VolumeBuilder{}
}

func (b VolumeBuilder) Build(storage *systemv1alpha1.DataSource) *v1.Volume {
	return &v1.Volume{
		Name: storage.Name,
		VolumeSource: v1.VolumeSource{
			PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
				ClaimName: storage.VolumeName,
				ReadOnly:  storage.ReadOnly,
			},
		},
	}
}

func BuildCommonVolume() *v1.Volume {
	hostType := v1.HostPathDirectoryOrCreate
	return &v1.Volume{
		Name: defaultMountName,
		VolumeSource: v1.VolumeSource{
			HostPath: &v1.HostPathVolumeSource{
				Path: getCommonMountPath(),
				Type: &hostType,
			},
		},
	}
}

func BuildLocalDiskVolume(jobName string) *v1.Volume {
	hostType := v1.HostPathDirectoryOrCreate
	prefix := config.SC.LocalDiskHostPath
	path := filepath.Join(prefix, jobName)
	return &v1.Volume{
		Name: localDiskMountName,
		VolumeSource: v1.VolumeSource{
			HostPath: &v1.HostPathVolumeSource{
				Path: path,
				Type: &hostType,
			},
		},
	}
}
