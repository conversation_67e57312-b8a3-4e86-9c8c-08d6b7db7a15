package vcjobbuilder

import (
	"strings"

	"hero.ai/hero-controllers/internal/controller/config"
	corev1 "k8s.io/api/core/v1"
	batch "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const (
	ResourceNameInfiniband = "rdma/hca_infiniband"
	ResourceNameRoCE       = "rdma/hca_roce"

	MultusCNIAnnotationKey = "k8s.v1.cni.cncf.io/networks"

	GPUTypeNvidia = "nvidia"
	GPUTypeAscend = "ascend"
)

type RDMABuilder struct {
	roceNetwork string
	ncclEnvs    []corev1.EnvVar
	hcclEnvs    []corev1.EnvVar
}

func NewRDMABuilder(cfg config.RDMA) CustomBuilder {
	rb := &RDMABuilder{
		roceNetwork: cfg.SecondaryNetwork,
	}
	if len(cfg.NCCLEnvs) != 0 {
		ncclEnvs := make([]corev1.EnvVar, 0, len(cfg.NCCLEnvs))
		for _, env := range cfg.NCCLEnvs {
			if splits := strings.Split(env, "="); len(splits) == 2 {
				ncclEnvs = append(ncclEnvs, corev1.EnvVar{
					Name:  splits[0],
					Value: splits[1],
				})
			}
		}
		rb.ncclEnvs = ncclEnvs
	}
	if len(cfg.HCCLEnvs) != 0 {
		hcclEnvs := make([]corev1.EnvVar, 0, len(cfg.HCCLEnvs))
		for _, env := range cfg.HCCLEnvs {
			if splits := strings.Split(env, "="); len(splits) == 2 {
				hcclEnvs = append(hcclEnvs, corev1.EnvVar{
					Name:  splits[0],
					Value: splits[1],
				})
			}
		}
		rb.hcclEnvs = hcclEnvs
	}
	return rb
}

func (rb *RDMABuilder) IsSatisfied(jobReq interface{}, vcJob *batch.Job) bool {
	return true
}

// 主要流程：
// 1. RoCE 需要注入多网卡配置
// 2. 注入 NCCL/HCCL RDMA通信相关环境变量
func (rb *RDMABuilder) PostBuild(jobReq interface{}, vcJob *batch.Job) {
	for i := range vcJob.Spec.Tasks {
		task := &vcJob.Spec.Tasks[i]
		pType := rb.protocolTypeAndInjectEnvs(task)
		if pType == ResourceNameRoCE {
			// 设置 RoCE 容器网络
			if task.Template.Annotations == nil {
				task.Template.Annotations = make(map[string]string)
			}
			task.Template.Annotations[MultusCNIAnnotationKey] = rb.roceNetwork
		}
	}
}

func (rb *RDMABuilder) protocolTypeAndInjectEnvs(task *batch.TaskSpec) (rName string) {
	for i, container := range task.Template.Spec.Containers {
		if _, ok := container.Resources.Requests[ResourceNameInfiniband]; ok {
			rName = ResourceNameInfiniband
		}
		if _, ok := container.Resources.Requests[ResourceNameRoCE]; ok {
			rName = ResourceNameRoCE
		}
		if len(rName) == 0 {
			continue
		}
		for resourceName := range container.Resources.Requests {
			if strings.HasPrefix(string(resourceName), GPUTypeNvidia) && len(rb.ncclEnvs) != 0 {
				task.Template.Spec.Containers[i].Env = append(task.Template.Spec.Containers[i].Env, rb.ncclEnvs...)

			} else if strings.Contains(strings.ToLower(string(resourceName)), GPUTypeAscend) && len(rb.hcclEnvs) != 0 {
				task.Template.Spec.Containers[i].Env = append(task.Template.Spec.Containers[i].Env, rb.hcclEnvs...)
			}
		}
		return
	}
	return ""
}
