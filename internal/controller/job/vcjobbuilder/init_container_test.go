package vcjobbuilder

import (
	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"testing"
	"volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

func TestNewTaskInitContainers(t *testing.T) {
	initContainers := NewTaskInitContainers()
	assert.NotNil(t, initContainers)
}

func TestRegisterCmd(t *testing.T) {
	RegisterCmd("testPlugin", "testCommand")
	assert.Equal(t, "testCommand", appToCmd["testPlugin"])
}

func TestRegisterEnv(t *testing.T) {
	RegisterEnv("testEnv", "testValue")
	assert.Equal(t, "testValue", envMap["testEnv"])
}

func TestInitRegisterConfig(t *testing.T) {
	InitRegisterConfig()
	assert.Empty(t, appToCmd)
	assert.Empty(t, envMap)
}

func TestBuildGitCodeInitContainer(t *testing.T) {
	codeSource := &systemv1alpha1.CodeSource{
		GitUrl:    "https://github.com/test/repo.git",
		MountPath: "/mnt/git",
	}

	container := buildGitCodeInitContainer(codeSource)

	assert.NotNil(t, container)
	assert.Equal(t, "code-init", container.Name)
	assert.Equal(t, gitPullImage, container.Image)
	assert.Contains(t, container.Args, "set -e; git clone https://github.com/test/repo.git ")
}

func TestGetGitCommand(t *testing.T) {
	codeSource := &systemv1alpha1.CodeSource{
		GitUrl:      "https://github.com/test/repo.git",
		AccessToken: "token",
		AccessName:  "user",
		Branch:      "main",
	}

	expected := "git clone https://user:<EMAIL>/test/repo.git -b main"
	result := getGitCommand(codeSource)
	assert.Equal(t, expected, result)

	codeSource2 := &systemv1alpha1.CodeSource{
		GitUrl: "http://github.com/test/repo.git",
	}

	expected2 := "git clone http://github.com/test/repo.git "
	result2 := getGitCommand(codeSource2)
	assert.Equal(t, expected2, result2)
}

func TestBuildVcjobExtendCap(t *testing.T) {
	vcjob := &v1alpha1.Job{
		Spec: v1alpha1.JobSpec{
			Tasks: []v1alpha1.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: "test-container"},
							},
						},
					},
				},
			},
		},
	}

	vcjob = BuildVcjobExtendCap(vcjob, "test-command", "")

	assert.NotNil(t, vcjob)
	assert.Equal(t, 1, len(vcjob.Spec.Tasks[0].Template.Spec.InitContainers))
	assert.Equal(t, initSupervisorName, vcjob.Spec.Tasks[0].Template.Spec.InitContainers[0].Name)
}

func TestBuildVcjobWebterminal(t *testing.T) {
	vcjob := &v1alpha1.Job{
		Spec: v1alpha1.JobSpec{
			Tasks: []v1alpha1.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: "test-container", Args: []string{"arg1"}},
							},
						},
					},
				},
			},
		},
	}

	vcjob = BuildVcjobWebterminal(vcjob)

	assert.NotNil(t, vcjob)
	assert.Equal(t, 1, len(vcjob.Spec.Tasks[0].Template.Spec.Containers))
	assert.Contains(t, vcjob.Spec.Tasks[0].Template.Spec.Containers[0].Args, "/app/gotty/gotty --address 0.0.0.0 --port 8083 --ws-origin '.*' --permit-write --reconnect /bin/bash & arg1")
}

func TestBuildVolume(t *testing.T) {
	storage := &systemv1alpha1.DataSource{Name: "test-volume"}
	volume := buildVolume(storage)

	assert.NotNil(t, volume)
	assert.Equal(t, storage.Name, volume.Name)
	assert.NotNil(t, volume.VolumeSource.EmptyDir)
}
