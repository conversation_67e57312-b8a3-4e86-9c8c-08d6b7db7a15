package vcjobbuilder

import (
	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"testing"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

func TestNewSidecarContainer(t *testing.T) {
	sidecar := NewSidecarContainer()
	assert.NotNil(t, sidecar)
}

func TestRegisterCheckCmd(t *testing.T) {
	RegisterCheckCmd("test-plugin", 8080)

	// 验证 pluginCmd 中的值
	assert.Equal(t, 8080, pluginCmd["test-plugin"])
}

func TestInitConfig(t *testing.T) {
	RegisterCheckCmd("test-plugin", 8080) // 先注册一个命令
	assert.Equal(t, 8080, pluginCmd["test-plugin"])

	InitConfig() // 初始化配置

	// 验证 pluginCmd 是否已重置
	_, exists := pluginCmd["test-plugin"]
	assert.False(t, exists)
}

func TestBuild(t *testing.T) {
	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	localURL := "http://localhost:8080"

	sidecar := NewSidecarContainer()
	container := sidecar.Build(job, localURL)

	assert.NotNil(t, container)
	assert.Equal(t, image, container.Image)
	assert.Equal(t, containerName, container.Name)
	assert.Len(t, container.Env, 3) // 确保环境变量数量正确
	assert.Equal(t, "JOBNAME", container.Env[1].Name)
	assert.Equal(t, job.Name, container.Env[1].Value)
	assert.Equal(t, "LOCALURL", container.Env[2].Name)
	assert.Equal(t, localURL, container.Env[2].Value)
}

func TestBuildCmd(t *testing.T) {
	// 设置一个测试用的插件命令
	RegisterCheckCmd("test-plugin", 8080)

	// 创建一个测试的 Notebook 和 Job
	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}
	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Tasks: []batchvc.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{
									Name: containerName,
								},
							},
						},
					},
				},
			},
		},
	}

	// 构建命令
	BuildCmd(job, vcjob)

	// 验证容器的 Args
	assert.Len(t, vcjob.Spec.Tasks[0].Template.Spec.Containers[0].Args, 1)
	assert.Contains(t, vcjob.Spec.Tasks[0].Template.Spec.Containers[0].Args[0], cmd)
	assert.Contains(t, vcjob.Spec.Tasks[0].Template.Spec.Containers[0].Args[0], "8080") // 确保端口被添加
}
