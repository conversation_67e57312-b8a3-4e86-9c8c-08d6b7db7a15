//nolint:all
package vcjobbuilder

import (
	v1 "k8s.io/api/core/v1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

type EmptyDirBuilder struct {
}

var (
	defaultCodeMountName = "mount-code"
	defaultMountName     = "mount-shared"
)

func defaultGitCode(cs *systemv1alpha1.CodeSource) *systemv1alpha1.DataSource {
	return &systemv1alpha1.DataSource{
		Name:      defaultCodeMountName,
		MountPath: cs.MountPath,
		DataType:  systemv1alpha1.Git,
	}
}

func NewEmptyDirBuilder() *EmptyDirBuilder {
	return &EmptyDirBuilder{}
}

func (eb EmptyDirBuilder) Build(storage *systemv1alpha1.DataSource) *v1.Volume {
	return &v1.Volume{
		Name: storage.Name,
		VolumeSource: v1.VolumeSource{
			EmptyDir: &v1.EmptyDirVolumeSource{},
		},
	}
}
