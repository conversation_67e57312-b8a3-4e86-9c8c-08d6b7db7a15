package vcjobbuilder

import (
	"strconv"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	"volcano.sh/apis/pkg/apis/bus/v1alpha1"
)

const (
	defaultTTLSecondsAfterFinished = 604800
	SSH                            = "ssh"
	DefaultSchedulerName           = "volcano"
	PluginEnvName                  = "env"
	PluginSvcName                  = "svc"
	LabelVendor                    = "vendor"
	HeroJobVendor                  = "job.hero.ai"
	LabelJobKind                   = "hero.ai/kind"
	VcjobNameTrainjobPrefix        = "tj-"
	VcjobNameNotebookPrefix        = "nb-"
	TrainingJobVersionLabel        = "system.hero.ai/job-version"
)

func NewVcJobBuilder() *vcjobBuilder { //nolint
	return &vcjobBuilder{
		ITaskBuilder:   NewVcjobTaskBuilder(),
		CustomBuilders: []CustomBuilder{NewAscendNPUBuilder(), NewRDMABuilder(config.SC.RDMA)},
	}
}

type ITaskBuilder interface {
	CreateTask(task *systemv1alpha1.Task, trainjob *systemv1alpha1.TrainingJob, secretName, resourcepool string) *batchvc.TaskSpec
	CreateTaskByNotebook(job *systemv1alpha1.Notebook, secretName, localURL string) *batchvc.TaskSpec
}

// CustomBuilder 对满足条件的作业进行额外的配置
type CustomBuilder interface {
	// IsSatisfied 判断是否需要该自定义 builder 构建配置
	IsSatisfied(jobReq interface{}, vcJob *batchvc.Job) bool

	// PostBuild 在一般构建流程完成后，对 job 进行额外配置
	PostBuild(jobReq interface{}, vcJob *batchvc.Job)
}

type vcjobBuilder struct {
	ITaskBuilder   ITaskBuilder
	CustomBuilders []CustomBuilder
}

func (vjcb *vcjobBuilder) BuildByNotebook(job *systemv1alpha1.Notebook, scheme *runtime.Scheme, secretName, localURL string) *batchvc.Job {
	vcJob := &batchvc.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      VcjobNameNotebookPrefix + job.Name,
			Namespace: job.Namespace,
		},
		Spec: batchvc.JobSpec{
			MinAvailable: 1,
		},
	}
	if err := controllerutil.SetControllerReference(job, vcJob, scheme); err != nil {
		klog.Errorf("set controller reference failed: %s", err.Error())
	}

	vjcb.buildCommonConfig(job.Name, job.Namespace, job.Spec.ResourcePool, job.Kind, job.Spec.MaxRetryCount, []systemv1alpha1.Env{}, vcJob)
	vcJob.Labels[systemv1alpha1.VcjobNbLabels] = ""
	vjcb.buildJobTasksByNotebook(job, vcJob, secretName, localURL)

	for _, cb := range vjcb.CustomBuilders {
		if cb.IsSatisfied(job, vcJob) {
			cb.PostBuild(job, vcJob)
		}
	}

	klog.Infof(
		"Input job config is: %s\t\tOutput vc-job FINAL config is: %s",
		ToJSON(job),
		ToJSON(vcJob),
	)

	return vcJob

}

func (vjcb *vcjobBuilder) Build(trainjob *systemv1alpha1.TrainingJob, scheme *runtime.Scheme, secretName string) *batchvc.Job {
	vcJob := &batchvc.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      VcjobNameTrainjobPrefix + trainjob.Name,
			Namespace: trainjob.Namespace,
		},
	}

	if err := controllerutil.SetControllerReference(trainjob, vcJob, scheme); err != nil {
		klog.Errorf("set controller reference failed: %s", err.Error())
	}

	// 1. 通用配置
	vjcb.buildCommonConfig(trainjob.Name, trainjob.Namespace, trainjob.Spec.ResourcePool, trainjob.Kind, trainjob.Spec.MaxRetryCount, trainjob.Spec.Envs, vcJob)
	vcJob.Labels[systemv1alpha1.VcjobTjLabels] = ""
	vcJob.Labels[TrainingJobVersionLabel] = strconv.Itoa(int(trainjob.Status.Version))

	vjcb.buildJobTasks(trainjob, vcJob, secretName)
	vjcb.buildMinAvailable(trainjob, vcJob)

	// 后处理构建其他自定义配置
	for _, cb := range vjcb.CustomBuilders {
		if cb.IsSatisfied(trainjob, vcJob) {
			cb.PostBuild(trainjob, vcJob)
		}
	}

	klog.Infof(
		"Input job config is: %s\t\tOutput vc-job FINAL config is: %s",
		ToJSON(trainjob),
		ToJSON(vcJob),
	)

	return vcJob
}

func (vjcb *vcjobBuilder) buildCommonConfig(name, namespace, queue, kind string, maxRetryCount int32, envs []systemv1alpha1.Env, vcJob *batchvc.Job) {
	vcJob.Namespace = namespace
	secondsAfterFinished := int32(defaultTTLSecondsAfterFinished)
	vcJob.Spec.TTLSecondsAfterFinished = &secondsAfterFinished
	vcJob.Spec.MaxRetry = maxRetryCount
	vcJob.Spec.Policies = nil

	// 适配VGPU
	vcJob.Spec.SchedulerName = DefaultSchedulerName
	vcJob.Spec.Policies = append(vcJob.Spec.Policies, batchvc.LifecyclePolicy{
		Action: v1alpha1.AbortJobAction,
		Event:  v1alpha1.PodEvictedEvent,
	})
	vjcb.buildPlugin(envs, vcJob)
	vjcb.buildQueue(queue, vcJob)
	vjcb.buildLables(name, namespace, kind, vcJob)
}

func (vjcb *vcjobBuilder) buildQueue(queue string, vcJob *batchvc.Job) {
	vcJob.Spec.Queue = queue
}

func (vjcb *vcjobBuilder) buildJobTasks(job *systemv1alpha1.TrainingJob, vcJob *batchvc.Job, secretName string) {
	for i := range job.Spec.Tasks {
		vcJob.Spec.Tasks = append(vcJob.Spec.Tasks, *vjcb.ITaskBuilder.CreateTask(&job.Spec.Tasks[i], job, secretName, job.Spec.Tasks[i].ResourcePool))
	}
}

func (vjcb *vcjobBuilder) buildJobTasksByNotebook(job *systemv1alpha1.Notebook, vcJob *batchvc.Job, secretName, localURL string) {
	vcJob.Spec.Tasks = append(vcJob.Spec.Tasks, *vjcb.ITaskBuilder.CreateTaskByNotebook(job, secretName, localURL))
}

func (vjcb *vcjobBuilder) buildPlugin(envs []systemv1alpha1.Env, vcJob *batchvc.Job) {
	vcJob.Spec.Plugins = make(map[string][]string)
	envlist := []string{}
	if len(envs) != 0 {
		for _, i := range envs {
			envlist = append(envlist, i.Name+","+i.Value)
		}
	}
	vcJob.Spec.Plugins[PluginEnvName] = envlist
	// 配置 volcano svc plugin
	// 为 Job 中的所有 Pod 配置 Service, 以便根据域名相互访问。
	// 如果 enableUserNetworkIsolation=true, 由后续自定义 NetworkPolicy 来实现隔离逻辑；
	// 否则，使用 volcano svc plugin 的 NetworkPolicy.
	vcJob.Spec.Plugins[PluginSvcName] = []string{"--disable-network-policy=true"}

	// 配置 volcano ssh plugin
	// 为 Job 中所有 Pod 配置相同密钥和 ".ssh/config"，保证可以相互密钥访问。
	// 如果 Job 有 ssh capability，则不使用该 plugin，使用后续自定义实现。
	vcJob.Spec.Plugins[SSH] = []string{}
}

func (vjcb *vcjobBuilder) buildLables(name, namespace, kind string, vcJob *batchvc.Job) {
	vcJob.Labels = make(map[string]string)
	vcJob.Labels[systemv1alpha1.VcjobNameLabels] = name
	vcJob.Labels[systemv1alpha1.VcjobNsLabels] = namespace
	vcJob.Labels[LabelVendor] = HeroJobVendor
	vcJob.Labels[LabelJobKind] = kind
}

func (vjcb *vcjobBuilder) buildMinAvailable(job *systemv1alpha1.TrainingJob, vcJob *batchvc.Job) {
	vcJob.Spec.MinAvailable = 0
	for i := range job.Spec.Tasks {
		vcJob.Spec.MinAvailable += job.Spec.Tasks[i].MinAvaluable
	}
}
