package vcjobbuilder

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestToJSON(t *testing.T) {
	type TestStruct struct {
		Name  string `json:"name"`
		Value int    `json:"value"`
	}

	testData := TestStruct{
		Name:  "test",
		Value: 42,
	}

	expectedJSON := `{"name":"test","value":42}`

	result := ToJSON(testData)

	assert.JSONEq(t, expectedJSON, string(result), "The JSON output should match the expected value")
}

func TestContainsStr(t *testing.T) {
	strList := []string{"apple", "banana", "orange"}

	assert.True(t, ContainsStr(strList, "banana"), "The string should be found in the list")
	assert.False(t, ContainsStr(strList, "grape"), "The string should not be found in the list")
}
