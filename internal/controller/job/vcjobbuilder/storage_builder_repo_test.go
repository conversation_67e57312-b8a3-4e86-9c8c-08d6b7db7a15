package vcjobbuilder

import (
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

func TestNewStorageBuilderFactory(t *testing.T) {
	factory := NewStorageBuilderFactory()

	assert.NotNil(t, factory)
	assert.NotNil(t, factory.volumeBuilders)
	assert.Equal(t, 4, len(factory.volumeBuilders)) // 确保注册了4个存储构建器

	// 验证各个类型的构建器是否注册成功
	assert.IsType(t, &VolumeBuilder{}, factory.Get(string(systemv1alpha1.Volume)))
	assert.IsType(t, &EmptyDirBuilder{}, factory.Get(string(systemv1alpha1.Git)))
	assert.IsType(t, &ShareMemoryStorage{}, factory.Get(string(systemv1alpha1.ShareMemory)))
	assert.IsType(t, &EmptyDirBuilder{}, factory.Get("app"))
}

func TestStorageBuilderFactory_Get_NonExistentBuilder(t *testing.T) {
	factory := NewStorageBuilderFactory()

	builder := factory.Get("nonexistent")
	assert.NotNil(t, builder)
	assert.IsType(t, &ErrorBuilder{}, builder)
}

func TestBuildCodeVolumeMount(t *testing.T) {
	codeSource := &systemv1alpha1.CodeSource{
		MountPath: "/code",
	}
	volumeMount := BuildCodeVolumeMount(codeSource)

	assert.NotNil(t, volumeMount)
	assert.Equal(t, "mount-code", volumeMount.Name)
	assert.Equal(t, codeSource.MountPath, volumeMount.MountPath)
	assert.False(t, volumeMount.ReadOnly)
}

func TestBuildVolumeMount(t *testing.T) {
	dataSource := &systemv1alpha1.DataSource{
		Name:      "test-volume",
		MountPath: "/mnt/test",
		ReadOnly:  true,
	}

	volumeMount := BuildVolumeMount(dataSource, "subpath")

	assert.NotNil(t, volumeMount)
	assert.Equal(t, dataSource.Name, volumeMount.Name)
	assert.Equal(t, dataSource.MountPath, volumeMount.MountPath)
	assert.True(t, volumeMount.ReadOnly)
	assert.Equal(t, "subpath", volumeMount.SubPath)
}

func TestBuildLocalDiskVolumeMount(t *testing.T) {
	volumeMount := BuildLocalDiskVolumeMount()

	assert.NotNil(t, volumeMount)
	assert.Equal(t, localDiskMountName, volumeMount.Name)
	assert.Equal(t, localDiskMountPath, volumeMount.MountPath)
	assert.False(t, volumeMount.ReadOnly)
}

func TestStorageBuilderFactory_Regist(t *testing.T) {
	factory := NewStorageBuilderFactory()
	assert.NotNil(t, factory)

	builder := &EmptyDirBuilder{}
	factory.regist("new-type", builder)

	// 验证新的构建器是否成功注册
	assert.Equal(t, builder, factory.Get("new-type"))
}

//import (
//	"reflect"
//	"testing"
//
//	"github.com/smartystreets/goconvey/convey"
//	v1 "k8s.io/api/core/v1"
//
//	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
//)
//
//func TestStorageBuilderFactory_Build(t *testing.T) {
//	convey.Convey("测试StorageBuilderFactory_Build", t, func() {
//		eb := &ErrorBuilder{}
//		storage := &systemv1alpha1.DataSource{}
//		result := eb.Build(storage)
//
//		convey.So(result, convey.ShouldBeNil)
//	})
//}
//
//func TestBuildVolumeMount(t *testing.T) {
//	convey.Convey("测试挂载卷创建", t, func() {
//		convey.Convey("挂载卷创建成功", func() {
//			storage := &systemv1alpha1.CodeSource{
//				// Name:      "name",
//				// MountPath: "mountPath",
//				// ReadOnly:  true,
//			}
//			expect := &v1.VolumeMount{
//				Name:      "name",
//				MountPath: "mountPath",
//				ReadOnly:  true,
//			}
//			result := BuildCodeVolumeMount(storage)
//
//			convey.So(reflect.DeepEqual(result, expect), convey.ShouldBeTrue)
//		})
//	})
//}
