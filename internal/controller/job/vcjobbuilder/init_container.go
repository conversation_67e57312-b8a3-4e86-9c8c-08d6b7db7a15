package vcjobbuilder

import (
	"fmt"
	"strings"
	"sync"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	v1 "k8s.io/api/core/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

var (
	TerminalMode        = []string{"sh", "-c"}
	initSupervisorName  = "init-supervisor"
	gitPullImage        = "registry.cnbita.com:5000/cloud-images/hero-user/git:2.34.5"
	initSupervisorImage = "registry.cnbita.com:5000/cloud-images/hero-user/initsupervisor:latest"
	supervisorCmd       = "/usr/bin/supervisord -c /etc/supervisord.ini"
	appToCmd            = map[string]string{}
	envMap              = map[string]string{}
)

func NewTaskInitContainers() *InitContainers {
	return &InitContainers{}
}

var appMutex sync.Mutex

func RegisterCmd(pluginName, cmd string) {
	appMutex.Lock()
	defer appMutex.Unlock()
	appToCmd[pluginName] = cmd
}

func RegisterEnv(name, value string) {
	appMutex.Lock()
	defer appMutex.Unlock()
	envMap[name] = value
}

func InitRegisterConfig() {
	appToCmd = make(map[string]string)
	envMap = make(map[string]string)
}

type InitContainers struct {
}

// 创建 init containers.
//  1. 如果需要 git clone 代码, 则创建 code-init initContainer;
func (ic *InitContainers) Build(code *systemv1alpha1.CodeSource) []*v1.Container {
	var containers []*v1.Container

	if isGitCode(code) {
		containers = append(containers, buildGitCodeInitContainer(code))
	}

	return containers
}

func getGitCommand(codeSource *systemv1alpha1.CodeSource) string {
	var cmd string
	if len(codeSource.AccessToken) != 0 {
		var gitArr []string
		if strings.Contains(codeSource.GitUrl, systemv1alpha1.PrefixGitHttpsRepo) {
			gitArr = strings.Split(codeSource.GitUrl, systemv1alpha1.PrefixGitHttpsRepo)
			cmd = "git clone https://"
		} else {
			gitArr = strings.Split(codeSource.GitUrl, systemv1alpha1.PrefixGitHttpRepo)
			cmd = "git clone http://"
		}
		if len(codeSource.AccessName) == 0 {
			codeSource.AccessName = "oauth2"
		}
		cmd += fmt.Sprintf("%s:%s@%s ", codeSource.AccessName, codeSource.AccessToken, gitArr[1])
	} else {
		cmd = fmt.Sprintf("git clone %s ", codeSource.GitUrl)
	}

	if len(codeSource.Branch) > 0 {
		cmd += fmt.Sprintf("-b %s", codeSource.Branch)
	}

	return cmd
}

func buildGitCodeInitContainer(code *systemv1alpha1.CodeSource) *v1.Container {
	var gitImage = gitPullImage
	if len(config.SC.GitImage) != 0 {
		gitImage = config.SC.GitImage
	}
	initContainer := v1.Container{
		Image: gitImage,
		Name:  "code-init",
	}

	initContainer.VolumeMounts = append(initContainer.VolumeMounts, *BuildCodeVolumeMount(code))
	mountPath := code.MountPath
	initContainer.Args = append(initContainer.Args, TerminalMode...)
	initContainer.Args = append(initContainer.Args, buildGitArg(code))
	initContainer.WorkingDir = mountPath

	return &initContainer
}

func isGitCode(code *systemv1alpha1.CodeSource) bool {
	return len(code.GitUrl) > 0
}

func buildGitArg(code *systemv1alpha1.CodeSource) string {
	arg := fmt.Sprintf(`set -e; %s`, getGitCommand(code))
	return arg
}

func BuildVcjobExtendCap(vcjob *batchvc.Job, command, initImage string) *batchvc.Job {
	if len(initImage) == 0 {
		initImage = initSupervisorImage
	}
	for k, task := range vcjob.Spec.Tasks {
		buildMulVolume(&vcjob.Spec.Tasks[k].Template.Spec.Volumes)
		if len(task.Template.Spec.InitContainers) == 0 {
			vcjob.Spec.Tasks[k].Template.Spec.InitContainers = []v1.Container{}
		}
		vcjob.Spec.Tasks[k].Template.Spec.InitContainers = append(vcjob.Spec.Tasks[k].Template.Spec.InitContainers, *buildSupervisorContainer(initImage))
		for kk := range task.Template.Spec.Containers {
			if task.Template.Spec.Containers[kk].Name == containerName {
				continue
			}
			vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].VolumeMounts = append(vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].VolumeMounts,
				*BuildVolumeMount(
					&systemv1alpha1.DataSource{
						Name:      initSupervisorName,
						MountPath: "/usr/bin/supervisord",
						ReadOnly:  false,
					}, "supervisord"),
				*BuildVolumeMount(
					&systemv1alpha1.DataSource{
						Name:      initSupervisorName,
						MountPath: "/etc/supervisord.ini",
						ReadOnly:  false,
					}, "supervisord.ini"),
			)

			if len(vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args) != 0 && len(command) != 0 {
				vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args = append(vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args, "&&", supervisorCmd)
			} else {
				vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args = []string{supervisorCmd} //append(vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args, supervisorCmd)
			}
		}
	}
	return vcjob
}

func BuildVcjobWebterminal(vcjob *batchvc.Job) *batchvc.Job {
	gottyCmd := "/app/gotty/gotty --address 0.0.0.0 --port 8083 --ws-origin '.*' --permit-write --reconnect /bin/bash"
	for k, task := range vcjob.Spec.Tasks {
		buildMulVolume(&vcjob.Spec.Tasks[k].Template.Spec.Volumes)
		if len(task.Template.Spec.InitContainers) == 0 {
			vcjob.Spec.Tasks[k].Template.Spec.InitContainers = []v1.Container{}
		}
		for kk := range task.Template.Spec.Containers {
			if task.Template.Spec.Containers[kk].Name == containerName {
				continue
			}
			if len(vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args) != 0 {
				argslen := len(vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args)
				argsplice := gottyCmd + " & " + vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args[argslen-1]
				vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].Args[argslen-1] = argsplice
			}
		}
	}

	return vcjob
}

func buildSupervisorContainer(initImage string) *v1.Container {
	initContainer := v1.Container{
		Image: initImage,
		Name:  initSupervisorName,
	}

	initContainer.VolumeMounts = append(
		initContainer.VolumeMounts,
		*BuildVolumeMount(
			&systemv1alpha1.DataSource{
				Name:      "init-supervisor",
				MountPath: "/app",
				ReadOnly:  false,
			}, ""),
	)

	var startCmd string
	for name := range appToCmd {
		startCmd += fmt.Sprintf("--%s ", name)
	}

	for name, value := range envMap {
		initContainer.Env = append(initContainer.Env, v1.EnvVar{
			Name:  name,
			Value: value,
		})
	}
	initContainer.Args = append(initContainer.Args, TerminalMode...)
	initContainer.Args = append(initContainer.Args, fmt.Sprintf("/config/initconfig %s && mkdir -p /app/gotty && cp /config/supervisord /app && cp /config/supervisord.ini /app", startCmd))

	return &initContainer
}

func buildMulVolume(volumes *[]v1.Volume) {
	*volumes = append(
		*volumes,
		*buildVolume(
			&systemv1alpha1.DataSource{
				Name: initSupervisorName,
			},
		),
	)
}

func buildVolume(storage *systemv1alpha1.DataSource) *v1.Volume {
	return &v1.Volume{
		Name: storage.Name,
		VolumeSource: v1.VolumeSource{
			EmptyDir: &v1.EmptyDirVolumeSource{},
		},
	}
}
