package vcjobbuilder

import (
	v1 "k8s.io/api/core/v1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

type IVcContainerBuilder interface {
	Build(job *systemv1alpha1.TrainingJob, task *systemv1alpha1.Task) *v1.Container
	BuildNotebook(job *systemv1alpha1.Notebook) *v1.Container
}

type IVcContainersBuilder interface {
	// TODO(yangpengcheng): return []*v1.Container or *[]v1.Container, any differences?
	Build(code *systemv1alpha1.CodeSource) []*v1.Container
}

type IvcSidecarContainerBuilder interface {
	Build(job *systemv1alpha1.Notebook, localURL string) *v1.Container
}
