package vcjobbuilder

import (
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

func TestVolumeConfig_HostPathVolume(t *testing.T) {
	vc := &VolumeConfig{
		Name:         "test-hostpath",
		VolumeSource: HostPathVolumeSource,
		HostPath:     "/tmp",
		HostPathType: v1.HostPathFile,
	}

	volume := vc.Volume()
	assert.NotNil(t, volume)
	assert.Equal(t, "test-hostpath", volume.Name)
	assert.NotNil(t, volume.VolumeSource.HostPath)
	assert.Equal(t, "/tmp", volume.VolumeSource.HostPath.Path)
	assert.Equal(t, &vc.HostPathType, volume.VolumeSource.HostPath.Type)
}

func TestVolumeConfig_EmptyDirVolume(t *testing.T) {
	vc := &VolumeConfig{
		Name:              "test-emptydir",
		VolumeSource:      EmptyDirVolumeSource,
		EmptyDirMedium:    v1.StorageMediumMemory,
		EmptyDirSizeLimit: "1Gi",
	}

	volume := vc.Volume()
	assert.NotNil(t, volume)
	assert.Equal(t, "test-emptydir", volume.Name)
	assert.NotNil(t, volume.VolumeSource.EmptyDir)
	assert.Equal(t, vc.EmptyDirMedium, volume.VolumeSource.EmptyDir.Medium)

	sizeLimit := resource.MustParse(vc.EmptyDirSizeLimit)
	assert.Equal(t, &sizeLimit, volume.VolumeSource.EmptyDir.SizeLimit)
}

func TestVolumeConfig_ConfigMapVolume(t *testing.T) {
	vc := &VolumeConfig{
		Name:          "test-configmap",
		VolumeSource:  "ConfigMap",
		ConfigMapName: "test-config",
	}

	volume := vc.Volume()
	assert.NotNil(t, volume)
	assert.Equal(t, "test-configmap", volume.Name)
	assert.NotNil(t, volume.VolumeSource.ConfigMap)
	assert.Equal(t, "test-config", volume.VolumeSource.ConfigMap.LocalObjectReference.Name)
}

func TestVolumeConfig_Mount(t *testing.T) {
	vc := &VolumeConfig{
		Name: "test-mount",
	}

	mount := vc.Mount("/mnt/test", true)
	assert.NotNil(t, mount)
	assert.Equal(t, "test-mount", mount.Name)
	assert.True(t, mount.ReadOnly)
	assert.Equal(t, "/mnt/test", mount.MountPath)
}
