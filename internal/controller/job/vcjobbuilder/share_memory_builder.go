package vcjobbuilder

import (
	"k8s.io/apimachinery/pkg/api/resource"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"

	"fmt"

	v1 "k8s.io/api/core/v1"
)

func defaultShareMemory(shm string) *systemv1alpha1.DataSource {
	return &systemv1alpha1.DataSource{
		Name:      "mount-share-memory",
		MountPath: "/dev/shm",
		SizeLimit: fmt.Sprintf("%vMi", shm),
		DataType:  systemv1alpha1.ShareMemory,
	}
}

type ShareMemoryStorage struct {
}

func NewShareMemoryBuilder() *ShareMemoryStorage {
	return &ShareMemoryStorage{}
}

func (sms *ShareMemoryStorage) Build(storage *systemv1alpha1.DataSource) *v1.Volume {
	sizeLimit := resource.MustParse(storage.SizeLimit)
	return &v1.Volume{
		Name: storage.Name,
		VolumeSource: v1.VolumeSource{
			EmptyDir: &v1.EmptyDirVolumeSource{
				Medium:    v1.StorageMediumMemory,
				SizeLimit: &sizeLimit,
			},
		},
	}
}
