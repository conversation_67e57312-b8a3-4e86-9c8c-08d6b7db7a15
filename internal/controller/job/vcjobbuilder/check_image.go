package vcjobbuilder

import (
	"fmt"
	"strconv"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
	v1 "k8s.io/api/core/v1"
)

var (
	name             = "check-image"
	mountPath        = "/hero-app/config/"
	defaultDelayTime = 1800
)

func BuildInitContainer(trainingjob *systemv1alpha1.TrainingJob) *v1.Container {
	combinedCommands := fmt.Sprintf("set -e; %s", combineCommands())

	count := 0
	for _, task := range trainingjob.Spec.Tasks {
		count += int(task.Replicas)
	}

	// 构建 initContainer
	initContainer := v1.Container{
		Image:   trainingjob.Spec.ImageUrl,
		Name:    name,
		Command: TerminalMode,
		Env: []v1.EnvVar{
			{
				Name:  "JOB_NAME",
				Value: trainingjob.Name,
			},
			{
				Name: "POD_NAME",
				ValueFrom: &v1.EnvVarSource{
					FieldRef: &v1.ObjectFieldSelector{
						FieldPath: "metadata.name",
					},
				},
			},
			{
				Name:  "POD_COUNT",
				Value: strconv.Itoa(count),
			},
			{
				Name:  "DELAY_TIME",
				Value: strconv.Itoa(getTime()),
			},
		},
		Args: []string{
			combinedCommands,
		},
	}

	initContainer.VolumeMounts = append(initContainer.VolumeMounts, *buildCommonVolumeMount())
	return &initContainer
}

func buildCommonVolumeMount() *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      defaultMountName,
		MountPath: mountPath,
	}
}

func combineCommands() string {
	cmd := `
	if [ -z "$POD_NAME" ]; then
      echo "Environment variable POD_NAME is not set." >&2
      exit 1
    fi
    if [ -z "$JOB_NAME" ] || [ -z "$DELAY_TIME" ] || [ -z "$POD_COUNT" ]; then
      echo "Error: JOB_NAME, DELAY_TIME, or POD_COUNT not set." >&2
      exit 1
    fi

	job_name="$JOB_NAME"
	lock_path="/hero-app/config/$job_name.lock"
	target_file="/hero-app/config/$job_name"

	[ -f "$target_file" ] || touch "$target_file"
	while ! mkdir "$lock_path" 2>/dev/null; do
      sleep 1
    done
    echo "$POD_NAME" >> "$target_file"
    rmdir "$lock_path"

	timeout_seconds="$DELAY_TIME"
	start_time=$(date +%s)
	loop_flag="/tmp/loop_flag_$$"

	while true; do
	  : > "$loop_flag"
	    if [ -f "$target_file" ]; then
	      line_count=$(grep -cv '^[[:space:]]*$' "$target_file")
	      echo "[DEBUG] Current line count: $line_count" >&2
	      current_time=$(date +%s)
	      elapsed=$((current_time - start_time))
	      if [ "$line_count" -ge "$POD_COUNT" ]; then
	        echo "Line count reached $POD_COUNT. Exiting." >&2
	        echo "break" > "$loop_flag"
	      elif [ "$elapsed" -ge "$timeout_seconds" ]; then
	        echo "Timeout $timeout_seconds seconds reached. Exiting." >&2
	        echo "break" > "$loop_flag"
	      fi
	    else
	      echo "File $target_file missing. Exiting." >&2
	      echo "break" > "$loop_flag"
	    fi

	  if grep -q "break" "$loop_flag"; then
	    rm -f "$loop_flag"
	    break
	  fi

	  sleep 5
	done

	echo "Exit successfully."
	exit 0
	`

	return cmd
}

func getTime() int {
	if config.SC.ImageDelayTime == 0 {
		return defaultDelayTime
	}
	return config.SC.ImageDelayTime
}
