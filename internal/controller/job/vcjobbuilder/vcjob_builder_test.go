package vcjobbuilder

import (
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

// Mock for ITaskBuilder
type MockTaskBuilder struct {
	mock.Mock
}

func (m *MockTaskBuilder) CreateTask(task *systemv1alpha1.Task, trainjob *systemv1alpha1.TrainingJob, secretName, resourcepool string) *batchvc.TaskSpec {
	args := m.Called(task, trainjob, secretName)
	return args.Get(0).(*batchvc.TaskSpec)
}

func (m *MockTaskBuilder) CreateTaskByNotebook(job *systemv1alpha1.Notebook, secretName, localURL string) *batchvc.TaskSpec {
	args := m.Called(job, secretName, localURL)
	return args.Get(0).(*batchvc.TaskSpec)
}

func TestNewVcJobBuilder(t *testing.T) {
	vjcb := NewVcJobBuilder()
	assert.NotNil(t, vjcb)
	assert.NotNil(t, vjcb.ITaskBuilder)
}

func TestBuildByNotebook(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}

	scheme := &runtime.Scheme{}
	secretName := "test-secret"
	localURL := "http://localhost"

	mockTaskBuilder.On("CreateTaskByNotebook", job, secretName, localURL).Return(&batchvc.TaskSpec{})

	vcJob := vjcb.BuildByNotebook(job, scheme, secretName, localURL)

	assert.NotNil(t, vcJob)
	assert.Equal(t, "nb-test-notebook", vcJob.Name)
	assert.Equal(t, "default", vcJob.Namespace)
}

func TestVcjobBuilderBuild(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	trainjob := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-trainingjob",
			Namespace: "default",
		},
	}

	scheme := &runtime.Scheme{}
	secretName := "test-secret"

	mockTaskBuilder.On("CreateTask", mock.Anything, trainjob, secretName).Return(&batchvc.TaskSpec{})

	vcJob := vjcb.Build(trainjob, scheme, secretName)

	assert.NotNil(t, vcJob)
	assert.Equal(t, "tj-test-trainingjob", vcJob.Name)
	assert.Equal(t, "default", vcJob.Namespace)
}

func TestBuildCommonConfig(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	vcJob := &batchvc.Job{}
	vjcb.buildCommonConfig("test-job", "default", "default", "notebook", 0, nil, vcJob)

	assert.Equal(t, "default", vcJob.Namespace)
	assert.Equal(t, int32(604800), *vcJob.Spec.TTLSecondsAfterFinished)
	assert.Equal(t, int32(0), vcJob.Spec.MaxRetry)
	assert.Equal(t, DefaultSchedulerName, vcJob.Spec.SchedulerName)
}

func TestBuildQueue(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	vcJob := &batchvc.Job{}
	vjcb.buildQueue("default-queue", vcJob)

	assert.Equal(t, "default-queue", vcJob.Spec.Queue)
}

func TestBuildJobTasks(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	trainjob := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{Name: "task1"},
			},
		},
	}

	vcJob := &batchvc.Job{}

	mockTaskBuilder.On("CreateTask", mock.Anything, trainjob, "test-secret").Return(&batchvc.TaskSpec{})

	vjcb.buildJobTasks(trainjob, vcJob, "test-secret")

	assert.Equal(t, 1, len(vcJob.Spec.Tasks))
}

func TestBuildJobTasksByNotebook(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	job := &systemv1alpha1.Notebook{}
	vcJob := &batchvc.Job{}

	mockTaskBuilder.On("CreateTaskByNotebook", job, "test-secret", "http://localhost").Return(&batchvc.TaskSpec{})

	vjcb.buildJobTasksByNotebook(job, vcJob, "test-secret", "http://localhost")

	assert.Equal(t, 1, len(vcJob.Spec.Tasks))
}

func TestBuildPlugin(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	vcJob := &batchvc.Job{}
	vjcb.buildPlugin(nil, vcJob)

	assert.NotNil(t, vcJob.Spec.Plugins)
	assert.NotNil(t, vcJob.Spec.Plugins[PluginEnvName])
	assert.NotNil(t, vcJob.Spec.Plugins[PluginSvcName])
	assert.NotNil(t, vcJob.Spec.Plugins[SSH])
}

func TestBuildLabels(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	vcJob := &batchvc.Job{}
	vjcb.buildLables("test-job", "default", "notebook", vcJob)

	assert.Equal(t, "test-job", vcJob.Labels[systemv1alpha1.VcjobNameLabels])
	assert.Equal(t, "default", vcJob.Labels[systemv1alpha1.VcjobNsLabels])
	assert.Equal(t, HeroJobVendor, vcJob.Labels[LabelVendor])
	assert.Equal(t, "notebook", vcJob.Labels[LabelJobKind])
}

func TestBuildMinAvailable(t *testing.T) {
	mockTaskBuilder := new(MockTaskBuilder)
	vjcb := &vcjobBuilder{
		ITaskBuilder: mockTaskBuilder,
	}

	job := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{MinAvaluable: 1},
				{MinAvaluable: 2},
			},
		},
	}

	vcJob := &batchvc.Job{}
	vjcb.buildMinAvailable(job, vcJob)

	assert.Equal(t, int32(3), vcJob.Spec.MinAvailable)
}
