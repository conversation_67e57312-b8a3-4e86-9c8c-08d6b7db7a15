package vcjobbuilder

import (
	"github.com/stretchr/testify/assert"
	"hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"testing"
)

func TestDefaultShareMemory(t *testing.T) {
	shm := "256" // 设置共享内存的大小
	dataSource := defaultShareMemory(shm)

	assert.NotNil(t, dataSource)
	assert.Equal(t, "mount-share-memory", dataSource.Name)
	assert.Equal(t, "/dev/shm", dataSource.MountPath)
	assert.Equal(t, "256Mi", dataSource.SizeLimit)
	assert.Equal(t, v1alpha1.ShareMemory, dataSource.DataType)
}

func TestNewShareMemoryBuilder(t *testing.T) {
	builder := NewShareMemoryBuilder()
	assert.NotNil(t, builder)
}

func TestBuildShareMemoryStorage(t *testing.T) {
	storage := &v1alpha1.DataSource{
		Name:      "mount-share-memory",
		MountPath: "/dev/shm",
		SizeLimit: "256Mi", // 这里要注意要符合格式
		DataType:  v1alpha1.ShareMemory,
	}

	builder := NewShareMemoryBuilder()
	volume := builder.Build(storage)

	assert.NotNil(t, volume)
	assert.Equal(t, storage.Name, volume.Name)
	assert.NotNil(t, volume.VolumeSource.EmptyDir)
	assert.Equal(t, v1.StorageMediumMemory, volume.VolumeSource.EmptyDir.Medium)

	// 验证 SizeLimit 是否正确解析
	expectedSizeLimit := resource.MustParse(storage.SizeLimit)
	assert.Equal(t, expectedSizeLimit.String(), volume.VolumeSource.EmptyDir.SizeLimit.String())
}

//import (
//	"reflect"
//	"testing"
//
//	"github.com/smartystreets/goconvey/convey"
//	v1 "k8s.io/api/core/v1"
//	"k8s.io/apimachinery/pkg/api/resource"
//
//	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
//)
//
//func TestShareMemoryStorage_Build(t *testing.T) {
//	convey.Convey("测试共享内存创建", t, func() {
//		convey.Convey("共享内存创建成功", func() {
//			smb := NewShareMemoryBuilder()
//			storage := &systemv1alpha1.DataSource{
//				Name:      "name",
//				SizeLimit: "100",
//			}
//			sizeLimit := resource.MustParse(storage.SizeLimit)
//			expect := &v1.Volume{
//				Name: "share-memory-" + storage.Name,
//				VolumeSource: v1.VolumeSource{
//					EmptyDir: &v1.EmptyDirVolumeSource{
//						Medium:    v1.StorageMediumMemory,
//						SizeLimit: &sizeLimit,
//					},
//				},
//			}
//			result := smb.Build(storage)
//
//			convey.So(reflect.DeepEqual(result, expect), convey.ShouldBeTrue)
//		})
//	})
//}
