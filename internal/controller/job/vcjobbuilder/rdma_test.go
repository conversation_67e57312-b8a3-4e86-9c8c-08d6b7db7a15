package vcjobbuilder

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"hero.ai/hero-controllers/internal/controller/config"
	corev1 "k8s.io/api/core/v1"
	batch "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

func TestNewRDMABuilder(t *testing.T) {
	tests := []struct {
		name string
		cfg  config.RDMA
	}{
		{
			name: "With NCCL and HCCL Envs",
			cfg: config.RDMA{
				SecondaryNetwork: "roce-network",
				NCCLEnvs:         []string{"NCCL_DEBUG=INFO", "NCCL_SOCKET_IFNAME=eth0"},
				HCCLEnvs:         []string{"HCCL_DEBUG=INFO"},
			},
		},
		{
			name: "Without NCCL and HCCL Envs",
			cfg: config.RDMA{
				SecondaryNetwork: "roce-network",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			builder := NewRDMABuilder(tt.cfg)
			assert.NotNil(t, builder)
			rb, ok := builder.(*RDMABuilder)
			assert.True(t, ok)
			assert.Equal(t, tt.cfg.SecondaryNetwork, rb.roceNetwork)
			assert.Len(t, rb.ncclEnvs, len(tt.cfg.NCCLEnvs))
			assert.Len(t, rb.hcclEnvs, len(tt.cfg.HCCLEnvs))
		})
	}
}

func TestRDMABuilder_IsSatisfied(t *testing.T) {
	builder := &RDMABuilder{}
	jobReq := struct{}{}
	vcJob := &batch.Job{}

	assert.True(t, builder.IsSatisfied(jobReq, vcJob))
}

func TestRDMABuilder_PostBuild(t *testing.T) {
	tests := []struct {
		name            string
		cfg             config.RDMA
		taskResources   corev1.ResourceList
		expectedRoceNet string
	}{
		{
			name: "RoCE Network Injection",
			cfg: config.RDMA{
				SecondaryNetwork: "roce-network",
			},
			taskResources: corev1.ResourceList{
				ResourceNameRoCE: {},
			},
			expectedRoceNet: "roce-network",
		},
		{
			name: "No RoCE Network Injection",
			cfg: config.RDMA{
				SecondaryNetwork: "roce-network",
			},
			taskResources:   corev1.ResourceList{},
			expectedRoceNet: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			builder := NewRDMABuilder(tt.cfg).(*RDMABuilder)
			vcJob := &batch.Job{
				Spec: batch.JobSpec{
					Tasks: []batch.TaskSpec{
						{
							Template: corev1.PodTemplateSpec{
								Spec: corev1.PodSpec{
									Containers: []corev1.Container{
										{
											Resources: corev1.ResourceRequirements{
												Requests: tt.taskResources,
											},
										},
									},
								},
							},
						},
					},
				},
			}

			builder.PostBuild(nil, vcJob)

			if tt.expectedRoceNet != "" {
				assert.NotNil(t, vcJob.Spec.Tasks[0].Template.Annotations)
				assert.Equal(t, tt.expectedRoceNet, vcJob.Spec.Tasks[0].Template.Annotations[MultusCNIAnnotationKey])
			} else {
				assert.Nil(t, vcJob.Spec.Tasks[0].Template.Annotations)
			}
		})
	}
}

func TestRDMABuilder_protocolTypeAndInjectEnvs(t *testing.T) {
	tests := []struct {
		name           string
		cfg            config.RDMA
		taskResources  corev1.ResourceList
		expectedRName  string
		expectedEnvLen int
	}{
		{
			name: "Infiniband Resource with NCCL Envs",
			cfg: config.RDMA{
				NCCLEnvs: []string{"NCCL_DEBUG=INFO"},
			},
			taskResources: corev1.ResourceList{
				ResourceNameInfiniband: {},
				"nvidia.com/gpu":       {},
			},
			expectedRName:  ResourceNameInfiniband,
			expectedEnvLen: 1,
		},
		{
			name: "RoCE Resource with HCCL Envs",
			cfg: config.RDMA{
				HCCLEnvs: []string{"HCCL_DEBUG=INFO"},
			},
			taskResources: corev1.ResourceList{
				ResourceNameRoCE: {},
				"ascend.ai/gpu":  {},
			},
			expectedRName:  ResourceNameRoCE,
			expectedEnvLen: 1,
		},
		{
			name: "No Matching Resource",
			cfg:  config.RDMA{},
			taskResources: corev1.ResourceList{
				"custom-resource": {},
			},
			expectedRName:  "",
			expectedEnvLen: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			builder := NewRDMABuilder(tt.cfg).(*RDMABuilder)
			task := &batch.TaskSpec{
				Template: corev1.PodTemplateSpec{
					Spec: corev1.PodSpec{
						Containers: []corev1.Container{
							{
								Resources: corev1.ResourceRequirements{
									Requests: tt.taskResources,
								},
							},
						},
					},
				},
			}

			rName := builder.protocolTypeAndInjectEnvs(task)

			assert.Equal(t, tt.expectedRName, rName)
			assert.Len(t, task.Template.Spec.Containers[0].Env, tt.expectedEnvLen)
		})
	}
}
