package vcjobbuilder

import (
	"testing"

	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

// Mock for IVcContainerBuilder
type MockContainerBuilder struct {
	mock.Mock
}

func (m *MockContainerBuilder) Build(job *systemv1alpha1.TrainingJob, task *systemv1alpha1.Task) *v1.Container {
	args := m.Called(job, task)
	return args.Get(0).(*v1.Container)
}

func (m *MockContainerBuilder) BuildNotebook(job *systemv1alpha1.Notebook) *v1.Container {
	args := m.Called(job)
	return args.Get(0).(*v1.Container)
}

// Mock for IVcContainersBuilder
type MockContainersBuilder struct {
	mock.Mock
}

func (m *MockContainersBuilder) Build(code *systemv1alpha1.CodeSource) []*v1.Container {
	args := m.Called(code)
	return args.Get(0).([]*v1.Container)
}

// Mock for IVcSidecarContainerBuilder
type MockSidecarContainerBuilder struct {
	mock.Mock
}

func (m *MockSidecarContainerBuilder) Build(job *systemv1alpha1.Notebook, localURL string) *v1.Container {
	args := m.Called(job, localURL)
	return args.Get(0).(*v1.Container)
}

// Test for NewVcjobTaskBuilder
func TestNewVcjobTaskBuilder(t *testing.T) {
	vtb := NewVcjobTaskBuilder()
	assert.NotNil(t, vtb)
	assert.NotNil(t, vtb.BusinessContainerBuiler)
	assert.NotNil(t, vtb.InitContainersBuiler)
	assert.NotNil(t, vtb.SidecarContinersBuilder)
}

// Test for CreateTaskByNotebook
func TestCreateTaskByNotebook(t *testing.T) {
	mockBusinessBuilder := new(MockContainerBuilder)
	mockInitBuilder := new(MockContainersBuilder)
	mockSidecarBuilder := new(MockSidecarContainerBuilder)

	vtb := &VcjobTaskBuilder{
		BusinessContainerBuiler: mockBusinessBuilder,
		InitContainersBuiler:    mockInitBuilder,
		SidecarContinersBuilder: mockSidecarBuilder,
	}

	job := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:        "test-notebook",
			Namespace:   "default",
			Labels:      map[string]string{"key": "value"},
			Annotations: map[string]string{LocalDiskCacheAnnotationKey: "true"},
		},
		Spec: systemv1alpha1.NotebookSpec{
			CodeSource: systemv1alpha1.CodeSource{GitUrl: "http://example.com"},
			DataSources: []systemv1alpha1.DataSource{
				{VolumeName: "VolumeName", DataType: "Volume"},
			},
			ExtendResource: systemv1alpha1.ExtendResource{},
		},
	}
	secretName := "test-secret"
	localURL := "http://localhost"

	mockBusinessBuilder.On("BuildNotebook", job).Return(&v1.Container{Name: "business-container"})
	mockInitBuilder.On("Build", &job.Spec.CodeSource).Return([]*v1.Container{{Name: "init-container"}})
	mockSidecarBuilder.On("Build", job, localURL).Return(&v1.Container{Name: "sidecar-container"})

	taskSpec := vtb.CreateTaskByNotebook(job, secretName, localURL)

	assert.NotNil(t, taskSpec)
	assert.Equal(t, "test-notebook", taskSpec.Name)
	assert.Equal(t, int32(1), taskSpec.Replicas)
	assert.Len(t, taskSpec.Template.Spec.Containers, 2) // business, init, and sidecar containers
}

// Test for CreateTask
func TestCreateTask(t *testing.T) {
	mockBusinessBuilder := new(MockContainerBuilder)
	mockInitBuilder := new(MockContainersBuilder)

	vtb := &VcjobTaskBuilder{
		BusinessContainerBuiler: mockBusinessBuilder,
		InitContainersBuiler:    mockInitBuilder,
	}

	task := &systemv1alpha1.Task{
		Name:           "test-task",
		Replicas:       1,
		MinAvaluable:   1,
		ExtendResource: systemv1alpha1.ExtendResource{},
		Resource: v1.ResourceList{
			v1.ResourceCPU:    resource.MustParse("1"),
			v1.ResourceMemory: resource.MustParse("1Gi"),
		},
	}
	job := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
			Labels:    map[string]string{"key": "value"},
		},
		Spec: systemv1alpha1.TrainingJobSpec{
			CodeSource: systemv1alpha1.CodeSource{GitUrl: "http://example.com"},
			Tasks:      []systemv1alpha1.Task{*task},
		},
	}
	secretName := "test-secret"
	resourcePool := "test"
	mockBusinessBuilder.On("Build", job, task).Return(&v1.Container{Name: "business-container"})
	mockInitBuilder.On("Build", &job.Spec.CodeSource).Return([]*v1.Container{{Name: "init-container"}})

	taskSpec := vtb.CreateTask(task, job, secretName, resourcePool)

	assert.NotNil(t, taskSpec)
	assert.Equal(t, "test-task", taskSpec.Name)
	assert.Equal(t, task.Replicas, taskSpec.Replicas)
	assert.Len(t, taskSpec.Template.Spec.Containers, 1) // only business container
}

// Test for buildSidecarContainer
func TestBuildSidecarContainer(t *testing.T) {
	mockBusinessBuilder := new(MockContainerBuilder)
	mockSidecarBuilder := new(MockSidecarContainerBuilder)

	vtb := &VcjobTaskBuilder{
		BusinessContainerBuiler: mockBusinessBuilder,
		SidecarContinersBuilder: mockSidecarBuilder,
	}

	job := &systemv1alpha1.Notebook{}
	taskSpec := &batchvc.TaskSpec{}

	mockSidecarBuilder.On("Build", job, "http://localhost").Return(&v1.Container{Name: "sidecar-container"})

	vtb.buildSidecarContainer(job, taskSpec, "http://localhost")

	assert.Len(t, taskSpec.Template.Spec.Containers, 1) // sidecar container added
}

// Test for buildNodeSelector
func TestBuildNodeSelector(t *testing.T) {
	vtb := &VcjobTaskBuilder{}

	taskSpec := &batchvc.TaskSpec{
		Template: v1.PodTemplateSpec{
			Spec: v1.PodSpec{
				NodeSelector: make(map[string]string),
			},
		},
	}

	extendResource := &systemv1alpha1.ExtendResource{}
	resource := &v1.ResourceList{}
	rp := "example-role"
	localDisk := "true"

	vtb.buildNodeSelector(extendResource, resource, taskSpec, rp, localDisk)

	assert.Equal(t, "true", taskSpec.Template.Spec.NodeSelector[LabelTrainingNodeRole])
	assert.Equal(t, rp, taskSpec.Template.Spec.NodeSelector[systemv1alpha1.NodeLabelKey])
}

// Test for buildRestartPolicy
func TestBuildRestartPolicy(t *testing.T) {
	vtb := &VcjobTaskBuilder{}

	taskSpec := &batchvc.TaskSpec{}

	vtb.buildRestartPolicy(taskSpec)

	assert.Equal(t, v1.RestartPolicy(v1.RestartPolicyNever), taskSpec.Template.Spec.RestartPolicy)
}

// Test for buildAnnotations
func TestBuildAnnotations(t *testing.T) {
	vtb := &VcjobTaskBuilder{}

	taskSpec := &batchvc.TaskSpec{
		Template: v1.PodTemplateSpec{
			ObjectMeta: metav1.ObjectMeta{
				Annotations: make(map[string]string),
			},
		},
	}
	tasks := []systemv1alpha1.Task{
		{Name: "task1"},
		{Name: "task2"},
	}

	vtb.buildAnnotations(tasks, taskSpec)

	assert.Equal(t, "task1,task2", taskSpec.Template.Annotations[VolcanoTaskTopologyAffinity])
	assert.Equal(t, "task1,task2", taskSpec.Template.Annotations[VolcanoTaskTopologyOrder])
	assert.Equal(t, StorageManagedAnnotationValue, taskSpec.Template.Annotations[StorageManagedAnnotationKey])
}

// Test for podVolume
func TestPodVolume(t *testing.T) {
	vtb := &VcjobTaskBuilder{}

	volumes := []v1.Volume{}
	codeSource := &systemv1alpha1.CodeSource{GitUrl: "http://example.com"}
	datasources := []systemv1alpha1.DataSource{
		{VolumeName: "VolumeName", DataType: "Volume"},
	}

	result := vtb.podVolume(codeSource, datasources, "", &volumes)

	assert.Len(t, *result, 3) // code and data source volumes
}

// Test for buildInitContainers
func TestBuildInitContainers(t *testing.T) {
	mockInitBuilder := new(MockContainersBuilder)

	vtb := &VcjobTaskBuilder{
		InitContainersBuiler: mockInitBuilder,
	}

	taskSpec := &batchvc.TaskSpec{}
	codeSource := &systemv1alpha1.CodeSource{}

	mockInitBuilder.On("Build", codeSource).Return([]*v1.Container{
		{Name: "init-container"},
	})

	vtb.buildInitContainers(codeSource, taskSpec)

	assert.Len(t, taskSpec.Template.Spec.InitContainers, 1)
}

// Test for buildBusinessContainer
func TestBuildBusinessContainer(t *testing.T) {
	mockBusinessBuilder := new(MockContainerBuilder)

	vtb := &VcjobTaskBuilder{
		BusinessContainerBuiler: mockBusinessBuilder,
	}

	taskSpec := &batchvc.TaskSpec{}
	job := &systemv1alpha1.TrainingJob{}
	task := &systemv1alpha1.Task{}

	mockBusinessBuilder.On("Build", job, task).Return(&v1.Container{Name: "business-container"})

	vtb.buildBusinessContainer(job, task, taskSpec)

	assert.Len(t, taskSpec.Template.Spec.Containers, 1)
}

// Test for buildBusinessContainerByNotebook
func TestBuildBusinessContainerByNotebook(t *testing.T) {
	mockBusinessBuilder := new(MockContainerBuilder)

	vtb := &VcjobTaskBuilder{
		BusinessContainerBuiler: mockBusinessBuilder,
	}

	taskSpec := &batchvc.TaskSpec{}
	job := &systemv1alpha1.Notebook{}

	mockBusinessBuilder.On("BuildNotebook", job).Return(&v1.Container{Name: "business-container"})

	vtb.buildBusinessContainerByNotebook(job, taskSpec)

	assert.Len(t, taskSpec.Template.Spec.Containers, 1)
}

// Test for isNoGpuResource
func TestIsNoGpuResource(t *testing.T) {
	resourceWithGpu := v1.ResourceList{
		v1.ResourceCPU:                    {},
		v1.ResourceMemory:                 {},
		v1.ResourceName("nvidia.com/gpu"): {},
	}
	resourceWithoutGpu := v1.ResourceList{
		v1.ResourceCPU:    {},
		v1.ResourceMemory: {},
	}

	assert.False(t, isNoGpuResource(resourceWithGpu))
	assert.True(t, isNoGpuResource(resourceWithoutGpu))
}
