package vcjobbuilder

import (
	"strconv"
	"strings"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"

	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const (
	TaskSriovAnnotations        = "k8s.v1.cni.cncf.io/networks"
	VolcanoTaskTopologyAffinity = "volcano.sh/task-topology-affinity"
	VolcanoTaskTopologyOrder    = "volcano.sh/task-topology-task-order"
	LabelTrainingNodeRole       = "node-role.kubernetes.io/training"
	LabelCPUNodeRole            = "node-role.kubernetes.io/cpu"
	LabelLocalDiskNodeRole      = "node-role.kubernetes.io/local-disk"

	StorageManagedAnnotationKey   = "leinao.ai/storage-managed"
	StorageManagedAnnotationValue = "true"

	LocalDiskCacheAnnotationKey = "leinao.ai/local-disk-cache"
	LocalDiskQuotaAnnotationKey = "leinao.ai/local-disk-quota"

	LocalDiskQuotaEnvKey = "LOCAL_DISK_QUOTA"

	UserContainerName = "main"
	// 挂载宿主机时区到容器
	// LocaltimeVolumeName = "mount-localtime"
	// LocaltimeHostPath   = "/etc/localtime"
	// LocaltimeMountPath  = "/etc/localtime"
	// TimezoneVolumeName  = "mount-timezone"
	// TimezoneHostPath    = "/etc/timezone"
	// TimezoneMountPath   = "/etc/timezone"
	// ZoneinfoMountPath   = "/usr/share/zoneinfo/Asia/Shanghai"
)

type VcjobTaskBuilder struct {
	BusinessContainerBuiler IVcContainerBuilder
	InitContainersBuiler    IVcContainersBuilder
	SidecarContinersBuilder IvcSidecarContainerBuilder
}

func NewVcjobTaskBuilder() *VcjobTaskBuilder {
	return &VcjobTaskBuilder{
		BusinessContainerBuiler: NewTaskNormalContainer(),
		InitContainersBuiler:    NewTaskInitContainers(),
		SidecarContinersBuilder: NewSidecarContainer(),
	}
}

func (vtb *VcjobTaskBuilder) CreateTaskByNotebook(job *systemv1alpha1.Notebook, secretName, localURL string) *batchvc.TaskSpec {
	trueVal := true
	falseVal := false
	var minAvailable int32 = 1
	taskSpec := &batchvc.TaskSpec{}
	taskSpec.Template.Annotations = make(map[string]string)
	taskSpec.Template.Spec.NodeSelector = make(map[string]string)
	taskSpec.Template.Labels = make(map[string]string)
	taskSpec.Template.Labels = job.Labels
	taskSpec.Template.Spec.ShareProcessNamespace = &trueVal
	taskSpec.Name = job.Name
	taskSpec.Replicas = 1
	taskSpec.MinAvailable = &minAvailable
	taskSpec.Template.Spec.EnableServiceLinks = &falseVal

	if len(secretName) > 0 {
		taskSpec.Template.Spec.ImagePullSecrets = []v1.LocalObjectReference{{Name: secretName}}
	}

	vtb.buildAnnotations([]systemv1alpha1.Task{}, taskSpec)
	vtb.buildNodeSelector(&job.Spec.ExtendResource, &job.Spec.Resource, taskSpec, job.GetLabels()[systemv1alpha1.NodeLabelKey], job.GetAnnotations()[LocalDiskCacheAnnotationKey])
	vtb.buildRestartPolicy(taskSpec)

	// Pod Volumes
	volumes := &taskSpec.Template.Spec.Volumes
	vtb.podVolume(&job.Spec.CodeSource, job.Spec.DataSources, job.Spec.ExtendResource.SharedMemory, volumes)

	if job.Annotations[LocalDiskCacheAnnotationKey] == "true" {
		vtb.buildLocalDiskVolume(job.Name, volumes)
	}

	// 任务容器参数加载
	vtb.buildBusinessContainerByNotebook(job, taskSpec)

	// 设置初始化容器
	vtb.buildInitContainers(&job.Spec.CodeSource, taskSpec)

	// 设置Sidecar容器
	vtb.buildSidecarContainer(job, taskSpec, localURL)
	return taskSpec
}

func (vtb *VcjobTaskBuilder) CreateTask(task *systemv1alpha1.Task, job *systemv1alpha1.TrainingJob, secretName, resourcepool string) *batchvc.TaskSpec {
	trueVal := true
	falseVal := false
	taskSpec := &batchvc.TaskSpec{}
	taskSpec.Template.Annotations = make(map[string]string)
	taskSpec.Template.Spec.NodeSelector = make(map[string]string)
	taskSpec.Template.Labels = make(map[string]string)
	taskSpec.Template.Labels = job.Labels
	taskSpec.Template.Labels["fuse.serverful.fluid.io/inject"] = "true"
	taskSpec.Template.Labels[TrainingJobVersionLabel] = strconv.Itoa(int(job.Status.Version))
	taskSpec.Template.Spec.ShareProcessNamespace = &trueVal
	taskSpec.Name = task.Name
	taskSpec.Replicas = task.Replicas
	taskSpec.MinAvailable = &task.MinAvaluable
	taskSpec.Template.Spec.EnableServiceLinks = &falseVal

	if len(secretName) > 0 {
		taskSpec.Template.Spec.ImagePullSecrets = []v1.LocalObjectReference{{Name: secretName}}
	}

	vtb.buildAnnotations(job.Spec.Tasks, taskSpec)
	vtb.buildNodeSelector(&task.ExtendResource, &task.Resource, taskSpec, resourcepool /* job.GetLabels()[systemv1alpha1.NodeLabelKey]*/, job.GetAnnotations()[LocalDiskCacheAnnotationKey])
	vtb.buildRestartPolicy(taskSpec)

	// Pod Volumes
	volumes := &taskSpec.Template.Spec.Volumes
	vtb.podVolume(&job.Spec.CodeSource, job.Spec.DataSources, task.ExtendResource.SharedMemory, volumes)

	// 任务容器参数加载
	vtb.buildBusinessContainer(job, task, taskSpec)

	// 设置初始化容器
	vtb.buildInitContainers(&job.Spec.CodeSource, taskSpec)

	if getReplicasCount(job) > 1 {
		//build volume
		vtb.buildShareVolumes(volumes)
		//build volumemount
		vtb.buildCheckInitContainers(job, taskSpec)
	}

	return taskSpec
}

func (vtb *VcjobTaskBuilder) buildSidecarContainer(job *systemv1alpha1.Notebook, taskSpec *batchvc.TaskSpec, localURL string) {
	sidecarContainer := vtb.SidecarContinersBuilder.Build(job, localURL)
	taskSpec.Template.Spec.Containers = append(taskSpec.Template.Spec.Containers, *sidecarContainer)
}

func isNoGpuResource(resource v1.ResourceList) bool {
	isNoGpuRes := true
	for rName := range resource {
		switch rName {
		case v1.ResourceCPU, v1.ResourceMemory:
			continue
		default:
			isNoGpuRes = false
		}

	}
	return isNoGpuRes
}

func (vtb *VcjobTaskBuilder) buildNodeSelector(extendResource *systemv1alpha1.ExtendResource, resource *v1.ResourceList, taskSpec *batchvc.TaskSpec, rp, localDisk string) {
	taskSpec.Template.Spec.NodeSelector[LabelTrainingNodeRole] = "true"
	taskSpec.Template.Spec.NodeSelector[systemv1alpha1.NodeLabelKey] = rp
	// 设置 NodeSelector 将纯 CPU 计算任务调度到 CPU 节点，不抢占 GPU/MLU/NPU 等节点计算资源。
	// 1. 纯 CPU 计算任务是指没有设置 AccDevice.
	// 2. CPU 节点是指有 "node-role.kubernetes.io/cpu=true" 的 node label 的节点。
	// 3. 目前如果有多个 TaskRoles，每个 task 使用相同的计算卡类型。

	// 无gpu资源，可专调度到cpu节点上
	if isNoGpuResource(*resource) {
		taskSpec.Template.Spec.NodeSelector[LabelCPUNodeRole] = "true"
	}

	// vcTask nodeSelector cpuArch
	if extendResource.CPUArch != "" {
		taskSpec.Template.Spec.NodeSelector["kubernetes.io/arch"] = extendResource.CPUArch
	}
	if localDisk == "true" {
		taskSpec.Template.Spec.NodeSelector[LabelLocalDiskNodeRole] = "true"
	}
}

func (vtb *VcjobTaskBuilder) buildRestartPolicy(taskSpec *batchvc.TaskSpec) {
	taskSpec.Template.Spec.RestartPolicy = v1.RestartPolicyNever
}

func (vtb *VcjobTaskBuilder) buildAnnotations(tasks []systemv1alpha1.Task, taskSpec *batchvc.TaskSpec) {
	var b strings.Builder
	for _, t := range tasks {
		b.WriteString(t.Name)
		b.WriteString(",")
	}
	annotationValue := strings.TrimRight(b.String(), ",")
	taskSpec.Template.Annotations[VolcanoTaskTopologyAffinity] = annotationValue
	taskSpec.Template.Annotations[VolcanoTaskTopologyOrder] = annotationValue
	taskSpec.Template.Annotations[StorageManagedAnnotationKey] = StorageManagedAnnotationValue
}

func (vtb *VcjobTaskBuilder) podVolume(code *systemv1alpha1.CodeSource, datasources []systemv1alpha1.DataSource, shm string, volumes *[]v1.Volume) *[]v1.Volume {
	vtb.buildCodeVolume(code, volumes)
	vtb.buildShareMemeryVolumes(shm, volumes)
	vtb.buildDataSourceVolumes(datasources, volumes)

	return volumes
}

func (vtb *VcjobTaskBuilder) buildCodeVolume(code *systemv1alpha1.CodeSource, volumes *[]v1.Volume) {
	if len(code.GitUrl) > 0 {
		*volumes = append(*volumes, *vtb.buildVolume(defaultGitCode(code)))
	}
}

func (vtb *VcjobTaskBuilder) buildShareMemeryVolumes(shm string, volumes *[]v1.Volume) {
	*volumes = append(*volumes, *vtb.buildVolume(defaultShareMemory(shm)))
}

func (vtb *VcjobTaskBuilder) buildShareVolumes(volumes *[]v1.Volume) {
	*volumes = append(*volumes, *BuildCommonVolume())
}

func (vtb *VcjobTaskBuilder) buildDataSourceVolumes(datasources []systemv1alpha1.DataSource, volumes *[]v1.Volume) {
	var tmpmap = make(map[string]*systemv1alpha1.DataSource)
	for k := range datasources {
		tmpmap[datasources[k].VolumeName] = &datasources[k]
	}
	for k := range tmpmap {
		*volumes = append(*volumes, *vtb.buildVolume(tmpmap[k]))
	}
}

func (vtb *VcjobTaskBuilder) buildLocalDiskVolume(jobName string, volumes *[]v1.Volume) {
	*volumes = append(*volumes, *BuildLocalDiskVolume(jobName))
}

func (vtb *VcjobTaskBuilder) buildVolume(dataSource *systemv1alpha1.DataSource) *v1.Volume {
	return NewStorageBuilderFactory().Get(string(dataSource.DataType)).Build(dataSource)
}

func (vtb *VcjobTaskBuilder) buildInitContainers(code *systemv1alpha1.CodeSource, taskSpec *batchvc.TaskSpec) {
	containers := vtb.InitContainersBuiler.Build(code)
	for _, v := range containers {
		taskSpec.Template.Spec.InitContainers = append(taskSpec.Template.Spec.InitContainers, *v)
	}
}

func (vtb *VcjobTaskBuilder) buildCheckInitContainers(trainingjob *systemv1alpha1.TrainingJob, taskSpec *batchvc.TaskSpec) {
	container := BuildInitContainer(trainingjob)
	taskSpec.Template.Spec.InitContainers = append(taskSpec.Template.Spec.InitContainers, *container)
}

func (vtb *VcjobTaskBuilder) buildBusinessContainer(trainjob *systemv1alpha1.TrainingJob, task *systemv1alpha1.Task, taskSpec *batchvc.TaskSpec) {
	businessContainer := vtb.BusinessContainerBuiler.Build(trainjob, task)
	taskSpec.Template.Spec.Containers = append(taskSpec.Template.Spec.Containers, *businessContainer)
}

func (vtb *VcjobTaskBuilder) buildBusinessContainerByNotebook(job *systemv1alpha1.Notebook, taskSpec *batchvc.TaskSpec) {
	businessContainer := vtb.BusinessContainerBuiler.BuildNotebook(job)
	taskSpec.Template.Spec.Containers = append(taskSpec.Template.Spec.Containers, *businessContainer)
}

func getReplicasCount(job *systemv1alpha1.TrainingJob) int32 {
	var count int32
	for _, task := range job.Spec.Tasks {
		count += task.Replicas
	}

	return count
}
