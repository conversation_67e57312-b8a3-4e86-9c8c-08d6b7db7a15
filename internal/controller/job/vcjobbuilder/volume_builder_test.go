package vcjobbuilder

import (
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
)

func TestVolumeBuilder_Build(t *testing.T) {
	// 创建一个示例 DataSource 对象
	storage := &systemv1alpha1.DataSource{
		Name:       "test-volume",
		VolumeName: "pvc-test",
		ReadOnly:   true,
	}

	// 初始化 VolumeBuilder
	volumeBuilder := NewVolumeBuilder()

	// 调用 Build 方法
	volume := volumeBuilder.Build(storage)

	// 进行断言
	assert.NotNil(t, volume)
	assert.Equal(t, "test-volume", volume.Name)
	assert.NotNil(t, volume.VolumeSource.PersistentVolumeClaim)
	assert.Equal(t, "pvc-test", volume.VolumeSource.PersistentVolumeClaim.ClaimName)
	assert.True(t, volume.VolumeSource.PersistentVolumeClaim.ReadOnly)
}

func TestBuildLocalDiskVolume(t *testing.T) {
	jobName := "testJob"
	hostType := v1.HostPathDirectoryOrCreate
	// 调用 Build 方法
	volume := BuildLocalDiskVolume(jobName)

	// 进行断言
	assert.NotNil(t, volume)
	assert.Equal(t, localDiskMountName, volume.Name)
	assert.NotNil(t, volume.VolumeSource.HostPath)
	assert.Equal(t, &hostType, volume.VolumeSource.HostPath.Type)
}
