package vcjobbuilder

import (
	"strings"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
	batch "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const (
	AscendDriverHostPath              = "/usr/local/Ascend/driver"
	AscendAddOnsHostPath              = "/usr/local/Ascend/add-ons"
	AscendNPULogHostPath              = "/var/log/npu"
	AscendHCCLConfigMountPath         = "/user/serverid/devindex/config"
	AscendRingControllerAtlasLabelKey = "ring-controller.atlas"
	AscendHCCLConfigMapNamePrefix     = "rings-config-"
)

var ringControllerAltasLabelValues = map[string]string{
	"huawei.com/Ascend910":      "ascend-910",
	"huawei.com/Ascend910B":     "ascend-910b",
	"huawei.com/Ascend910-32GB": "ascend-910b",
	"huawei.com/Ascend310":      "ascend-310", // 根据 910 推断出的值，没有找到官方文档
}

// AscendNPUBuilder 对使用华为昇腾 NPU 的作业，添加额外的 volcanojob 配置
type AscendNPUBuilder struct {
}

func NewAscendNPUBuilder() CustomBuilder {
	return &AscendNPUBuilder{}
}

// IsSatisfied 判断 job 是否使用昇腾 NPU
func (ab *AscendNPUBuilder) IsSatisfied(jobReq interface{}, vcJob *batch.Job) bool {
	if vcJob == nil {
		return false
	}
	for _, task := range vcJob.Spec.Tasks {
		for _, container := range task.Template.Spec.Containers {
			for resourceName := range container.Resources.Requests {
				if isAscendNPU(string(resourceName)) {
					return true
				}
			}
		}
	}
	return false
}

func isAscendNPU(resourceName string) bool {
	return strings.Contains(strings.ToLower(resourceName), "ascend")
}

// PostBuild 基于通用配置构造使用华为昇腾 NPU 时 volcano Job 需要的额外配置。
// 参考 https://gitee.com/ascend/mindxdl-deploy
func (ab *AscendNPUBuilder) PostBuild(jobReq interface{}, vcJob *batch.Job) {
	if !ab.IsSatisfied(jobReq, vcJob) {
		return
	}

	for i := range vcJob.Spec.Tasks {
		task := &vcJob.Spec.Tasks[i]
		npu := ab.npuResourceNameOfTask(task)
		value, ok := ringControllerAltasLabelValues[npu]
		if !ok {
			continue
		}

		// 固定配置，与 name 为 rings-config-{jobName} 的 ConfigMap 中的对应 label 的值保持一致
		if vcJob.Labels == nil {
			vcJob.Labels = make(map[string]string)
		}
		vcJob.Labels[AscendRingControllerAtlasLabelKey] = value
		if vcJob.Spec.Tasks[i].Template.Labels == nil {
			vcJob.Spec.Tasks[i].Template.Labels = make(map[string]string)
		}
		vcJob.Spec.Tasks[i].Template.Labels[AscendRingControllerAtlasLabelKey] = value

		ab.buildVolumesAndMounts(task, vcJob)
		ab.buildEnvs(task)
	}
}

func (ab *AscendNPUBuilder) npuResourceNameOfTask(task *batch.TaskSpec) string {
	for _, container := range task.Template.Spec.Containers {
		for resourceName := range container.Resources.Requests {
			if isAscendNPU(string(resourceName)) {
				return string(resourceName)
			}
		}
	}
	return ""
}

func (ab *AscendNPUBuilder) getUserContainer(vcTask *batch.TaskSpec) *corev1.Container {
	var container *corev1.Container
	if len(vcTask.Template.Spec.Containers) == 1 {
		container = &vcTask.Template.Spec.Containers[0]
	} else {
		for i := range vcTask.Template.Spec.Containers {
			cont := &vcTask.Template.Spec.Containers[i]
			if cont.Name == UserContainerName {
				container = cont
				break
			}
		}
	}
	return container
}

func (ab *AscendNPUBuilder) buildVolumesAndMounts(vcTask *batch.TaskSpec, vcJob *batch.Job) {
	container := ab.getUserContainer(vcTask)
	if container == nil {
		klog.Errorf("User container not found")
		return
	}

	volumes := vcTask.Template.Spec.Volumes
	volumeMounts := container.VolumeMounts

	// 1. HCCL ConfigMap rings-config-{jobName}
	ringsConfigVolumeConfig := VolumeConfig{
		Name:          "mount-rings-config",
		VolumeSource:  ConfigMapVolumeSource,
		ConfigMapName: AscendHCCLConfigMapNamePrefix + vcJob.Name,
	}
	volumes = append(volumes, *ringsConfigVolumeConfig.Volume())
	volumeMounts = append(volumeMounts, *ringsConfigVolumeConfig.Mount(AscendHCCLConfigMountPath, true))

	// 2. ascend-driver  hostPath /usr/local/Ascend/driver
	driverVolumeConfig := &VolumeConfig{
		Name:         "mount-ascend-driver",
		VolumeSource: HostPathVolumeSource,
		HostPath:     AscendDriverHostPath,
		HostPathType: corev1.HostPathDirectory, // Host 上 Driver 目录必须存在
	}
	volumes = append(volumes, *driverVolumeConfig.Volume())
	volumeMounts = append(volumeMounts, *driverVolumeConfig.Mount(AscendDriverHostPath, true))

	// 3. ascend-add-ons hostPath /usr/local/Ascend/add-ons
	addOnesVolumeConfig := VolumeConfig{
		Name:         "mount-ascend-add-ons",
		VolumeSource: HostPathVolumeSource,
		HostPath:     AscendAddOnsHostPath,
	}
	volumes = append(volumes, *addOnesVolumeConfig.Volume())
	volumeMounts = append(volumeMounts, *addOnesVolumeConfig.Mount(AscendAddOnsHostPath, true))

	// 4. slog           hostPath /var/log/npu
	npuLogVolumeConfig := VolumeConfig{
		Name:         "mount-ascend-npu-slog",
		VolumeSource: HostPathVolumeSource,
		HostPath:     AscendNPULogHostPath,
		HostPathType: corev1.HostPathDirectoryOrCreate,
	}
	volumes = append(volumes, *npuLogVolumeConfig.Volume())
	volumeMounts = append(volumeMounts, *npuLogVolumeConfig.Mount(AscendNPULogHostPath, false))

	// 5. dshm           emptyDir {medium: Memory, sizeLimit: 16Gi}
	// 断点续训，通用配置已经配置 /dev/shm

	vcTask.Template.Spec.Volumes = volumes
	container.VolumeMounts = volumeMounts
}

func (ab *AscendNPUBuilder) buildEnvs(vcTask *batch.TaskSpec) {
	container := ab.getUserContainer(vcTask)
	if container == nil {
		klog.Errorf("User container not found")
		return
	}

	// RANK_TABLE_FILE
	// 即 hccl-controller 生成的 hccl.json 文件
	// 单机多卡和多机分布式训练时需要读取的环境变量
	container.Env = append(container.Env, corev1.EnvVar{
		Name:  "RANK_TABLE_FILE",
		Value: AscendHCCLConfigMountPath + "/hccl.json",
	})

	// XDL_IP
	// 设置环境变量 XDL_IP 为容器所在宿主机的 IP.
	// 由于 hccl-controller 生成的 hccl.json 中 server_id 是 hostIP, 提供 XDL_IP 环境变量，以便
	// 从 hccl.json 文件中解析出当前容器是多节点分布式训练中的第几个节点，以便设置 RANK_ID 环境变量。
	// 同时，由于多机分布式训练每节点都必须为8卡，目前只有8卡服务器，所以不会存在两个容器对应的 server_id 相同的情况。
	// （无语，一旦每个容器允许 2 卡或 4 卡，就可能有冲突了，那么 hccl.json 中 server_id 必须为 podIP.
	// 注意: XDL_IP 仅用来与 hccl.json 中 server_id 比对，来确定节点序号，而不能作为分布式训练多节点通信的 IP.
	container.Env = append(container.Env, corev1.EnvVar{
		Name: "XDL_IP",
		ValueFrom: &corev1.EnvVarSource{
			FieldRef: &corev1.ObjectFieldSelector{
				FieldPath: "status.hostIP",
			},
		},
	})

	// ASCEND_VISIBLE_DEVICES env variable is used by ascend-docker-runtime when in the whole card scheduling scene with volcano scheduler.
	// Please delete it when in the static vNPU scheduling, dynamic vNPU scheduling, volcano without Ascend-volcano-plugin, without volcano scenes.
	// container.Env = append(container.Env, v1.EnvVar{
	//	Name: "ASCEND_VISIBLE_DEVICES",
	//	ValueFrom: &v1.EnvVarSource{
	//		FieldRef: &v1.ObjectFieldSelector{
	//			FieldPath: "metadata.annotations['huawei.com/Ascend910']", // The value must be the same as resources.requests
	//		},
	//	},
	// })
}

// BuildAscendNPUHCCLConfigMap 创建使用华为昇腾 NPU 时需要的 ConfigMap，用于生成 hccl.json 配置文件，挂载到容器中。
//
// 创建的 ConfigMap 示例如下：
//
//	apiVersion: v1
//	kind: ConfigMap
//	metadata:
//	  name: rings-config-mindx-dls-test     # rings-config- 是固定前缀，后面是 volcano jobName
//	  namespace: bita-user
//	  labels:
//	    ring-controller.atlas: ascend-910   # 固定值，对应 Ascend910 NPU
//	data:
//	  hccl.json: |
//	    {
//	        "status":"initializing"
//	    }
func BuildAscendNPUHCCLConfigMap(vcJobName, namespace, ringControllerValue string) *corev1.ConfigMap {
	cmName := AscendHCCLConfigMapNamePrefix + vcJobName
	configMapPara := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      cmName,
			Namespace: namespace,
			Labels:    map[string]string{AscendRingControllerAtlasLabelKey: ringControllerValue},
		},
		Data: map[string]string{"hccl.json": `{"status": "initializing"}`},
	}
	return configMapPara
}
