package vcjobbuilder

import (
	"testing"

	"k8s.io/apimachinery/pkg/api/resource"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"

	"github.com/stretchr/testify/assert"
)

func TestBuildNotebook(t *testing.T) {
	job := &systemv1alpha1.Notebook{
		Spec: systemv1alpha1.NotebookSpec{
			ImageUrl: "test-image",
			Command:  "/bin/bash",
			Resource: v1.ResourceList{
				v1.ResourceCPU:    resource.MustParse("1"),
				v1.ResourceMemory: resource.MustParse("1Gi"),
			},
			CodeSource: systemv1alpha1.CodeSource{
				GitUrl:    "https://github.com/test/repo.git",
				MountPath: "/mnt/code",
			},
		},
	}
	job.SetAnnotations(map[string]string{LocalDiskCacheAnnotationKey: "true"})

	builder := NewTaskNormalContainer()
	container := builder.BuildNotebook(job)

	assert.Equal(t, "test-image", container.Image)
	assert.Equal(t, v1.PullAlways, container.ImagePullPolicy)
	assert.Contains(t, container.Args, "/bin/bash")
	assert.Equal(t, job.Spec.Resource, container.Resources.Requests)
	assert.Equal(t, job.Spec.Resource, container.Resources.Limits)
	assert.Equal(t, "/mnt/code", container.WorkingDir)
	assert.NotNil(t, container.VolumeMounts)
}

func TestBusinessContainerBuild(t *testing.T) {
	job := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			ImageUrl: "training-image",
			Envs: []systemv1alpha1.Env{
				{Name: "TEST_ENV", Value: "test_value"},
			},
			CodeSource: systemv1alpha1.CodeSource{
				GitUrl:    "https://github.com/test/repo.git",
				MountPath: "/mnt/code",
			},
		},
	}

	task := &systemv1alpha1.Task{
		Resource: v1.ResourceList{
			v1.ResourceCPU:    resource.MustParse("2"),
			v1.ResourceMemory: resource.MustParse("4Gi"),
		},
		Command: "run-task",
	}

	builder := NewTaskNormalContainer()
	container := builder.Build(job, task)

	assert.Equal(t, "training-image", container.Image)
	assert.Equal(t, v1.PullAlways, container.ImagePullPolicy)
	assert.Contains(t, container.Args, "run-task")
	assert.Equal(t, task.Resource, container.Resources.Requests)
	assert.Equal(t, task.Resource, container.Resources.Limits)
	assert.Equal(t, "/mnt/code", container.WorkingDir)
	assert.NotNil(t, container.VolumeMounts)
	assert.Contains(t, container.Env, v1.EnvVar{Name: "TEST_ENV", Value: "test_value"})
}

func TestBuildSecurityContext(t *testing.T) {
	builder := NewTaskNormalContainer()
	securityContext := builder.buildSecurityContext()

	assert.NotNil(t, securityContext)
	assert.Contains(t, securityContext.Capabilities.Add, v1.Capability("IPC_LOCK"))
}

func TestSetEnvs(t *testing.T) {
	builder := NewTaskNormalContainer()
	container := &v1.Container{}
	resource := v1.ResourceList{
		v1.ResourceCPU:    resource.MustParse("2"),
		v1.ResourceMemory: resource.MustParse("4Gi"),
	}

	envs := []systemv1alpha1.Env{
		{Name: "TEST_ENV", Value: "test_value"},
	}

	builder.setEnvs(container, &resource, envs)

	assert.NotEmpty(t, container.Env)
	assert.Contains(t, container.Env, v1.EnvVar{Name: "TEST_ENV", Value: "test_value"})
	assert.Contains(t, container.Env, v1.EnvVar{Name: "CURRENT_VC_TASK_NAME", ValueFrom: &v1.EnvVarSource{
		FieldRef: &v1.ObjectFieldSelector{FieldPath: `metadata.labels['volcano.sh/task-spec']`},
	}})
	assert.Contains(t, container.Env, v1.EnvVar{Name: "VC_TASK_NAMES", ValueFrom: &v1.EnvVarSource{
		FieldRef: &v1.ObjectFieldSelector{FieldPath: `metadata.annotations['volcano.sh/task-topology-task-order']`},
	}})
	assert.Contains(t, container.Env, v1.EnvVar{Name: "POD_IP", ValueFrom: &v1.EnvVarSource{
		FieldRef: &v1.ObjectFieldSelector{FieldPath: `status.podIP`},
	}})
	assert.Contains(t, container.Env, v1.EnvVar{Name: "TZ", Value: "Asia/Shanghai"})
}

func TestBuildVolumeMountArray(t *testing.T) {
	builder := NewTaskNormalContainer()
	volumeMounts := []v1.VolumeMount{}
	dataSources := []systemv1alpha1.DataSource{
		{
			VolumeName:    "volume1",
			Name:          "data1",
			VolumeSubPath: "/data1",
		},
		{
			VolumeName:    "volume2",
			Name:          "data2",
			VolumeSubPath: "/data2",
		},
	}

	builder.buildVolumeMountArray(dataSources, &volumeMounts)

	assert.Len(t, volumeMounts, 2)
	assert.Equal(t, "data1", volumeMounts[0].Name)
	assert.Equal(t, "/data1", volumeMounts[0].SubPath)
	assert.Equal(t, "data2", volumeMounts[1].Name)
	assert.Equal(t, "/data2", volumeMounts[1].SubPath)
}
