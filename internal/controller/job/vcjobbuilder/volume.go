package vcjobbuilder

import (
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

type VolumeConfig struct {
	Name              string
	VolumeSource      VolumeSource
	HostPath          string
	HostPathType      v1.HostPathType
	EmptyDirMedium    v1.StorageMedium
	EmptyDirSizeLimit string
	ConfigMapName     string
}

type VolumeSource string

const (
	HostPathVolumeSource  VolumeSource = "HostPath"
	EmptyDirVolumeSource  VolumeSource = "EmptyDir"
	ConfigMapVolumeSource VolumeSource = "ConfigMap"
)

func (vc *VolumeConfig) Volume() *v1.Volume {
	if vc.VolumeSource == HostPathVolumeSource {
		return vc.hostPathVolume()
	} else if vc.VolumeSource == EmptyDirVolumeSource {
		return vc.emptyDirVolume()
	} else {
		return vc.configMapVolume()
	}
}

func (vc *VolumeConfig) hostPathVolume() *v1.Volume {
	return &v1.Volume{
		Name: vc.Name,
		VolumeSource: v1.VolumeSource{
			HostPath: &v1.HostPathVolumeSource{
				Path: vc.HostPath,
				Type: &vc.HostPathType,
			},
		},
	}
}

func (vc *VolumeConfig) configMapVolume() *v1.Volume {
	return &v1.Volume{
		Name: vc.Name,
		VolumeSource: v1.VolumeSource{
			ConfigMap: &v1.ConfigMapVolumeSource{
				LocalObjectReference: v1.LocalObjectReference{
					Name: vc.ConfigMapName,
				},
			},
		},
	}
}

func (vc *VolumeConfig) emptyDirVolume() *v1.Volume {
	sizeLimit := resource.MustParse(vc.EmptyDirSizeLimit)
	return &v1.Volume{
		Name: vc.Name,
		VolumeSource: v1.VolumeSource{
			EmptyDir: &v1.EmptyDirVolumeSource{
				Medium:    vc.EmptyDirMedium,
				SizeLimit: &sizeLimit,
			},
		},
	}
}

func (vc *VolumeConfig) Mount(path string, readOnly bool) *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      vc.Name,
		ReadOnly:  readOnly,
		MountPath: path,
	}
}
