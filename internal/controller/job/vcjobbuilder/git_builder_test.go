package vcjobbuilder

import (
	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"testing"
)

func TestDefaultGitCode(t *testing.T) {
	codeSource := &systemv1alpha1.CodeSource{
		MountPath: "/mnt/git",
	}

	expected := &systemv1alpha1.DataSource{
		Name:      "mount-code",
		MountPath: codeSource.MountPath,
		DataType:  systemv1alpha1.Git,
	}

	result := defaultGitCode(codeSource)
	assert.Equal(t, expected, result)
}

func TestNewEmptyDirBuilder(t *testing.T) {
	builder := NewEmptyDirBuilder()
	assert.NotNil(t, builder)
}

func TestGitBuilderBuild(t *testing.T) {
	storage := &systemv1alpha1.DataSource{
		Name: "test-volume",
	}

	builder := NewEmptyDirBuilder()
	volume := builder.Build(storage)

	assert.NotNil(t, volume)
	assert.Equal(t, storage.Name, volume.Name)
	assert.NotNil(t, volume.VolumeSource.EmptyDir)
}
