package vcjobbuilder

import (
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	batch "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

func TestAscendNPUBuilderIsSatisfied(t *testing.T) {
	tests := []struct {
		vj   *batch.Job
		want bool
	}{
		{
			vj:   generateVcJob(false),
			want: false,
		},
		{
			vj:   generateVcJob(true),
			want: true,
		},
	}
	for _, test := range tests {
		result := NewAscendNPUBuilder().IsSatisfied(nil, test.vj)
		assert.Equal(t, test.want, result)
	}
}

func TestAscendNPUBuilderPostBuild(t *testing.T) {
	tests := []struct {
		vj               *batch.Job
		wantLabel        bool
		wantDriverVolume bool
		wantDriverMount  bool
		wantRankTableEnv bool
	}{
		{
			vj:               generateVcJob(false),
			wantLabel:        false,
			wantDriverVolume: false,
			wantDriverMount:  false,
			wantRankTableEnv: false,
		},
		{
			vj:               generateVcJob(true),
			wantLabel:        true,
			wantDriverVolume: true,
			wantDriverMount:  true,
			wantRankTableEnv: true,
		},
	}

	npuDriverVolumeExist := func(vj *batch.Job) bool {
		for i := range vj.Spec.Tasks[0].Template.Spec.Volumes {
			volume := &vj.Spec.Tasks[0].Template.Spec.Volumes[i]
			if volume.HostPath != nil && volume.HostPath.Path == AscendDriverHostPath {
				return true
			}
		}
		return false
	}
	npuDriverMount := func(vj *batch.Job) bool {
		for i := range vj.Spec.Tasks[0].Template.Spec.Containers[0].VolumeMounts {
			mounts := &vj.Spec.Tasks[0].Template.Spec.Containers[0].VolumeMounts[i]
			if mounts.MountPath == AscendDriverHostPath {
				return true
			}
		}
		return false
	}
	rankTableEnvExit := func(vj *batch.Job) bool {
		for _, env := range vj.Spec.Tasks[0].Template.Spec.Containers[0].Env {
			if env.Name == "RANK_TABLE_FILE" {
				return true
			}
		}
		return false
	}

	for _, test := range tests {
		vj := test.vj
		builder := NewAscendNPUBuilder()
		builder.PostBuild(nil, vj)

		assert.Equal(t, test.wantLabel, vj.Labels[AscendRingControllerAtlasLabelKey] != "")
		assert.Equal(t, test.wantLabel, vj.Spec.Tasks[0].Template.Labels[AscendRingControllerAtlasLabelKey] != "")
		assert.Equal(t, test.wantDriverVolume, npuDriverVolumeExist(vj))
		assert.Equal(t, test.wantDriverMount, npuDriverMount(vj))
		assert.Equal(t, test.wantRankTableEnv, rankTableEnvExit(vj))
	}
}

func TestBuildAscendNPUHCCLConfigMap(t *testing.T) {
	vjName := "job1"
	namespace := "hero-user"
	npu := "ascend-910"
	cm := BuildAscendNPUHCCLConfigMap(vjName, namespace, npu)

	assert.Equal(t, cm.Name, AscendHCCLConfigMapNamePrefix+vjName)
	assert.Contains(t, cm.Labels, AscendRingControllerAtlasLabelKey)
	assert.Equal(t, cm.Labels[AscendRingControllerAtlasLabelKey], npu)
	assert.Contains(t, cm.Data, "hccl.json")
}

func generateVcJob(useNPU bool) *batch.Job {
	vj := &batch.Job{
		Spec: batch.JobSpec{
			Tasks: []batch.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{
									Name: UserContainerName,
									Resources: v1.ResourceRequirements{
										Requests: map[v1.ResourceName]resource.Quantity{
											"cpu": *resource.NewQuantity(int64(1), resource.DecimalSI),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	if useNPU {
		vj.Spec.Tasks[0].Template.Spec.Containers[0].Resources.Requests["huawei.com/Ascend910"] = *resource.NewQuantity(int64(1), resource.DecimalSI)
	}
	return vj
}
