package vcjobbuilder

import (
	"sync"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
)

var pTaskVolumes *StorageBuilderFactory
var taskVolumesOnce sync.Once

func NewStorageBuilderFactory() *StorageBuilderFactory {
	taskVolumesOnce.Do(func() {
		pTaskVolumes = &StorageBuilderFactory{}
		pTaskVolumes.regist(string(systemv1alpha1.Volume), NewVolumeBuilder())
		pTaskVolumes.regist(string(systemv1alpha1.Git), NewEmptyDirBuilder())
		pTaskVolumes.regist(string(systemv1alpha1.ShareMemory), NewShareMemoryBuilder())

		// 存放增强容器功能的软件，比如 web terminal (gotty) 的执行文件，挂载目录为 /app
		pTaskVolumes.regist("app", NewEmptyDirBuilder())
	})

	return pTaskVolumes
}

type IStorageBuilder interface {
	Build(storage *systemv1alpha1.DataSource) *v1.Volume
}

func BuildCodeVolumeMount(storage *systemv1alpha1.CodeSource) *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      defaultCodeMountName,
		MountPath: storage.MountPath,
		ReadOnly:  false,
	}
}

func BuildVolumeMount(storage *systemv1alpha1.DataSource, subpath string) *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      storage.Name,
		MountPath: storage.MountPath,
		ReadOnly:  storage.ReadOnly,
		SubPath:   subpath,
	}
}

func BuildLocalDiskVolumeMount() *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      localDiskMountName,
		MountPath: localDiskMountPath,
		ReadOnly:  false,
	}
}

type ErrorBuilder struct {
}

func (eb *ErrorBuilder) Build(storage *systemv1alpha1.DataSource) *v1.Volume {
	return nil
}

type StorageBuilderFactory struct {
	volumeBuilders map[string]IStorageBuilder
}

func (sbf *StorageBuilderFactory) regist(kind string, builder IStorageBuilder) {
	if sbf.volumeBuilders == nil {
		sbf.volumeBuilders = map[string]IStorageBuilder{}
	}

	sbf.volumeBuilders[kind] = builder
}

func (sbf *StorageBuilderFactory) Get(kind string) IStorageBuilder {
	builder, ok := sbf.volumeBuilders[kind]
	if !ok {
		return &ErrorBuilder{}
	}

	return builder
}
