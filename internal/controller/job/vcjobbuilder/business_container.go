package vcjobbuilder

import (
	"regexp"

	v1 "k8s.io/api/core/v1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

func NewTaskNormalContainer() *TaskNormalContainer {
	return &TaskNormalContainer{}
}

type TaskNormalContainer struct {
}

func (tnc *TaskNormalContainer) BuildNotebook(job *systemv1alpha1.Notebook) *v1.Container {
	containerSpec := v1.Container{}
	containerSpec.Name = UserContainerName
	containerSpec.Image = job.Spec.ImageUrl
	containerSpec.ImagePullPolicy = v1.PullAlways
	containerSpec.Command = append(containerSpec.Command, TerminalMode...)
	containerSpec.Args = append(containerSpec.Args, job.Spec.Command)
	// 任务容器资源设置
	containerSpec.Resources.Limits = job.Spec.Resource
	containerSpec.Resources.Requests = job.Spec.Resource

	if len(job.Spec.CodeSource.GitUrl) != 0 {
		containerSpec.WorkingDir = job.Spec.CodeSource.MountPath
	}

	// 设置环境变量
	envs := []systemv1alpha1.Env{}
	if job.Annotations[LocalDiskCacheAnnotationKey] == "true" {
		quota := job.Annotations[LocalDiskQuotaAnnotationKey]
		env := systemv1alpha1.Env{Name: LocalDiskQuotaEnvKey, Value: quota}
		if !isValidQuotaValue(quota) {
			env.Value = "450g"
		}
		envs = append(envs, env)
	}
	tnc.setEnvs(&containerSpec, &job.Spec.Resource, envs)

	containerSpec.VolumeMounts = tnc.getVolumeMounts(&job.Spec.CodeSource, job.Spec.DataSources, &job.Spec.ExtendResource)
	if job.Annotations[LocalDiskCacheAnnotationKey] == "true" {
		containerSpec.VolumeMounts = append(containerSpec.VolumeMounts, *BuildLocalDiskVolumeMount())
	}

	return &containerSpec
}

func (tnc *TaskNormalContainer) Build(job *systemv1alpha1.TrainingJob, task *systemv1alpha1.Task) *v1.Container {
	containerSpec := v1.Container{}
	containerSpec.Name = UserContainerName
	containerSpec.Image = job.Spec.ImageUrl
	containerSpec.ImagePullPolicy = v1.PullAlways
	containerSpec.Command = append(containerSpec.Command, TerminalMode...)
	containerSpec.Args = append(containerSpec.Args, task.Command)
	// 任务容器资源设置
	containerSpec.Resources.Limits = task.Resource
	containerSpec.Resources.Requests = task.Resource

	if len(job.Spec.CodeSource.GitUrl) != 0 {
		containerSpec.WorkingDir = job.Spec.CodeSource.MountPath
	}

	// // 设置环境变量
	tnc.setEnvs(&containerSpec, &task.Resource, job.Spec.Envs)

	if len(job.Spec.Tasks) > 2 {
		containerSpec.SecurityContext = tnc.buildSecurityContext()
	}

	containerSpec.VolumeMounts = tnc.getVolumeMounts(&job.Spec.CodeSource, job.Spec.DataSources, &task.ExtendResource)

	return &containerSpec
}

func (tnc *TaskNormalContainer) buildSecurityContext() *v1.SecurityContext {
	return &v1.SecurityContext{
		Capabilities: &v1.Capabilities{
			Add: []v1.Capability{
				"IPC_LOCK",
			},
		},
	}
}

func (tnc *TaskNormalContainer) getVolumeMounts(code *systemv1alpha1.CodeSource, datasources []systemv1alpha1.DataSource, extendResource *systemv1alpha1.ExtendResource) []v1.VolumeMount {
	volumeMounts := []v1.VolumeMount{}
	if len(code.GitUrl) > 0 {
		volumeMounts = append(volumeMounts, *BuildCodeVolumeMount(code))
	}
	tnc.buildVolumeMountArray(datasources, &volumeMounts)

	volumeMounts = append(volumeMounts, *BuildVolumeMount(defaultShareMemory(extendResource.SharedMemory), ""))
	return volumeMounts
}

func (tnc *TaskNormalContainer) buildVolumeMountArray(storages []systemv1alpha1.DataSource, volumeMounts *[]v1.VolumeMount) {
	var tmpmap = make(map[string]*systemv1alpha1.DataSource)
	for k := range storages {
		tmpmap[storages[k].VolumeName] = &storages[k]
	}
	for k, storage := range storages {
		if _, found := tmpmap[storage.VolumeName]; found {
			storages[k].Name = tmpmap[storage.VolumeName].Name
		}
		*volumeMounts = append(*volumeMounts, *BuildVolumeMount(&storages[k], storage.VolumeSubPath))

	}
}

func (tnc *TaskNormalContainer) setEnvs(containerSpec *v1.Container, resource *v1.ResourceList, envs []systemv1alpha1.Env) {
	// 设置 NVIDIA_VISIBLE_DEVICES="" 来避免没有申请 GPU 资源的容器内看得见 GPU
	if isNoGpuResource(*resource) {
		containerSpec.Env = append(containerSpec.Env, v1.EnvVar{Name: "NVIDIA_VISIBLE_DEVICES", Value: ""})
	}

	// CURRENT_VC_TASK_NAME: 当前容器所在的 volcano task 名称
	// VC_TASK_NAMES: 当前容器所在的 volcano job 所有 task 名称，多个 task 以逗号分隔
	// 多节点分布式计算场景下，每个 Pod 需要通过 VC_{task_name}_HOSTS 环境变量得到每个 task 的所有节点的 host 名称，以便通信，
	// 但是 volcano ENV plugin 提供的环境变量，不方便拿到上述信息，所以添加这 2 个环境变量
	containerSpec.Env = append(containerSpec.Env, v1.EnvVar{
		Name: "CURRENT_VC_TASK_NAME",
		ValueFrom: &v1.EnvVarSource{
			FieldRef: &v1.ObjectFieldSelector{FieldPath: `metadata.labels['volcano.sh/task-spec']`},
		},
	})

	containerSpec.Env = append(containerSpec.Env, v1.EnvVar{
		Name: "VC_TASK_NAMES",
		ValueFrom: &v1.EnvVarSource{
			FieldRef: &v1.ObjectFieldSelector{FieldPath: `metadata.annotations['volcano.sh/task-topology-task-order']`},
		},
	})

	// POD_IP
	// 方便某些分布式计算场景下使用
	containerSpec.Env = append(containerSpec.Env, v1.EnvVar{
		Name: "POD_IP",
		ValueFrom: &v1.EnvVarSource{
			FieldRef: &v1.ObjectFieldSelector{FieldPath: `status.podIP`},
		},
	})

	// TZ
	// 设置时区为上海
	containerSpec.Env = append(containerSpec.Env, v1.EnvVar{
		Name:  "TZ",
		Value: "Asia/Shanghai",
	})

	// 添加用户自定义的环境变量
	// TODO: 检查用户设置的环境变量是否可以覆盖默认设置，或被 env plugin 覆盖？
	for _, v := range envs {
		containerSpec.Env = append(containerSpec.Env, v1.EnvVar{Name: v.Name, Value: v.Value})
	}
}

func isValidQuotaValue(quota string) bool {
	reg := regexp.MustCompile(`\b\d+g\b`)
	return reg.MatchString(quota)
}
