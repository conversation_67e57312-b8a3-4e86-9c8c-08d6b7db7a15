package jupyter

import (
	"fmt"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	"k8s.io/klog"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

var (
	JupyterPluginName    = "jupyter"
	jupyterContainerPort = 8081
)

type jupyterPlugin struct {
	client     *pluginsinterface.PluginClientset
	resbuilder *pluginResBuilder.DefaultCapability
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	baseURI := "JUPYTER_BASE_URL"
	return &jupyterPlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			Kind:         JupyterPluginName,
			BaseURLKey:   &baseURI,
			OffNetPolicy: true,
			FrontAction: &pluginResBuilder.IngressAction{
				Kind:   JupyterPluginName,
				Client: client.KubeClients,
				Port:   jupyterContainerPort,
				IngressBuilder: pluginResBuilder.IngressBuilder{
					Config: client.IngressConfig,
				}},
			BackEndActions: []pluginResBuilder.Action{
				&pluginResBuilder.ServiceAction{Kind: JupyterPluginName, Client: client.KubeClients, SelectorFunc: pluginResBuilder.DefaultSelectFunc, Port: jupyterContainerPort},
			},
		},
	}
}

func (jp *jupyterPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {

	return nil
}

func (jp *jupyterPlugin) CreateVJNotebookPlugins(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	jp.client.IsMultiApp = true
	if err := jp.resbuilder.PreCreate(vcjob, notebookjob); err != nil {
		klog.Errorf("plugin %s err in PreCreate, err : %s", JupyterPluginName, err.Error())
		return err
	}

	vcjobbuilder.RegisterCmd(JupyterPluginName, "")
	vcjobbuilder.RegisterEnv(*jp.resbuilder.BaseURLKey, fmt.Sprintf("/%s/%s", jp.resbuilder.Kind, notebookjob.Name))
	vcjobbuilder.RegisterCheckCmd(JupyterPluginName, jupyterContainerPort)
	if err := jp.resbuilder.Create(notebookjob, jp.serviceName(JupyterPluginName, notebookjob.Name)); err != nil {
		return err
	}
	if len(jp.resbuilder.GetURL()) != 0 {
		notebookjob.Status.Jupyter.URL = jp.resbuilder.GetURL()
	}
	return nil
}

func (jp *jupyterPlugin) serviceName(kind string, jobName string) string {
	return kind + "-" + jobName
}
