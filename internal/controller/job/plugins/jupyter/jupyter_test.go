package jupyter

import (
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

type MockFrontAction struct{}

func (m *MockFrontAction) Create(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) Exist(job *systemv1alpha1.Notebook) bool {
	return true
}

func (m *MockFrontAction) Delete(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) FrontPath(nameSpace, jobName string) map[string]string {
	return nil
}

func (m *MockFrontAction) GetWebURL() string {
	return "http://example.com"
}

func TestNew(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)

	assert.NotNil(t, clt, "New should return a non-nil jupyterPlugin")
	assert.Equal(t, "jupyter", JupyterPluginName, "JupyterPluginName should be 'jupyter'")
	assert.Equal(t, 8081, jupyterContainerPort, "jupyterContainerPort should be 8081")
}

func TestCreateVJNotebookPlugins(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	baseURL := "http://localhost:8080"
	jp := &jupyterPlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			FrontAction: &MockFrontAction{},
			BaseURLKey:  &baseURL,
		},
	}

	notebookJob := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}

	vcJob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Tasks: []batchvc.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{},
					},
				},
			},
		},
	}

	err := jp.CreateVJNotebookPlugins(notebookJob, vcJob)
	assert.NoError(t, err, "CreateVJNotebookPlugins should not return an error")
	assert.Equal(t, "http://example.com", notebookJob.Status.Jupyter.URL, "URL should be set correctly")
}

func TestServiceName(t *testing.T) {
	jp := &jupyterPlugin{}
	serviceName := jp.serviceName(JupyterPluginName, "test-job")

	expected := "jupyter-test-job"
	assert.Equal(t, expected, serviceName, "ServiceName should return the correct service name")
}
