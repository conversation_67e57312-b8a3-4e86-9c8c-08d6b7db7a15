package pytorch

import (
	"fmt"

	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
)

const (
	PytorchPluginName = "pytorch"
	DefaultMaster     = "master"
	DefaultWorker     = "worker"
)

type pytorchPlugin struct {
	args []string
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	return &pytorchPlugin{}
}

func (pp *pytorchPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	for _, v := range trainjob.Spec.Tasks {
		if v.TaskType == DefaultMaster {
			pp.args = append(pp.args, fmt.Sprintf("--master=%s", v.Name))
		} else if v.TaskType == DefaultWorker {
			pp.args = append(pp.args, fmt.Sprintf("--worker=%s", v.Name))
		}
	}

	vcjob.Spec.Plugins[PytorchPluginName] = pp.args
	return nil
}

func (pp *pytorchPlugin) CreateVJNotebookPlugins(notebook *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	return nil
}
