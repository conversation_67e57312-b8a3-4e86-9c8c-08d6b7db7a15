package pytorch

import (
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

// Test constants
const (
	TestMasterTaskName = "master-task"
	TestWorkerTaskName = "worker-task"
)

// TestNew tests the New function.
func TestNew(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)

	assert.NotNil(t, clt, "Expected plugin to be created")
	assert.IsType(t, &pytorchPlugin{}, clt, "Expected plugin type to be pytorchPlugin")
}

// TestCreateVolcanojobPlugins tests the CreateVolcanojobPlugins function.
func TestCreateVolcanojobPlugins(t *testing.T) {
	pt := &pytorchPlugin{
		args: make([]string, 0),
	}

	trainjob := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{
					TaskType: DefaultMaster,
					Name:     TestMasterTaskName,
				},
				{
					TaskType: DefaultWorker,
					Name:     TestWorkerTaskName,
				},
			},
		},
	}

	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Plugins: make(map[string][]string),
		},
	}

	err := pt.CreateVolcanojobPlugins(trainjob, vcjob)

	assert.NoError(t, err, "Expected no error from CreateVolcanojobPlugins")

	// Verify that the plugin arguments are correctly set
	expectedArgs := []string{
		"--master=" + TestMasterTaskName,
		"--worker=" + TestWorkerTaskName,
	}
	assert.Equal(t, expectedArgs, vcjob.Spec.Plugins[PytorchPluginName], "Expected plugin arguments to match")
}

// TestCreateVJNotebookPlugins tests the CreateVJNotebookPlugins function.
func TestCreateVJNotebookPlugins(t *testing.T) {
	plugin := New(nil).(*pytorchPlugin)

	notebook := &systemv1alpha1.Notebook{}
	vcjob := &batchvc.Job{}

	err := plugin.CreateVJNotebookPlugins(notebook, vcjob)

	assert.NoError(t, err, "Expected no error from CreateVJNotebookPlugins")
}
