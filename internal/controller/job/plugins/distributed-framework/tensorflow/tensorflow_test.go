package tensorflow

import (
	"fmt"
	"testing"

	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"

	"github.com/stretchr/testify/assert"
)

func TestTensorflowPluginConstants(t *testing.T) {
	// 测试常量
	assert.Equal(t, "tensorflow", TFPluginName)
	assert.Equal(t, "ps", DefaultPs)
	assert.Equal(t, "worker", DefaultTfWorker)
	assert.Equal(t, "chief", DefaultChief)
	assert.Equal(t, "evaluator", DefaultEvaluator)
}

func TestTensorflowPlugin_New(t *testing.T) {
	// 测试 New 函数
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)

	assert.NotNil(t, clt)
}

func TestTensorflowPlugin_CreateVolcanojobPlugins(t *testing.T) {
	// 测试 CreateVolcanojobPlugins 函数
	tf := &tensorflowPlugin{
		args: make([]string, 0),
	}

	// 创建一个训练任务
	trainjob := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{TaskType: DefaultPs, Name: "ps-task"},
				{TaskType: DefaultTfWorker, Name: "worker-task"},
				{TaskType: DefaultChief, Name: "chief-task"},
				{TaskType: DefaultEvaluator, Name: "evaluator-task"},
			},
		},
	}

	// 创建一个 Volcano Job
	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Plugins: make(map[string][]string),
		},
	}

	// 调用函数
	err := tf.CreateVolcanojobPlugins(trainjob, vcjob)
	assert.NoError(t, err)

	// 验证 vcjob.Spec.Plugins 是否正确
	expectedArgs := []string{
		fmt.Sprintf("--ps=%s", "ps-task"),
		fmt.Sprintf("--worker=%s", "worker-task"),
		fmt.Sprintf("--chief=%s", "chief-task"),
		fmt.Sprintf("--evaluator=%s", "evaluator-task"),
	}

	assert.Equal(t, expectedArgs, vcjob.Spec.Plugins[TFPluginName])
}

func TestTensorflowPlugin_CreateVJNotebookPlugins(t *testing.T) {
	// 测试 CreateVJNotebookPlugins 函数
	tf := &tensorflowPlugin{
		args: make([]string, 0),
	}
	notebook := &systemv1alpha1.Notebook{}
	vcjob := &batchvc.Job{}

	// 调用函数
	err := tf.CreateVJNotebookPlugins(notebook, vcjob)
	assert.NoError(t, err)
}
