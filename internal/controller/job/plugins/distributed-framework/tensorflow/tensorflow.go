package tensorflow

import (
	"fmt"

	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
)

const (
	TFPluginName     = "tensorflow"
	DefaultPs        = "ps"
	DefaultTfWorker  = "worker"
	DefaultChief     = "chief"
	DefaultEvaluator = "evaluator"
)

type tensorflowPlugin struct {
	args []string
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	return &tensorflowPlugin{}
}
func (tfp *tensorflowPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	for _, v := range trainjob.Spec.Tasks {
		if v.TaskType == DefaultPs {
			tfp.args = append(tfp.args, fmt.Sprintf("--ps=%s", v.Name))
		} else if v.TaskType == DefaultTfWorker {
			tfp.args = append(tfp.args, fmt.Sprintf("--worker=%s", v.Name))
		} else if v.TaskType == DefaultChief {
			tfp.args = append(tfp.args, fmt.Sprintf("--chief=%s", v.Name))
		} else if v.TaskType == DefaultEvaluator {
			tfp.args = append(tfp.args, fmt.Sprintf("--evaluator=%s", v.Name))
		}
	}

	vcjob.Spec.Plugins[TFPluginName] = tfp.args
	return nil
}

func (pp *tensorflowPlugin) CreateVJNotebookPlugins(notebook *systemv1alpha1.Notebook, vcjob *batchvc.Job) error { //nolint
	return nil
}
