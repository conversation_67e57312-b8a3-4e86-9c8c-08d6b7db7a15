package deepspeed

import (
	"context"
	"fmt"
	"strings"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	"volcano.sh/apis/pkg/apis/bus/v1alpha1"
)

const (
	DeepSpeedPluginName = "deepspeed"
	VolumeSourceName    = "hostfile"
	MountPath           = "/etc/deepspeed"
	Command1            = "mkdir -p /var/run/sshd; /usr/sbin/sshd;"
	Command2            = "mkdir -p /var/run/sshd; /usr/sbin/sshd -D;"
)

type deepspeedPlugin struct {
	client        *pluginsinterface.PluginClientset
	ConfigMapName string
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	return &deepspeedPlugin{client: client}
}

func (dpp *deepspeedPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	falseVal := false
	if len(vcjob.Spec.Tasks) <= 1 {
		return nil
	}
	vcjob.Spec.Policies = append(vcjob.Spec.Policies,
		batchvc.LifecyclePolicy{Event: v1alpha1.TaskCompletedEvent, Action: v1alpha1.CompleteJobAction},
		batchvc.LifecyclePolicy{Event: v1alpha1.TaskFailedEvent, Action: v1alpha1.TerminateJobAction},
		batchvc.LifecyclePolicy{Event: v1alpha1.PodFailedEvent, Action: v1alpha1.TerminateJobAction},
	)
	vcjob.Spec.Plugins["ssh"] = make([]string, 0)
	vcjob.Spec.Plugins["svc"] = make([]string, 0)
	if err := dpp.CreateDeepSpeedComfigMapResource(trainjob); err != nil {
		return err
	}

	for _, task := range trainjob.Spec.Tasks {
		if task.TaskType == "master" {
			dpp.buildVolumeSource(&vcjob.Spec.Tasks[0].Template.Spec.Volumes)
			container0 := &vcjob.Spec.Tasks[0].Template.Spec.Containers[0]
			vcjob.Spec.Tasks[0].Template.Spec.EnableServiceLinks = &falseVal
			container0.VolumeMounts = append(container0.VolumeMounts, *dpp.buildVolumeMount())
			container0.Command = append(container0.Command, Command1+container0.Args[len(container0.Args)-1])
			container0.Ports = append(container0.Ports, v1.ContainerPort{Name: "ssh-port", ContainerPort: 22})
			container0.Args = make([]string, 0)
		} else {
			container1 := &vcjob.Spec.Tasks[1].Template.Spec.Containers[0]
			vcjob.Spec.Tasks[1].Template.Spec.EnableServiceLinks = &falseVal
			container1.Command = []string{"sh", "-c", Command2}
			container1.Ports = append(container1.Ports, v1.ContainerPort{Name: "ssh-port", ContainerPort: 22})
			container1.Args = make([]string, 0)
		}
	}

	return nil
}

func (dpp *deepspeedPlugin) CreateVJNotebookPlugins(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	return nil
}

func (dpp *deepspeedPlugin) buildVolumeMount() *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      VolumeSourceName,
		MountPath: MountPath,
		ReadOnly:  false,
	}
}

func (dpp *deepspeedPlugin) buildVolumeSource(volumes *[]v1.Volume) {
	*volumes = append(*volumes, v1.Volume{
		Name: VolumeSourceName,
		VolumeSource: v1.VolumeSource{
			ConfigMap: &v1.ConfigMapVolumeSource{
				LocalObjectReference: v1.LocalObjectReference{
					Name: dpp.ConfigMapName,
				},
			},
		},
	})
}

func (dpp *deepspeedPlugin) CreateDeepSpeedComfigMapResource(trainjob *systemv1alpha1.TrainingJob) error {
	dpp.ConfigMapName = vcjobbuilder.VcjobNameTrainjobPrefix + trainjob.Name + "-deepspeed-hostfile-configmap"
	_, gpu_nums := dpp.GetGpuNameAndNums(trainjob) //nolint
	hostfileData := ""

	refs := []metav1.OwnerReference{
		{
			APIVersion: "system.hero.ai/v1alpha1",
			Kind:       "TrainingJob",
			Name:       trainjob.Name,
			UID:        trainjob.UID,
		},
	}
	configMap := &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:            dpp.ConfigMapName,
			Namespace:       trainjob.Namespace,
			OwnerReferences: refs,
		},
	}
	for _, task := range trainjob.Spec.Tasks {
		for i := 0; i < int(task.Replicas); i++ {
			hostname := "tj-" + trainjob.Name + "-" + task.Name
			hostfileData += fmt.Sprintf("%s-%d slots=%d\n", hostname, i, gpu_nums)
		}
	}

	configMap.Data = map[string]string{"hostfile": hostfileData}

	if err := dpp.client.KubeClients.Create(context.TODO(), configMap); err != nil {
		return err
	}
	return nil
}

func (dpp *deepspeedPlugin) GetGpuNameAndNums(trainjob *systemv1alpha1.TrainingJob) (string, int64) {
	for k, v := range trainjob.Spec.Tasks[0].Resource {
		if strings.Contains(k.String(), "nvidia.com") || strings.Contains(k.String(), "huawei.com") {
			return k.String(), v.Value()
		}
	}
	return "", int64(0)
}
