package deepspeed

import (
	"testing"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

// TestNew tests the New function.
func TestNew(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)

	assert.NotNil(t, clt, "Expected plugin to be created")
	assert.IsType(t, &deepspeedPlugin{}, clt, "Expected plugin type to be deepspeedPlugin")
}

// TestCreateVolcanojobPlugins tests the CreateVolcanojobPlugins function.
func TestCreateVolcanojobPlugins(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	client := &pluginsinterface.PluginClientset{
		KubeClients: k8sFakeClient,
	}
	dp := &deepspeedPlugin{
		client:        client,
		ConfigMapName: "tj-" + "jobName" + "-deepspeed-hostfile-configmap",
	}

	trainjob := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{
					TaskType: "master",
					Replicas: 1,
				},
				{
					TaskType: "worker",
					Replicas: 2,
				},
			},
		},
	}

	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Tasks: []batchvc.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{{Args: make([]string, 2)}},
						},
					},
				},
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{{Args: make([]string, 2)}},
						},
					},
				},
			},
			Plugins: map[string][]string{
				"ssh": []string{""},
				"svc": []string{""},
			},
		},
	}

	err := dp.CreateVolcanojobPlugins(trainjob, vcjob)

	assert.NoError(t, err, "Expected no error from CreateVolcanojobPlugins")

	// Validate the modified vcjob
	assert.Len(t, vcjob.Spec.Policies, 3, "Expected 3 lifecycle policies to be added")
}

// TestCreateDeepSpeedComfigMapResource tests the CreateDeepSpeedComfigMapResource function.
func TestCreateDeepSpeedComfigMapResource(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	client := &pluginsinterface.PluginClientset{
		KubeClients: k8sFakeClient,
	}
	dp := &deepspeedPlugin{
		client:        client,
		ConfigMapName: "tj-" + "jobName" + "-deepspeed-hostfile-configmap",
	}

	trainjob := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
			UID:       types.UID("12345"),
		},
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{
					Name:     "task1",
					Replicas: 2,
					Resource: map[v1.ResourceName]resource.Quantity{
						"nvidia.com/gpu": resource.MustParse("2"),
					},
				},
			},
		},
	}

	err := dp.CreateDeepSpeedComfigMapResource(trainjob)

	assert.NoError(t, err, "Expected no error from CreateDeepSpeedComfigMapResource")

	// Verify ConfigMap creation
	expectedConfigMapName := "tj-test-job-deepspeed-hostfile-configmap"
	assert.Equal(t, expectedConfigMapName, dp.ConfigMapName, "Expected ConfigMap name to match")
}

// TestBuildVolumeMount tests the buildVolumeMount function.
func TestBuildVolumeMount(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	dp := &deepspeedPlugin{
		client:        client,
		ConfigMapName: "tj-" + "jobName" + "-deepspeed-hostfile-configmap",
	}

	volumeMount := dp.buildVolumeMount()

	assert.NotNil(t, volumeMount, "Expected volume mount to be created")
	assert.Equal(t, VolumeSourceName, volumeMount.Name, "Expected volume mount name to match")
	assert.Equal(t, MountPath, volumeMount.MountPath, "Expected mount path to match")
	assert.False(t, volumeMount.ReadOnly, "Expected volume mount to be writable")
}

// TestBuildVolumeSource tests the buildVolumeSource function.
func TestBuildVolumeSource(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	dp := &deepspeedPlugin{
		client:        client,
		ConfigMapName: "tj-" + "jobName" + "-deepspeed-hostfile-configmap",
	}

	volumes := []v1.Volume{}
	dp.buildVolumeSource(&volumes)

	assert.Len(t, volumes, 1, "Expected one volume to be added")
	assert.Equal(t, VolumeSourceName, volumes[0].Name, "Expected volume name to match")
	assert.NotNil(t, volumes[0].VolumeSource.ConfigMap, "Expected volume source to be ConfigMap")
}

// TestGetGpuNameAndNums tests the GetGpuNameAndNums function.
func TestGetGpuNameAndNums(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	dp := &deepspeedPlugin{
		client:        client,
		ConfigMapName: "tj-" + "jobName" + "-deepspeed-hostfile-configmap",
	}

	trainjob := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{
					Resource: map[v1.ResourceName]resource.Quantity{
						"nvidia.com/gpu": resource.MustParse("2"),
					},
				},
			},
		},
	}

	gpuName, gpuNum := dp.GetGpuNameAndNums(trainjob)

	assert.Equal(t, "nvidia.com/gpu", gpuName, "Expected GPU name to match")
	assert.Equal(t, int64(2), gpuNum, "Expected GPU number to match")
}

// TestCreateVJNotebookPlugins tests the CreateVJNotebookPlugins function.
func TestCreateVJNotebookPlugins(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	dp := &deepspeedPlugin{
		client:        client,
		ConfigMapName: "tj-" + "jobName" + "-deepspeed-hostfile-configmap",
	}

	notebookjob := &systemv1alpha1.Notebook{}
	vcjob := &batchvc.Job{}

	err := dp.CreateVJNotebookPlugins(notebookjob, vcjob)

	assert.NoError(t, err, "Expected no error from CreateVJNotebookPlugins")
}
