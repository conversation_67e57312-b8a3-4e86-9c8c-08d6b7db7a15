package colossalai

import (
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"testing"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

// TestNew tests the New function.
func TestNew(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)

	assert.NotNil(t, clt, "Expected plugin to be created")
	assert.IsType(t, &colossalaiPlugin{}, clt, "Expected plugin type to be colossalaiPlugin")
}

// TestCreateVolcanojobPlugins tests the CreateVolcanojobPlugins function.
func TestCreateVolcanojobPlugins(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	client := &pluginsinterface.PluginClientset{
		KubeClients: k8sFakeClient,
	}
	cl := &colossalaiPlugin{
		client:        client,
		ConfigMapName: "tj-" + "jobName" + "-colossalai-hostfile-configmap",
	}

	trainjob := &systemv1alpha1.TrainingJob{
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{
					TaskType: "master",
					Replicas: 1,
				},
				{
					TaskType: "worker",
					Replicas: 2,
				},
			},
		},
	}

	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Tasks: []batchvc.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{{Args: make([]string, 2)}},
						},
					},
				},
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{{Args: make([]string, 2)}},
						},
					},
				},
			},
			Plugins: map[string][]string{
				"ssh": []string{""},
				"svc": []string{""},
			},
		},
	}

	err := cl.CreateVolcanojobPlugins(trainjob, vcjob)

	assert.NoError(t, err, "Expected no error from CreateVolcanojobPlugins")

	// Validate the modified vcjob
	assert.Len(t, vcjob.Spec.Policies, 3, "Expected 3 lifecycle policies to be added")
}

// TestCreateColossalaiComfigMapResource tests the CreateColossalaiComfigMapResource function.
func TestCreateColossalaiComfigMapResource(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	client := &pluginsinterface.PluginClientset{
		KubeClients: k8sFakeClient,
	}
	cl := &colossalaiPlugin{
		client: client,
	}

	trainjob := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
			UID:       types.UID("12345"),
		},
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{
					Name:     "task1",
					Replicas: 1,
				},
			},
		},
	}

	err := cl.CreateColossalaiComfigMapResource(trainjob)

	assert.NoError(t, err, "Expected no error from CreateColossalaiComfigMapResource")

	// Verify ConfigMap creation
	expectedConfigMapName := "tj-test-job-colossalai-hostfile-configmap"
	assert.Equal(t, expectedConfigMapName, cl.ConfigMapName, "Expected ConfigMap name to match")
}

// TestBuildVolumeMount tests the buildVolumeMount function.
func TestBuildVolumeMount(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	cl := &colossalaiPlugin{
		client: client,
	}

	volumeMount := cl.buildVolumeMount()

	assert.NotNil(t, volumeMount, "Expected volume mount to be created")
	assert.Equal(t, VolumeSourceName, volumeMount.Name, "Expected volume mount name to match")
	assert.Equal(t, MountPath, volumeMount.MountPath, "Expected mount path to match")
	assert.False(t, volumeMount.ReadOnly, "Expected volume mount to be writable")
}

// TestBuildVolumeSource tests the buildVolumeSource function.
func TestBuildVolumeSource(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	cl := &colossalaiPlugin{
		client: client,
	}

	volumes := []v1.Volume{}
	cl.buildVolumeSource(&volumes)

	assert.Len(t, volumes, 1, "Expected one volume to be added")
	assert.Equal(t, VolumeSourceName, volumes[0].Name, "Expected volume name to match")
	assert.NotNil(t, volumes[0].VolumeSource.ConfigMap, "Expected volume source to be ConfigMap")
}

// TestCreateVJNotebookPlugins tests the CreateVJNotebookPlugins function.
func TestCreateVJNotebookPlugins(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	cl := &colossalaiPlugin{
		client: client,
	}

	notebookjob := &systemv1alpha1.Notebook{}
	vcjob := &batchvc.Job{}

	err := cl.CreateVJNotebookPlugins(notebookjob, vcjob)

	assert.NoError(t, err, "Expected no error from CreateVJNotebookPlugins")
}
