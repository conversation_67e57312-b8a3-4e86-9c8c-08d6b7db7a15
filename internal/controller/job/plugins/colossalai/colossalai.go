package colossalai

import (
	"context"
	"fmt"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	"volcano.sh/apis/pkg/apis/bus/v1alpha1"
)

const (
	ColossalAIPluginName = "colossalai"
	VolumeSourceName     = "hostfile"
	MountPath            = "/etc/colossalai"
	Command1             = "sleep 10s; sh /app/getIP.sh %s %s %s; mkdir -p /var/run/sshd; /usr/sbin/sshd;"
	Command2             = "sleep 10s; sh /app/getIP.sh %s %s %s; mkdir -p /var/run/sshd; /usr/sbin/sshd -D;"
	ClusterDomain        = "svc.cluster.local"
)

type colossalaiPlugin struct {
	client        *pluginsinterface.PluginClientset
	ConfigMapName string
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	return &colossalaiPlugin{client: client}
}

func (cp *colossalaiPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {

	falseVal := false
	if len(vcjob.Spec.Tasks) <= 1 {
		return nil
	}
	vcjob.Spec.Policies = append(vcjob.Spec.Policies,
		batchvc.LifecyclePolicy{Event: v1alpha1.TaskCompletedEvent, Action: v1alpha1.CompleteJobAction},
		batchvc.LifecyclePolicy{Event: v1alpha1.TaskFailedEvent, Action: v1alpha1.TerminateJobAction},
		batchvc.LifecyclePolicy{Event: v1alpha1.PodFailedEvent, Action: v1alpha1.TerminateJobAction},
	)
	vcjob.Spec.Plugins["ssh"] = make([]string, 0)
	vcjob.Spec.Plugins["svc"] = make([]string, 0)
	if err := cp.CreateColossalaiComfigMapResource(trainjob); err != nil {
		return err
	}
	for _, task := range trainjob.Spec.Tasks {
		if task.TaskType == "master" {
			cp.buildVolumeSource(&vcjob.Spec.Tasks[0].Template.Spec.Volumes)
			container0 := &vcjob.Spec.Tasks[0].Template.Spec.Containers[0]
			vcjob.Spec.Tasks[0].Template.Spec.EnableServiceLinks = &falseVal
			container0.VolumeMounts = append(container0.VolumeMounts, *cp.buildVolumeMount())
			container0.Command = append(container0.Command,
				fmt.Sprintf(Command1, vcjobbuilder.VcjobNameTrainjobPrefix+trainjob.Name, trainjob.Namespace, ClusterDomain)+container0.Args[len(container0.Args)-1])
			container0.Ports = append(container0.Ports, v1.ContainerPort{Name: "ssh-port", ContainerPort: 22})
			container0.Args = make([]string, 0)
		} else {
			cp.buildVolumeSource(&vcjob.Spec.Tasks[1].Template.Spec.Volumes)
			container1 := &vcjob.Spec.Tasks[1].Template.Spec.Containers[0]
			vcjob.Spec.Tasks[1].Template.Spec.EnableServiceLinks = &falseVal
			container1.VolumeMounts = append(container1.VolumeMounts, *cp.buildVolumeMount())
			container1.Command = []string{"sh", "-c", fmt.Sprintf(Command2, vcjobbuilder.VcjobNameTrainjobPrefix+trainjob.Name, trainjob.Namespace, ClusterDomain)}
			container1.Ports = append(container1.Ports, v1.ContainerPort{Name: "ssh-port", ContainerPort: 22})
			container1.Args = make([]string, 0)
		}
	}

	return nil
}

func (cp *colossalaiPlugin) CreateVJNotebookPlugins(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	return nil
}

func (cp *colossalaiPlugin) buildVolumeMount() *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      VolumeSourceName,
		MountPath: MountPath,
		ReadOnly:  false,
	}
}

func (cp *colossalaiPlugin) buildVolumeSource(volumes *[]v1.Volume) {
	*volumes = append(*volumes, v1.Volume{
		Name: VolumeSourceName,
		VolumeSource: v1.VolumeSource{
			ConfigMap: &v1.ConfigMapVolumeSource{
				LocalObjectReference: v1.LocalObjectReference{
					Name: cp.ConfigMapName,
				},
			},
		},
	})
}

func (cp *colossalaiPlugin) CreateColossalaiComfigMapResource(trainjob *systemv1alpha1.TrainingJob) error {
	cp.ConfigMapName = vcjobbuilder.VcjobNameTrainjobPrefix + trainjob.Name + "-colossalai-hostfile-configmap"
	hostfileData := ""

	refs := []metav1.OwnerReference{
		{
			APIVersion: "system.hero.ai/v1alpha1",
			Kind:       "TrainingJob",
			Name:       trainjob.Name,
			UID:        trainjob.UID,
		},
	}
	configMap := &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:            cp.ConfigMapName,
			Namespace:       trainjob.Namespace,
			OwnerReferences: refs,
		},
	}
	for _, task := range trainjob.Spec.Tasks {
		for i := 0; i < int(task.Replicas); i++ {
			hostname := vcjobbuilder.VcjobNameTrainjobPrefix + trainjob.Name + "-" + task.Name
			hostfileData += fmt.Sprintf("%s-%d\n", hostname, i)
		}
	}

	configMap.Data = map[string]string{"hostfile": hostfileData}

	if err := cp.client.KubeClients.Create(context.TODO(), configMap); err != nil {
		return err
	}
	return nil
}
