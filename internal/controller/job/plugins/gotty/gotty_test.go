package gotty

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	v1 "k8s.io/api/core/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

type MockFrontAction struct{}

func (m *MockFrontAction) Create(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) Exist(job *systemv1alpha1.Notebook) bool {
	return true
}

func (m *MockFrontAction) Delete(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) FrontPath(nameSpace, jobName string) map[string]string {
	return nil
}

func (m *MockFrontAction) GetWebURL() string {
	return "http://example.com"
}

func TestNew(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)
	assert.NotNil(t, clt, "New should return a non-nil gottyPlugin")
}

func TestCreateVolcanojobPlugins(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	gotty := &gottyPlugin{
		client:     client,
		resbuilder: &pluginResBuilder.DefaultCapability{},
	}

	trainjob := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-job",
			Namespace: "default",
		},
		Spec: systemv1alpha1.TrainingJobSpec{
			Tasks: []systemv1alpha1.Task{
				{
					Name:     "task1",
					Replicas: 2,
				},
				{
					Name:     "task2",
					Replicas: 2,
				},
			},
		},
	}

	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Tasks: []batchvc.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{},
					},
				},
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{},
					},
				},
			},
		},
	}

	err := gotty.CreateVolcanojobPlugins(trainjob, vcjob)
	assert.NoError(t, err, "CreateVolcanojobPlugins should not return an error")

	// Verify InitContainers added to each task
	for _, task := range vcjob.Spec.Tasks {
		assert.NotEmpty(t, task.Template.Spec.InitContainers, "InitContainers should not be empty")
	}
}

func TestCreateVJNotebookPlugins(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = v1.AddToScheme(scheme)
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	client := &pluginsinterface.PluginClientset{
		KubeClients: k8sFakeClient,
	}
	gotty := &gottyPlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			FrontAction: &MockFrontAction{},
		},
	}

	notebookjob := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}

	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Tasks: []batchvc.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{},
					},
				},
			},
		},
	}

	err := gotty.CreateVJNotebookPlugins(notebookjob, vcjob)
	assert.NoError(t, err, "CreateVJNotebookPlugins should not return an error")
	assert.Equal(t, "http://example.com", notebookjob.Status.WebTerminal.URL, "WebTerminal URL should be set")
}

func TestServiceName(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	gotty := &gottyPlugin{
		client: client,
	}

	kind := "web-terminal"
	jobName := "test-job"
	expectedServiceName := "web-terminal-test-job"

	serviceName := gotty.serviceName(kind, jobName)
	assert.Equal(t, expectedServiceName, serviceName, "Service name should match the expected format")
}
