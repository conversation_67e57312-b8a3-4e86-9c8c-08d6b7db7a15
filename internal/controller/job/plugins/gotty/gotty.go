package gotty

import (
	"strconv"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/plugins/utils"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	v1 "k8s.io/api/core/v1"
	"k8s.io/klog"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

var (
	gottyPluginName    = "web-terminal"
	shortName          = "wb"
	gottyContainerPort = 8083
	gottyImage         = "registry.cnbita.com:5000/cloud-images/hero-user/web-terminal-gotty:latest"
	containerName      = "web-terminal-init"
	cmd                = " mkdir /app/gotty && cp /usr/local/bin/gotty /app/gotty/gotty "
)

type gottyPlugin struct {
	client     *pluginsinterface.PluginClientset
	resbuilder *pluginResBuilder.DefaultCapability
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	baseURI := "BASE_URL"
	regular := `(.*)`
	rewritePath := `/$1`
	ingressPathFormat := "/%s/%s/"
	return &gottyPlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			Kind:         shortName,
			BaseURLKey:   &baseURI,
			OffNetPolicy: true,
			FrontAction: &pluginResBuilder.IngressAction{
				Kind:              shortName,
				Client:            client.KubeClients,
				Regular:           regular,
				RewritePath:       &rewritePath,
				IngressPathFormat: &ingressPathFormat,
				Port:              gottyContainerPort,
				IngressBuilder: pluginResBuilder.IngressBuilder{
					Config: client.IngressConfig,
				},
			},
			BackEndActions: []pluginResBuilder.Action{
				&pluginResBuilder.ServiceAction{Kind: shortName, Client: client.KubeClients, SelectorFunc: pluginResBuilder.DefaultSelectFunc, Port: gottyContainerPort},
			},
		},
	}
}

func (gp *gottyPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	gp.client.IsMultiApp = true
	var podSuffixList []string
	cli := utils.NewAppContainerClient(containerName, gottyImage, cmd)
	for k, task := range vcjob.Spec.Tasks {
		//create app volume
		cli.BuildAppVolume(&vcjob.Spec.Tasks[k].Template.Spec.Volumes)
		if len(task.Template.Spec.InitContainers) == 0 {
			vcjob.Spec.Tasks[k].Template.Spec.InitContainers = []v1.Container{}
		}
		vcjob.Spec.Tasks[k].Template.Spec.InitContainers = append(vcjob.Spec.Tasks[k].Template.Spec.InitContainers, *cli.BuildInitContainer())
		for kk := range task.Template.Spec.Containers {
			cli.BuildMainVolumeMount(&vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].VolumeMounts)
		}

		for i := 0; i < int(task.Replicas); i++ {
			podSuffix := task.Name + "-" + strconv.Itoa(i)
			podSuffixList = append(podSuffixList, podSuffix)
		}
	}

	err := gp.resbuilder.PreCreate(vcjob, nil)
	if err != nil {
		klog.Errorf("plugin %s err in PreCreate, err : %s", gottyPluginName, err.Error())
		return err
	}

	vcjobbuilder.RegisterCmd(gottyPluginName, "")
	vcjobbuilder.RegisterCheckCmd(gottyPluginName, gottyContainerPort)
	if err := gp.resbuilder.CreateTJ(trainjob, podSuffixList); err != nil {
		return err
	}

	return nil
}

func (gp *gottyPlugin) CreateVJNotebookPlugins(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	gp.client.IsMultiApp = true
	cli := utils.NewAppContainerClient(containerName, gottyImage, cmd)
	for k, task := range vcjob.Spec.Tasks {
		//create app volume
		cli.BuildAppVolume(&vcjob.Spec.Tasks[k].Template.Spec.Volumes)
		if len(task.Template.Spec.InitContainers) == 0 {
			vcjob.Spec.Tasks[k].Template.Spec.InitContainers = []v1.Container{}
		}
		vcjob.Spec.Tasks[k].Template.Spec.InitContainers = append(vcjob.Spec.Tasks[k].Template.Spec.InitContainers, *cli.BuildInitContainer())
		for kk := range task.Template.Spec.Containers {
			cli.BuildMainVolumeMount(&vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].VolumeMounts)
		}
	}

	err := gp.resbuilder.PreCreate(vcjob, notebookjob)
	if err != nil {
		klog.Errorf("plugin %s err in PreCreate, err : %s", gottyPluginName, err.Error())
		return err
	}

	vcjobbuilder.RegisterCmd(gottyPluginName, "")
	vcjobbuilder.RegisterCheckCmd(gottyPluginName, gottyContainerPort)
	if err := gp.resbuilder.Create(notebookjob, gp.serviceName(shortName, notebookjob.Name)); err != nil {
		return err
	}

	if len(gp.resbuilder.GetURL()) != 0 {
		notebookjob.Status.WebTerminal.URL = gp.resbuilder.GetURL()
	}

	return nil
}

func (gp *gottyPlugin) serviceName(kind string, jobName string) string {
	return kind + "-" + jobName
}
