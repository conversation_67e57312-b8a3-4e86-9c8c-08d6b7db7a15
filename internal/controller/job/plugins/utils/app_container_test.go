package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
)

func TestNewAppContainerClient(t *testing.T) {
	client := NewAppContainerClient("test-name", "test-image", "test-cmd")

	assert.NotNil(t, client, "NewAppContainerClient should return a non-nil appContainerClient")
	assert.Equal(t, "test-name", client.name, "Name should be set correctly")
	assert.Equal(t, "test-image", client.image, "Image should be set correctly")
	assert.Equal(t, "test-cmd", client.cmd, "Cmd should be set correctly")
}

func TestBuildInitContainer(t *testing.T) {
	client := NewAppContainerClient("init-container", "test-image", "test-cmd")
	initContainer := client.BuildInitContainer()

	assert.NotNil(t, initContainer, "BuildInitContainer should return a non-nil container")
	assert.Equal(t, "init-container", initContainer.Name, "Container name should be set correctly")
	assert.Equal(t, "test-image", initContainer.Image, "Container image should be set correctly")

	// Check volume mounts
	assert.Len(t, initContainer.VolumeMounts, 1, "There should be one volume mount")
	assert.Equal(t, AppVolumeName, initContainer.VolumeMounts[0].Name, "VolumeMount name should match AppVolumeName")
	assert.Equal(t, "/app", initContainer.VolumeMounts[0].MountPath, "VolumeMount path should be '/app'")

	// Check command and args
	assert.ElementsMatch(t, initContainer.Args, append(terminalMode, "test-cmd"), "Args should include terminal mode and cmd")
}

func TestBuildVolumeMount(t *testing.T) {
	client := NewAppContainerClient("test", "test-image", "test-cmd")
	dataSource := &systemv1alpha1.DataSource{
		Name:      "test-volume",
		MountPath: "/data",
		ReadOnly:  true,
	}

	volumeMount := client.buildVolumeMount(dataSource)
	assert.Equal(t, "test-volume", volumeMount.Name, "VolumeMount name should be set correctly")
	assert.Equal(t, "/data", volumeMount.MountPath, "VolumeMount path should be set correctly")
	assert.True(t, volumeMount.ReadOnly, "VolumeMount ReadOnly should be true")
}

func TestBuildMainVolumeMount(t *testing.T) {
	client := NewAppContainerClient("test", "test-image", "test-cmd")
	volumeMounts := []v1.VolumeMount{}

	client.BuildMainVolumeMount(&volumeMounts)
	assert.Len(t, volumeMounts, 1, "There should be one volume mount added")
	assert.Equal(t, AppVolumeName, volumeMounts[0].Name, "VolumeMount name should match AppVolumeName")
	assert.Equal(t, "/app", volumeMounts[0].MountPath, "VolumeMount path should be '/app'")

	// Test adding again (no duplicates)
	client.BuildMainVolumeMount(&volumeMounts)
	assert.Len(t, volumeMounts, 1, "There should still be one volume mount")
}

func TestBuildAppVolume(t *testing.T) {
	client := NewAppContainerClient("test", "test-image", "test-cmd")
	volumes := []v1.Volume{}

	client.BuildAppVolume(&volumes)
	assert.Len(t, volumes, 1, "There should be one volume added")
	assert.Equal(t, AppVolumeName, volumes[0].Name, "Volume name should match AppVolumeName")

	// Test adding again (no duplicates)
	client.BuildAppVolume(&volumes)
	assert.Len(t, volumes, 1, "There should still be one volume")
}

func TestBuildVolume(t *testing.T) {
	client := NewAppContainerClient("test", "test-image", "test-cmd")
	dataSource := &systemv1alpha1.DataSource{
		Name: AppVolumeName,
	}

	volume := client.buildVolume(dataSource)
	assert.Equal(t, AppVolumeName, volume.Name, "Volume name should be set correctly")
	assert.NotNil(t, volume.VolumeSource.EmptyDir, "Volume source should be an EmptyDir")
}

func TestConstants(t *testing.T) {
	assert.ElementsMatch(t, terminalMode, []string{"sh", "-c"}, "terminalMode should be ['sh', '-c']")
	assert.Equal(t, "mount-app", AppVolumeName, "AppVolumeName should be 'mount-app'")
}
