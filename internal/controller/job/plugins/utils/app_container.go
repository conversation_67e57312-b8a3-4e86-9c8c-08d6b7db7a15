package utils

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	v1 "k8s.io/api/core/v1"
)

var (
	terminalMode  = []string{"sh", "-c"}
	AppVolumeName = "mount-app"
)

type appContainerClient struct {
	name  string
	image string
	cmd   string
}

func NewAppContainerClient(name, image, cmd string) *appContainerClient { //nolint
	return &appContainerClient{
		name:  name,
		image: image,
		cmd:   cmd,
	}
}

func (ic *appContainerClient) BuildInitContainer() *v1.Container {
	initContainer := v1.Container{
		Image: ic.image,
		Name:  ic.name,
	}

	initContainer.VolumeMounts = append(
		initContainer.VolumeMounts,
		*ic.buildVolumeMount(
			&systemv1alpha1.DataSource{
				Name:      AppVolumeName,
				MountPath: "/app",
				ReadOnly:  false,
			},
		),
	)

	initContainer.Args = append(initContainer.Args, terminalMode...)
	initContainer.Args = append(initContainer.Args, ic.cmd)

	return &initContainer
}

func (ic *appContainerClient) buildVolumeMount(storage *systemv1alpha1.DataSource) *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      storage.Name,
		MountPath: storage.MountPath,
		ReadOnly:  storage.ReadOnly,
	}
}

// 去重
func (ic *appContainerClient) BuildMainVolumeMount(volumeMounts *[]v1.VolumeMount) {
	var isExist bool
	for _, v := range *volumeMounts {
		if v.Name == AppVolumeName {
			isExist = true
			break
		}
	}

	if !isExist {
		*volumeMounts = append(*volumeMounts, v1.VolumeMount{
			Name:      AppVolumeName,
			MountPath: "/app",
			ReadOnly:  false,
		})
	}
}

func (ic *appContainerClient) BuildAppVolume(volumes *[]v1.Volume) {
	var isExist bool
	for _, v := range *volumes {
		if v.Name == AppVolumeName {
			isExist = true
			break
		}
	}

	if !isExist {
		*volumes = append(
			*volumes,
			*ic.buildVolume(
				&systemv1alpha1.DataSource{
					Name: AppVolumeName,
				},
			),
		)

	}
}

func (ic *appContainerClient) buildVolume(storage *systemv1alpha1.DataSource) *v1.Volume {
	return &v1.Volume{
		Name: storage.Name,
		VolumeSource: v1.VolumeSource{
			EmptyDir: &v1.EmptyDirVolumeSource{},
		},
	}
}
