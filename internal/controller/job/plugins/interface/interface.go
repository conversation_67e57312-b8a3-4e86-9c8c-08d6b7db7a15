package pluginsinterface

import (
	"sigs.k8s.io/controller-runtime/pkg/client"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller/config"
)

type PluginInterface interface {
	CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcJob *batchvc.Job) error
	CreateVJNotebookPlugins(notebook *systemv1alpha1.Notebook, vcJob *batchvc.Job) error
	//DeleteVolcanojobPlugin() error
}

type PluginClientset struct {
	KubeClients   client.Client //kubernetes.Interface // PluginClientset clientset. ssh plugin ...
	IsMultiApp    bool
	IngressConfig config.IngressConfig
	VncBaseURL    string
	TokenPath     string
}
