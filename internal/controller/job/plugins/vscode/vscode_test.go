package vscode

import (
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"testing"

	"github.com/stretchr/testify/assert"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

type MockFrontAction struct{}

func (m *MockFrontAction) Create(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) Exist(job *systemv1alpha1.Notebook) bool {
	return true
}

func (m *MockFrontAction) Delete(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) FrontPath(nameSpace, jobName string) map[string]string {
	return nil
}

func (m *MockFrontAction) GetWebURL() string {
	return "http://example.com"
}

func TestNew(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)

	assert.NotNil(t, clt, "New should return a non-nil vscodePlugin")
	assert.Equal(t, VscodePluginName, clt.(*vscodePlugin).resbuilder.Kind, "Kind should be VscodePluginName")
}

func TestCreateVolcanojobPlugins(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	vp := &vscodePlugin{
		client:     client,
		resbuilder: &pluginResBuilder.DefaultCapability{},
	}

	trainjob := &systemv1alpha1.TrainingJob{}
	vcjob := &batchvc.Job{}

	err := vp.CreateVolcanojobPlugins(trainjob, vcjob)
	assert.NoError(t, err, "CreateVolcanojobPlugins should not return an error")
}

func TestCreateVJNotebookPlugins(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	vp := &vscodePlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			FrontAction: &MockFrontAction{},
		},
	}

	notebookjob := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-notebook",
		},
	}
	vcjob := &batchvc.Job{}

	err := vp.CreateVJNotebookPlugins(notebookjob, vcjob)
	assert.NoError(t, err, "CreateVJNotebookPlugins should not return an error")
	assert.Equal(t, "http://example.com", notebookjob.Status.Vscode.URL, "Notebook status should contain the Vscode URL")
}

func TestServiceName(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	vp := &vscodePlugin{
		client:     client,
		resbuilder: &pluginResBuilder.DefaultCapability{},
	}

	jobName := "test-job"
	expectedServiceName := "vscode-test-job-svc"
	actualServiceName := vp.serviceName(VscodePluginName, jobName)

	assert.Equal(t, expectedServiceName, actualServiceName, "serviceName should return the correct service name")
}
