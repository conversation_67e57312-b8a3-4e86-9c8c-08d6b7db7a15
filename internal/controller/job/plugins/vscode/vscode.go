package vscode

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	"k8s.io/klog"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

var (
	VscodePluginName    = "vscode"
	vscodeContainerPort = 8082
)

type vscodePlugin struct {
	client     *pluginsinterface.PluginClientset
	resbuilder *pluginResBuilder.DefaultCapability
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	rewritePath := "/$1"
	format := "/%s/%s/"
	return &vscodePlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			Kind:         VscodePluginName,
			BaseURLKey:   nil,
			OffNetPolicy: true,
			FrontAction:  &pluginResBuilder.IngressAction{Kind: VscodePluginName, Port: vscodeContainerPort, Client: client.KubeClients, RewritePath: &rewritePath, Regular: "(.*)", IngressPathFormat: &format},
			BackEndActions: []pluginResBuilder.Action{
				&pluginResBuilder.ServiceAction{Kind: VscodePluginName, Client: client.KubeClients, SelectorFunc: pluginResBuilder.DefaultSelectFunc, Port: vscodeContainerPort},
			},
		},
	}
}

func (vp *vscodePlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {

	return nil
}

func (vp *vscodePlugin) CreateVJNotebookPlugins(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	vp.client.IsMultiApp = true
	if err := vp.resbuilder.PreCreate(vcjob, notebookjob); err != nil {
		klog.Errorf("plugin %s err in PreCreate, err : %s", VscodePluginName, err.Error())
		return err
	}

	vcjobbuilder.RegisterCmd(VscodePluginName, "")
	if err := vp.resbuilder.Create(notebookjob, vp.serviceName(VscodePluginName, notebookjob.Name)); err != nil {
		return err
	}
	if len(vp.resbuilder.GetURL()) != 0 {
		notebookjob.Status.Vscode.URL = vp.resbuilder.GetURL()
	}
	return nil
}

func (vp *vscodePlugin) serviceName(kind string, jobName string) string {
	return kind + "-" + jobName + "-svc"
}
