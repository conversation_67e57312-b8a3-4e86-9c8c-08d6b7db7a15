package autotrain

import (
	"context"
	"encoding/base64"

	"hero.ai/hero-controllers/internal/controller/config"

	"github.com/pkg/errors"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	"volcano.sh/apis/pkg/apis/bus/v1alpha1"
)

const (
	PluginName          = "auto-train"
	ConfigMapNameSuffix = "-training-params-configmap"
	ConfigMountFileName = "training_params.json"
	ConfigMountDir      = "/app/training-params"
	VolumeMountName     = "training-params"
	HfEndpointEnvKey    = "HF_ENDPOINT"
	// "http://10.0.101.71" 进行base64内容加密，为通过代码安全审查
	HfEndpointEnvValue          = "aHR0cDovLzEwLjAuMTAxLjcx"
	ParamsPathEnvKey            = "PARAMS_FILE_PATH"
	TrainingParamsAnnotationKey = "finetune-data"
)

type autotrainPlugin struct {
	client        *pluginsinterface.PluginClientset
	ConfigMapName string
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	return &autotrainPlugin{client: client}
}

func (atp *autotrainPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	vcjob.Spec.Policies = append(vcjob.Spec.Policies,
		batchvc.LifecyclePolicy{Event: v1alpha1.TaskCompletedEvent, Action: v1alpha1.CompleteJobAction},
		batchvc.LifecyclePolicy{Event: v1alpha1.TaskFailedEvent, Action: v1alpha1.AbortJobAction},
		batchvc.LifecyclePolicy{Event: v1alpha1.PodFailedEvent, Action: v1alpha1.AbortJobAction},
	)
	if err := atp.CreateAutoTrainConfigMapResource(trainjob); err != nil {
		return err
	}

	for i := range trainjob.Spec.Tasks {
		atp.buildVolumeSource(&vcjob.Spec.Tasks[i].Template.Spec.Volumes)
		container0 := &vcjob.Spec.Tasks[i].Template.Spec.Containers[0]
		container0.VolumeMounts = append(container0.VolumeMounts, *atp.buildVolumeMount())
		hfEndpoint := config.EC.HfEndpoint
		if hfEndpoint == "" {
			// 为通过代码安全审查，将 HfEndpointEnvValue 进行base64解密使用
			decoded, _ := base64.StdEncoding.DecodeString(HfEndpointEnvValue)
			hfEndpoint = string(decoded)
		}
		container0.Env = append(container0.Env, v1.EnvVar{
			Name:  HfEndpointEnvKey,
			Value: hfEndpoint,
		}, v1.EnvVar{
			Name:  ParamsPathEnvKey,
			Value: ConfigMountDir + "/" + ConfigMountFileName,
		})
		container0.Command = []string{"/bin/sh", "-c"}
		container0.Args = []string{"PATH=$PATH:/app/env/bin /app/env/bin/python /app/autotrain-wrapper.py"}
	}

	return nil
}

func (atp *autotrainPlugin) CreateVJNotebookPlugins(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	return nil
}

func (atp *autotrainPlugin) buildVolumeMount() *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      VolumeMountName,
		MountPath: ConfigMountDir,
		ReadOnly:  false,
	}
}

func (atp *autotrainPlugin) buildVolumeSource(volumes *[]v1.Volume) {
	*volumes = append(*volumes, v1.Volume{
		Name: VolumeMountName,
		VolumeSource: v1.VolumeSource{
			ConfigMap: &v1.ConfigMapVolumeSource{
				LocalObjectReference: v1.LocalObjectReference{
					Name: atp.ConfigMapName,
				},
			},
		},
	})
}

func (atp *autotrainPlugin) CreateAutoTrainConfigMapResource(trainjob *systemv1alpha1.TrainingJob) error {
	atp.ConfigMapName = "trainingjob-" + trainjob.Name + ConfigMapNameSuffix
	refs := []metav1.OwnerReference{
		{
			APIVersion: "system.hero.ai/v1alpha1",
			Kind:       "TrainingJob",
			Name:       trainjob.Name,
			UID:        trainjob.UID,
		},
	}
	configMap := &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:            atp.ConfigMapName,
			Namespace:       trainjob.Namespace,
			OwnerReferences: refs,
		},
	}
	if value, found := trainjob.Annotations[TrainingParamsAnnotationKey]; found {
		configMap.Data = map[string]string{ConfigMountFileName: value}
	} else {
		return errors.New("training params not found")
	}
	if err := atp.client.KubeClients.Create(context.TODO(), configMap); err != nil {
		return err
	}
	return nil
}
