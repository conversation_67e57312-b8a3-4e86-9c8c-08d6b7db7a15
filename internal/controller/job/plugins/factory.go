package plugins

import (
	"sync"

	"hero.ai/hero-controllers/internal/controller/job/plugins/autotrain"
	"hero.ai/hero-controllers/internal/controller/job/plugins/colossalai"
	"hero.ai/hero-controllers/internal/controller/job/plugins/customize"
	"hero.ai/hero-controllers/internal/controller/job/plugins/deepspeed"
	"hero.ai/hero-controllers/internal/controller/job/plugins/distributed-framework/pytorch"
	"hero.ai/hero-controllers/internal/controller/job/plugins/distributed-framework/tensorflow"
	"hero.ai/hero-controllers/internal/controller/job/plugins/gotty"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/plugins/jupyter"
	"hero.ai/hero-controllers/internal/controller/job/plugins/ssh"
	"hero.ai/hero-controllers/internal/controller/job/plugins/vnc"
	"hero.ai/hero-controllers/internal/controller/job/plugins/vscode"
)

func init() {
	RegisterPluginBuilder("jupyter", jupyter.New)
	RegisterPluginBuilder("web-terminal", gotty.New)
	RegisterPluginBuilder("vscode", vscode.New)
	RegisterPluginBuilder("tensorflow", tensorflow.New)
	RegisterPluginBuilder("pytorch", pytorch.New)
	RegisterPluginBuilder("ssh", ssh.New)
	RegisterPluginBuilder("vnc", vnc.New)
	RegisterPluginBuilder(deepspeed.DeepSpeedPluginName, deepspeed.New)
	RegisterPluginBuilder(colossalai.ColossalAIPluginName, colossalai.New)
	RegisterPluginBuilder(autotrain.PluginName, autotrain.New)
	RegisterPluginBuilder(customize.PluginName, customize.New)
}

var pluginMutex sync.Mutex

// Plugin management.
var pluginBuilders = map[string]PluginBuilder{}

// PluginBuilder func prototype.
type PluginBuilder func(*pluginsinterface.PluginClientset) pluginsinterface.PluginInterface

// RegisterPluginBuilder register plugin builders.
func RegisterPluginBuilder(name string, pc PluginBuilder) {
	pluginMutex.Lock()
	defer pluginMutex.Unlock()

	pluginBuilders[name] = pc
}

// GetPluginBuilder returns plugin builder for a given plugin name.
func GetPluginBuilder(name string) (PluginBuilder, bool) {
	pluginMutex.Lock()
	defer pluginMutex.Unlock()

	pb, found := pluginBuilders[name]
	return pb, found
}
