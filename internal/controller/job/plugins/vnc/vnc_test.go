package vnc

import (
	"encoding/base64"
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"testing"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	"github.com/stretchr/testify/assert"
	"hero.ai/hero-controllers/internal/controller/job/plugins/interface"
)

type MockFrontAction struct{}

func (m *MockFrontAction) Create(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) Exist(job *systemv1alpha1.Notebook) bool {
	return true
}

func (m *MockFrontAction) Delete(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) FrontPath(nameSpace, jobName string) map[string]string {
	return nil
}

func (m *MockFrontAction) GetWebURL() string {
	return "http://example.com"
}

func TestNew(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)

	assert.NotNil(t, clt, "New should return a non-nil vncPlugin")
	assert.Equal(t, VNCPluginName, clt.(*vncPlugin).resbuilder.Kind, "Kind should match VNCPluginName")
	assert.Equal(t, "VNC_BASE_URL", *clt.(*vncPlugin).resbuilder.BaseURLKey, "BaseURLKey should match")
	assert.True(t, clt.(*vncPlugin).resbuilder.OffNetPolicy, "OffNetPolicy should be true")
	assert.Len(t, clt.(*vncPlugin).resbuilder.BackEndActions, 1, "There should be one back-end action")
}

func TestCreateVJNotebookPlugins(t *testing.T) {
	vp := &vncPlugin{
		client: &pluginsinterface.PluginClientset{
			IsMultiApp: false,
			TokenPath:  "./test_token.txt",
		},
		resbuilder: &pluginResBuilder.DefaultCapability{
			FrontAction: &MockFrontAction{},
		},
	}

	// Set up test data
	vcJob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Tasks: []batchvc.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{{
								Name:  "test-container",
								Image: "test-image",
							}},
						},
					},
				},
			},
		},
	}

	notebookJob := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "default",
		},
	}

	// Execute the method
	err := vp.CreateVJNotebookPlugins(notebookJob, vcJob)

	assert.NoError(t, err, "CreateVJNotebookPlugins should not return an error")

	// Verify password creation
	assert.Len(t, vcJob.Spec.Tasks[0].Template.Spec.Containers[0].Env, 1, "There should be one environment variable for VNC password")
	assert.Equal(t, envVNCPWDKey, vcJob.Spec.Tasks[0].Template.Spec.Containers[0].Env[0].Name, "Environment variable name should match")
	assert.Len(t, notebookJob.ObjectMeta.Finalizers, 1, "Finalizers should include VncFinalizerName")

	// Verify VNC URL
	assert.NotEmpty(t, notebookJob.Status.Vnc.URL, "VNC URL should not be empty")
}

func TestCreateVNCPasswd(t *testing.T) {
	vp := &vncPlugin{}

	password := vp.CreateVNCPasswd(pwdLength)

	assert.Len(t, password, pwdLength, "Password length should match the specified length")
	for _, char := range password {
		assert.Contains(t, "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", string(char), "Password should contain only valid characters")
	}
}

func TestGetEnvVncUrlPre(t *testing.T) {
	// 为通过代码安全审查，将 vncBaseUrl 进行base64解密使用
	decoded, _ := base64.StdEncoding.DecodeString(vncBaseUrl)
	tests := []struct {
		vncUrl string
		expect string
	}{
		{"", string(decoded)},
		{"http://example.com", "http://example.com"},
	}

	for _, test := range tests {
		assert.Equal(t, test.expect, GetEnvVncUrlPre(test.vncUrl), "GetEnvVncUrlPre should return correct VNC URL")
	}
}

func TestGetEnvTokenPath(t *testing.T) {
	tests := []struct {
		tokenPath string
		expect    string
	}{
		{"", tokenPath},
		{"/custom/token/path", "/custom/token/path"},
	}

	for _, test := range tests {
		assert.Equal(t, test.expect, GetEnvTokenPath(test.tokenPath), "GetEnvTokenPath should return correct token path")
	}
}

func TestGetServiceName(t *testing.T) {
	kind := "test-kind"
	jobName := "test-job"

	expectedServiceName := "test-kind-test-job-svc"
	assert.Equal(t, expectedServiceName, GetServiceName(kind, jobName), "GetServiceName should return the correct service name")
}

func TestVncContainsString(t *testing.T) {
	tests := []struct {
		strings []string
		val     string
		expect  bool
	}{
		{[]string{"a", "b", "c"}, "b", true},
		{[]string{"a", "b", "c"}, "d", false},
	}

	for _, test := range tests {
		assert.Equal(t, test.expect, ContainsString(test.strings, test.val), "ContainsString should return correct result")
	}
}
