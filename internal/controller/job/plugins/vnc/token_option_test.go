package vnc

import (
	"io/ioutil"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUpdateToken(t *testing.T) {
	// Set up test variables
	namespace := "default"
	jobName := "test-job"
	tkPath := "./test_token.txt"

	// Clean up token file after the test
	defer os.Remove(tkPath)

	// Create a new token and check if it's generated
	token, err := UpdateToken(namespace, jobName, tkPath)
	assert.NoError(t, err, "UpdateToken should not return an error")
	assert.NotEmpty(t, token, "UpdateToken should return a non-empty token")

	// Check if the token file was created and contains the token
	tokenInfo, err := getTokenInfo(tkPath)
	assert.NoError(t, err, "getTokenInfo should not return an error")
	assert.NotEmpty(t, tokenInfo, "Token info should not be empty")
}

func TestDleleToken(t *testing.T) {
	// Set up test variables
	namespace := "default"
	jobName := "test-job"
	tkPath := "./test_token.txt"

	// Clean up token file after the test
	defer os.Remove(tkPath)

	// Create a token
	_, err := UpdateToken(namespace, jobName, tkPath)
	assert.NoError(t, err, "UpdateToken should not return an error")

	// Delete the token
	err = DleleToken(namespace, jobName, tkPath)
	assert.NoError(t, err, "DleleToken should not return an error")

	// Check if the token file is empty after deletion
	tokenInfo, err := getTokenInfo(tkPath)
	assert.NoError(t, err, "getTokenInfo should not return an error")
	assert.Empty(t, tokenInfo, "Token info should be empty after deletion")
}

func TestWirteTokenInfo(t *testing.T) {
	// Test writing to a file
	tkPath := "./test_write_token.txt"
	defer os.Remove(tkPath)

	tokenInfo := []string{"token1: test.namespace.svc.cluster.local:5901\n"}
	err := wirteTokenInfo(tkPath, tokenInfo, "DEFAULT")
	assert.NoError(t, err, "wirteTokenInfo should not return an error")

	// Read the file back and check content
	content, err := ioutil.ReadFile(tkPath)
	assert.NoError(t, err, "Reading file should not return an error")
	assert.Contains(t, string(content), "token1", "The token should be written to the file")
}

func TestGenerateUUID(t *testing.T) {
	uuid := generateUUID()
	assert.NotEmpty(t, uuid, "generateUUID should return a non-empty UUID")
	assert.Len(t, uuid, 32, "UUID should be 32 characters long without hyphens")
}

func TestGetTokenInfo(t *testing.T) {
	// Set up test variables
	tkPath := "./test_get_token.txt"
	defer os.Remove(tkPath)

	// Write a token to the file for reading
	tokenInfo := []string{"token1: test.namespace.svc.cluster.local:5901\n"}
	err := wirteTokenInfo(tkPath, tokenInfo, "DEFAULT")
	assert.NoError(t, err, "wirteTokenInfo should not return an error")

	// Now read the token back
	readTokens, err := getTokenInfo(tkPath)
	assert.NoError(t, err, "getTokenInfo should not return an error")
	assert.Len(t, readTokens, 1, "There should be one token read from the file")
	assert.Contains(t, readTokens[0], "token1", "The token should match what was written")
}

func TestFileisExist(t *testing.T) {
	existingFile := "./test_file_exist.txt"
	nonExistingFile := "./test_file_non_exist.txt"

	// Create a file to test existence
	defer os.Remove(existingFile)
	_, err := os.Create(existingFile)
	assert.NoError(t, err, "Creating a test file should not return an error")

	assert.True(t, FileisExist(existingFile), "FileisExist should return true for existing file")
	assert.False(t, FileisExist(nonExistingFile), "FileisExist should return false for non-existing file")
}

func TestTokenOptionContainsString(t *testing.T) {
	slice := []string{"apple", "banana", "cherry"}

	assert.True(t, ContainsString(slice, "banana"), "ContainsString should return true for existing item")
	assert.False(t, ContainsString(slice, "grape"), "ContainsString should return false for non-existing item")
}

func TestRemoveString(t *testing.T) {
	slice := []string{"apple", "banana", "cherry"}
	result := RemoveString(slice, "banana")

	assert.Len(t, result, 2, "Result should contain two items after removal")
	assert.NotContains(t, result, "banana", "Result should not contain the removed item")
	assert.Contains(t, result, "apple", "Result should still contain 'apple'")
	assert.Contains(t, result, "cherry", "Result should still contain 'cherry'")
}
