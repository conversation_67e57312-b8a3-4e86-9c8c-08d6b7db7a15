package vnc

import (
	"encoding/base64"
	"math/rand"
	"time"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/plugins/utils"
	v1 "k8s.io/api/core/v1"

	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"

	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const (
	VNCPluginName    = "vnc"
	VNCServerName    = "vncserver"
	vncPort          = 5901
	pwdLength        = 6
	envVNCPWDKey     = "VNCPWD"
	VNCBASEURLKEY    = "VNCBASEURL"
	TOKENPATHKEY     = "TOKENPATH"
	VncFinalizerName = "system.hero.ai/vnc"

	tokenPath = "/etc/websockify/token/token.conf"
	// "http://10.0.102.45:30086/vnc.html?path=websockify/?token=" 进行base64内容加密，为通过代码安全审查
	vncBaseUrl    = "aHR0cDovLzEwLjAuMTAyLjQ1OjMwMDg2L3ZuYy5odG1sP3BhdGg9d2Vic29ja2lmeS8/dG9rZW49"
	vncImage      = "registry.cnbita.com:5000/cloud-images/hero-user/vncstart:latest"
	containerName = "vnc-init"
)

var (
	cmd = " mkdir -p /app/vnc && cp /vncstart.sh /app/vnc/vncstart.sh "
)

type vncPlugin struct {
	client     *pluginsinterface.PluginClientset
	resbuilder *pluginResBuilder.DefaultCapability
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	baseURI := "VNC_BASE_URL"
	return &vncPlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			Kind:         VNCPluginName,
			BaseURLKey:   &baseURI,
			OffNetPolicy: true,
			BackEndActions: []pluginResBuilder.Action{
				&pluginResBuilder.ServiceAction{Kind: VNCPluginName, Client: client.KubeClients, SelectorFunc: pluginResBuilder.DefaultSelectFunc, Port: vncPort},
			},
		},
	}
}

func (vp *vncPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	return nil
}

func (vp *vncPlugin) CreateVJNotebookPlugins(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {

	cli := utils.NewAppContainerClient(containerName, vncImage, cmd)
	for k, task := range vcjob.Spec.Tasks {
		cli.BuildAppVolume(&vcjob.Spec.Tasks[k].Template.Spec.Volumes)
		if len(task.Template.Spec.InitContainers) == 0 {
			vcjob.Spec.Tasks[k].Template.Spec.InitContainers = []v1.Container{}
		}
		vcjob.Spec.Tasks[k].Template.Spec.InitContainers = append(vcjob.Spec.Tasks[k].Template.Spec.InitContainers, *cli.BuildInitContainer())
		for kk := range task.Template.Spec.Containers {
			cli.BuildMainVolumeMount(&vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].VolumeMounts)
		}
	}

	//写入密码
	vp.client.IsMultiApp = true
	vcjobbuilder.RegisterCmd(VNCPluginName, "")
	vcjobbuilder.RegisterCheckCmd(VNCPluginName, vncPort)

	vncPwd := vp.CreateVNCPasswd(pwdLength)
	pwdEnv := v1.EnvVar{
		Name:  envVNCPWDKey,
		Value: vncPwd,
	}

	// 创建Finalizer
	if !ContainsString(notebookjob.ObjectMeta.Finalizers, VncFinalizerName) {
		notebookjob.ObjectMeta.Finalizers = append(notebookjob.ObjectMeta.Finalizers, VncFinalizerName)
	}

	vcjob.Spec.Tasks[0].Template.Spec.Containers[0].Env = append(vcjob.Spec.Tasks[0].Template.Spec.Containers[0].Env, pwdEnv)

	if err := vp.resbuilder.Create(notebookjob, GetServiceName(VNCPluginName, notebookjob.Name)); err != nil {
		return err
	}

	token, err := UpdateToken(notebookjob.Namespace, notebookjob.Name, vp.client.TokenPath)
	if err != nil {
		return err
	}
	notebookjob.Status.Vnc.VncPWD = vncPwd
	notebookjob.Status.Vnc.URL = GetEnvVncUrlPre(vp.client.VncBaseURL) + token

	return nil

}

func (vp *vncPlugin) CreateVNCPasswd(length int) string {
	rand.New(rand.NewSource(time.Now().UnixNano()))

	// 定义密码字符集合
	characters := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	// 生成6位密码
	password := ""
	for i := 0; i < length; i++ {
		index := rand.Intn(len(characters))
		password += string(characters[index])
	}

	return password
}

func GetEnvVncUrlPre(vncUrl string) string { //nolint
	if len(vncUrl) != 0 {
		return vncUrl
	}

	// 为通过代码安全审查，将 vncBaseUrl 进行base64解密使用
	decoded, _ := base64.StdEncoding.DecodeString(vncBaseUrl)

	return string(decoded)
}

func GetEnvTokenPath(tp string) string {
	if len(tp) != 0 {
		return tp
	}

	return tokenPath
}

func GetServiceName(kind string, jobName string) string {
	return kind + "-" + jobName + "-svc"
}
