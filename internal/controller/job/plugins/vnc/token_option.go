//nolint:all
package vnc

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/google/uuid"
)

func UpdateToken(nameSpace, jobName, tokenPath string) (string, error) {
	serviceName := GetServiceName(VNCPluginName, jobName)

	record := fmt.Sprintf("%s.%s.svc.cluster.local", serviceName, nameSpace)
	tp := GetEnvTokenPath(tokenPath)

	TokenInfo, err := getTokenInfo(tp)
	if err != nil {
		return "", err
	}

	for _, tokeninfo := range TokenInfo {
		if strings.Contains(tokeninfo, record) {
			token := strings.Split(tokeninfo, ":")[0]
			return token, nil
		}
	}

	uuid := generateUUID()
	tokeninfo := fmt.Sprintf("%s: %s:%s\n", uuid, record, fmt.Sprintf("%d", vncPort))

	err = wirteTokenInfo(tp, []string{tokeninfo}, "DEFAULT")
	if err != nil {
		return "", err
	}

	return uuid, nil

}

func DleleToken(nameSpace, jobName, tokenPath string) error {

	serviceName := GetServiceName(VNCPluginName, jobName)

	record := fmt.Sprintf("%s.%s.svc.cluster.local", serviceName, nameSpace)

	tp := GetEnvTokenPath(tokenPath)

	TokenInfo, err := getTokenInfo(tp)
	if err != nil {
		return err
	}

	fondFlag := false

	newTokenList := []string{}
	for _, tokeninfo := range TokenInfo {
		if strings.Contains(tokeninfo, record) {
			fondFlag = true
		} else {
			newTokenList = append(newTokenList, tokeninfo+"\n")
		}

	}

	if fondFlag {
		err = wirteTokenInfo(tp, newTokenList, "RERWITE")
		if err != nil {
			return err
		}
	}

	return nil

}
func wirteTokenInfo(filepath string, tokenInfoList []string, mode string) error {

	var f *os.File
	var err error

	if !FileisExist(filepath) {
		_, err = os.Create(filepath)
		if err != nil {
			fmt.Println("file create fail")
			return err
		}
	}

	if mode == "RERWITE" && FileisExist(filepath) {
		f, err = os.OpenFile(filepath, os.O_WRONLY|os.O_TRUNC|os.O_CREATE, 0644)
	} else {
		f, err = os.OpenFile(filepath, os.O_WRONLY|os.O_APPEND, 0666)
	}

	if err != nil {
		fmt.Println("file open failed", err)
		return err
	}

	write := bufio.NewWriter(f)
	for _, tokeninfo := range tokenInfoList {
		write.WriteString(tokeninfo)

	}
	write.Flush()
	f.Close()

	return nil

}

func generateUUID() string {

	uuidWithHyphen := uuid.New()
	uuid := strings.Replace(uuidWithHyphen.String(), "-", "", -1)
	return uuid
}

func getTokenInfo(filepath string) ([]string, error) {

	if !FileisExist(filepath) {
		_, err := os.Create(filepath) //创建文件
		if err != nil {
			fmt.Println("file create fail:", filepath)
		}
		return nil, err
	}

	fileHandle, err := os.OpenFile(filepath, os.O_RDONLY, 0666)

	if err != nil {
		return nil, err
	}

	reader := bufio.NewReader(fileHandle)

	var results []string
	for {
		line, _, err := reader.ReadLine()
		if err == io.EOF {
			break
		}
		results = append(results, string(line))
	}
	fileHandle.Close()
	return results, nil
}

func FileisExist(fileName string) bool {
	_, err := os.Stat(fileName)

	// check if error is "file not exists"
	if os.IsNotExist(err) {
		return false
	} else {
		return true
	}
}

func ContainsString(slice []string, s string) bool {
	for _, item := range slice {
		if item == s {
			return true
		}
	}
	return false
}

func RemoveString(slice []string, s string) (result []string) {
	for _, item := range slice {
		if item == s {
			continue
		}
		result = append(result, item)
	}
	return
}
