package ssh

import (
	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"hero.ai/hero-controllers/internal/controller/job/vcjobbuilder"
	v1 "k8s.io/api/core/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const (
	SSHPluginName     = "ssh"
	SSHServerName     = "sshd"
	sshPort           = 22
	sshAbsolutePath   = "/root/.ssh"
	sshAuthorizedKeys = "authorized_keys"
)

type sshPlugin struct {
	client     *pluginsinterface.PluginClientset
	resbuilder *pluginResBuilder.DefaultCapability
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	baseURI := "SSH_BASE_URL"
	return &sshPlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			Kind:         SSHPluginName,
			BaseURLKey:   &baseURI,
			OffNetPolicy: true,
			BackEndActions: []pluginResBuilder.Action{
				&pluginResBuilder.ServiceAction{Kind: SSHPluginName, Client: client.KubeClients, SelectorFunc: pluginResBuilder.DefaultSelectFunc, Port: sshPort},
			},
		},
	}
}

func (sp *sshPlugin) CreateVolcanojobPlugins(trainjob *systemv1alpha1.TrainingJob, vcjob *batchvc.Job) error {
	return nil
}

func (sp *sshPlugin) CreateVJNotebookPlugins(notebookjob *systemv1alpha1.Notebook, vcjob *batchvc.Job) error {
	sp.client.IsMultiApp = true
	if len(notebookjob.Spec.SecretName) != 0 {
		vcjobbuilder.RegisterCmd(SSHPluginName, "")
		vcjobbuilder.RegisterCheckCmd(SSHPluginName, sshPort)
		for k, task := range vcjob.Spec.Tasks {
			sp.buildVolume(notebookjob.Spec.SecretName, &vcjob.Spec.Tasks[k].Template.Spec.Volumes)
			for kk := range task.Template.Spec.Containers {
				vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].VolumeMounts = append(vcjob.Spec.Tasks[k].Template.Spec.Containers[kk].VolumeMounts, *sp.buildVolumeMount(
					notebookjob.Spec.SecretName,
				))
			}
		}
	}

	return sp.resbuilder.Create(notebookjob, notebookjob.Name)
}

func (sp *sshPlugin) buildVolumeMount(secretName string) *v1.VolumeMount {
	return &v1.VolumeMount{
		Name:      secretName,
		MountPath: sshAbsolutePath + "/" + sshAuthorizedKeys,
		SubPath:   sshAuthorizedKeys,
		ReadOnly:  false,
	}
}

func (sp *sshPlugin) buildVolume(secretName string, volumes *[]v1.Volume) {
	mode := int32(0600)
	secretVolumeSource := &v1.SecretVolumeSource{
		SecretName:  secretName,
		DefaultMode: &mode,
	}
	*volumes = append(*volumes, v1.Volume{
		Name: secretName,
		VolumeSource: v1.VolumeSource{
			Secret: secretVolumeSource,
		},
	})
}
