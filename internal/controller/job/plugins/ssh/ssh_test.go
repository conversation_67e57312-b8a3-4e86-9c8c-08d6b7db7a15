package ssh

import (
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
)

type MockFrontAction struct{}

func (m *MockFrontAction) Create(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) CreateTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) Exist(job *systemv1alpha1.Notebook) bool {
	return true
}

func (m *MockFrontAction) Delete(job *systemv1alpha1.Notebook) error {
	return nil
}

func (m *MockFrontAction) DeleteTJ(jobTJ *systemv1alpha1.TrainingJob, podSuffixList []string) error {
	return nil
}

func (m *MockFrontAction) FrontPath(nameSpace, jobName string) map[string]string {
	return nil
}

func (m *MockFrontAction) GetWebURL() string {
	return "http://example.com"
}

func TestNew(t *testing.T) {
	client := &pluginsinterface.PluginClientset{}
	clt := New(client)

	assert.NotNil(t, clt, "New should return a non-nil sshPlugin")
}

func TestCreateVolcanojobPlugins(t *testing.T) {
	sp := &sshPlugin{}

	// Create a mock TrainingJob
	trainjob := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-training-job",
		},
	}

	// Create a mock Volcano Job
	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{},
	}

	err := sp.CreateVolcanojobPlugins(trainjob, vcjob)
	assert.NoError(t, err, "CreateVolcanojobPlugins should not return an error")
}

func TestCreateVJNotebookPlugins(t *testing.T) {
	sp := &sshPlugin{
		client: &pluginsinterface.PluginClientset{
			IsMultiApp: false,
		},
		resbuilder: &pluginResBuilder.DefaultCapability{
			FrontAction: &MockFrontAction{},
		},
	}

	// Create a mock Notebook
	notebookjob := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-notebook",
		},
		Spec: systemv1alpha1.NotebookSpec{
			SecretName: "test-secret",
		},
	}

	// Create a mock Volcano Job
	vcjob := &batchvc.Job{
		Spec: batchvc.JobSpec{
			Tasks: []batchvc.TaskSpec{
				{
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{Name: "test-container"},
							},
						},
					},
				},
			},
		},
	}

	err := sp.CreateVJNotebookPlugins(notebookjob, vcjob)
	assert.NoError(t, err, "CreateVJNotebookPlugins should not return an error")

	// Check if the SSH command was registered
	assert.True(t, sp.client.IsMultiApp, "IsMultiApp should be set to true")

	// Check if volumes and volume mounts were added correctly
	assert.Len(t, vcjob.Spec.Tasks[0].Template.Spec.Volumes, 1, "There should be one volume added")
	assert.Equal(t, "test-secret", vcjob.Spec.Tasks[0].Template.Spec.Volumes[0].Name, "Volume name should match the secret name")

	assert.Len(t, vcjob.Spec.Tasks[0].Template.Spec.Containers[0].VolumeMounts, 1, "There should be one volume mount added")
	assert.Equal(t, sshAbsolutePath+"/"+sshAuthorizedKeys, vcjob.Spec.Tasks[0].Template.Spec.Containers[0].VolumeMounts[0].MountPath, "MountPath should be set correctly")
}

func TestBuildVolumeMount(t *testing.T) {
	sp := &sshPlugin{}
	secretName := "test-secret"

	volumeMount := sp.buildVolumeMount(secretName)
	assert.Equal(t, secretName, volumeMount.Name, "VolumeMount name should match the secret name")
	assert.Equal(t, sshAbsolutePath+"/"+sshAuthorizedKeys, volumeMount.MountPath, "MountPath should be set correctly")
	assert.Equal(t, sshAuthorizedKeys, volumeMount.SubPath, "SubPath should be set correctly")
	assert.False(t, volumeMount.ReadOnly, "ReadOnly should be false")
}

func TestBuildVolume(t *testing.T) {
	sp := &sshPlugin{}
	secretName := "test-secret"
	var volumes []v1.Volume

	sp.buildVolume(secretName, &volumes)
	assert.Len(t, volumes, 1, "There should be one volume added")
	assert.Equal(t, secretName, volumes[0].Name, "Volume name should match the secret name")

	// Check secret volume source details
	assert.NotNil(t, volumes[0].VolumeSource.Secret, "VolumeSource.Secret should not be nil")
	assert.Equal(t, secretName, volumes[0].VolumeSource.Secret.SecretName, "SecretName should match the input secret name")
	assert.Equal(t, int32(0600), *volumes[0].VolumeSource.Secret.DefaultMode, "DefaultMode should be set to 0600")
}

func TestConstants(t *testing.T) {
	assert.Equal(t, "ssh", SSHPluginName, "SSHPluginName should be 'ssh'")
	assert.Equal(t, "sshd", SSHServerName, "SSHServerName should be 'sshd'")
	assert.Equal(t, 22, sshPort, "sshPort should be 22")
	assert.Equal(t, "/root/.ssh", sshAbsolutePath, "sshAbsolutePath should be '/root/.ssh'")
	assert.Equal(t, "authorized_keys", sshAuthorizedKeys, "sshAuthorizedKeys should be 'authorized_keys'")
}
