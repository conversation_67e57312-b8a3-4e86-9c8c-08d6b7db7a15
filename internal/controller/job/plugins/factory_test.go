package plugins

import (
	"github.com/stretchr/testify/assert"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	"testing"
)

func TestRegisterPluginBuilder(t *testing.T) {
	pluginName := "test-plugin"
	mockPluginBuilder := func(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
		return nil
	}

	RegisterPluginBuilder(pluginName, mockPluginBuilder)

	// Check if the plugin builder was registered correctly
	builder, found := GetPluginBuilder(pluginName)
	assert.True(t, found, "Plugin builder should be found")
	assert.NotNil(t, builder, "Builder should not be nil")
}

func TestGetPluginBuilder(t *testing.T) {
	pluginName := "jupyter"

	builder, found := GetPluginBuilder(pluginName)
	assert.True(t, found, "Expected to find the plugin builder")
	assert.NotNil(t, builder, "Builder should not be nil")
}
