package customize

import (
	"fmt"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	pluginResBuilder "hero.ai/hero-controllers/internal/controller/job/plugin-res-builder"
	pluginsinterface "hero.ai/hero-controllers/internal/controller/job/plugins/interface"
	batchvc "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

const (
	PluginName = "customize"
	Port       = 65534
)

type CustomizePlugin struct {
	client     *pluginsinterface.PluginClientset
	resbuilder *pluginResBuilder.DefaultCapability
}

func New(client *pluginsinterface.PluginClientset) pluginsinterface.PluginInterface {
	regular := `(.*)`
	rewritePath := `/$1`
	return &CustomizePlugin{
		client: client,
		resbuilder: &pluginResBuilder.DefaultCapability{
			Kind: PluginName,
			FrontAction: &pluginResBuilder.IngressAction{
				Kind:        PluginName,
				Client:      client.KubeClients,
				Regular:     regular,
				RewritePath: &rewritePath,
				IngressBuilder: pluginResBuilder.IngressBuilder{
					Config: client.IngressConfig,
				}},
			BackEndActions: []pluginResBuilder.Action{
				&pluginResBuilder.ServiceAction{Kind: PluginName, Client: client.KubeClients, SelectorFunc: pluginResBuilder.DefaultSelectFunc, Port: Port},
			},
		},
	}
}

func (sp *CustomizePlugin) CreateVolcanojobPlugins(_ *systemv1alpha1.TrainingJob, _ *batchvc.Job) error {
	return nil
}

func (sp *CustomizePlugin) CreateVJNotebookPlugins(job *systemv1alpha1.Notebook, _ *batchvc.Job) error {
	return sp.resbuilder.Create(job, fmt.Sprintf("%s-%s", PluginName, job.GetName()))
}
