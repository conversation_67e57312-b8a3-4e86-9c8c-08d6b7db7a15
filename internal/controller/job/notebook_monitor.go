package job

import (
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"k8s.io/klog"
)

var statusChan = make(chan *info, 1024)

func InitServer(monitorAddress string) *http.Server {
	return &http.Server{
		Handler:      newRouter(),
		Addr:         monitorAddress,
		WriteTimeout: 60 * time.Second,
		ReadTimeout:  30 * time.Second,
	}
}

func newRouter() *mux.Router {
	r := mux.NewRouter()
	addHandlers(r)
	return r
}

type info struct {
	jobname string
	plugin  string
	state   string
}

func addHandlers(r *mux.Router) {
	r.HandleFunc("/api/v1/plugin-status/update", pluginStatsUpdate)
}

func pluginStatsUpdate(w http.ResponseWriter, r *http.Request) {
	jobname := r.FormValue("jobname")
	plugin := r.FormValue("plugin")
	state := r.FormValue("state")
	klog.Infof("update plugin status, request: %v", r)
	statusChan <- &info{
		jobname: jobname,
		plugin:  plugin,
		state:   state,
	}
}
