package job

import (
	"context"
	"hero.ai/hero-controllers/internal/controller/job/state"
	"testing"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
)

func TestTrainingJobReconcile(t *testing.T) {
	scheme := runtime.NewScheme()
	_ = systemv1alpha1.AddToScheme(scheme)

	// Create a fake client with the TrainingJob resource
	k8sFakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	r := &TrainingJobReconciler{
		Client: k8sFakeClient,
		Scheme: scheme,
	}

	// Create a TrainingJob object for testing
	trainJob := &systemv1alpha1.TrainingJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-trainingjob",
			Namespace: "default",
		},
		Spec: systemv1alpha1.TrainingJobSpec{
			// Fill in spec as necessary
		},
	}

	// Mock implementation for CreateJob
	state.CreateJob = func(ctx context.Context, job *systemv1alpha1.TrainingJob, fn state.UpdateJobStatusFn) error {
		if fn != nil {
			fn(&job.Status)
		}
		return nil
	}

	// Create the TrainingJob in the fake client
	err := r.Create(context.TODO(), trainJob)
	assert.NoError(t, err)

	// Test Reconcile
	req := ctrl.Request{
		NamespacedName: types.NamespacedName{
			Name:      "test-trainingjob",
			Namespace: "default",
		},
	}

	result, err := r.Reconcile(context.TODO(), req)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Optionally, check if the expected state or conditions are set after reconciliation
	var updatedJob systemv1alpha1.TrainingJob
	err = r.Get(context.TODO(), req.NamespacedName, &updatedJob)
	assert.NoError(t, err)

	// Example assertions, adjust based on actual implementation details
	assert.Equal(t, trainJob.Name, updatedJob.Name)
	assert.Equal(t, trainJob.Namespace, updatedJob.Namespace)
	// Add additional assertions based on the logic you expect in your reconcile method
}
