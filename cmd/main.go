//nolint:all
package main

import (
	"flag"
	// Import all Kubernetes client auth plugins (e.g. Azure, GCP, OIDC, etc.)
	// to ensure that exec-entrypoint and run can make use of them.
	_ "net/http/pprof"
	"os"

	"hero.ai/hero-controllers/internal/controller/event"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	_ "k8s.io/client-go/plugin/pkg/client/auth"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"

	batchvolcanoshv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	busvolcanoshv1alpha1 "volcano.sh/apis/pkg/apis/bus/v1alpha1"
	scheduingv1beta1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"

	systemv1alpha1 "hero.ai/hero-controllers/api/v1alpha1"
	"hero.ai/hero-controllers/internal/controller"
	"hero.ai/hero-controllers/internal/controller/actions"
	"hero.ai/hero-controllers/internal/controller/config"
	"hero.ai/hero-controllers/internal/controller/gc"
	im "hero.ai/hero-controllers/internal/controller/imagemaker"
	controllerjob "hero.ai/hero-controllers/internal/controller/job"
	controllerResPool "hero.ai/hero-controllers/internal/controller/resourcepool"
	//+kubebuilder:scaffold:imports
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))
	utilruntime.Must(batchvolcanoshv1alpha1.AddToScheme(scheme))
	utilruntime.Must(busvolcanoshv1alpha1.AddToScheme(scheme))
	utilruntime.Must(scheduingv1beta1.AddToScheme(scheme))
	//+kubebuilder:scaffold:scheme

}

func main() {
	var metricsAddr string
	var enableLeaderElection bool
	var probeAddr string
	flag.StringVar(&metricsAddr, "metrics-bind-address", ":8080", "The address the metric endpoint binds to.")
	flag.StringVar(&probeAddr, "health-probe-bind-address", ":8081", "The address the probe endpoint binds to.")
	flag.BoolVar(&enableLeaderElection, "leader-elect", false,
		"Enable leader election for controller manager. "+
			"Enabling this will ensure there is only one active controller manager.")
	opts := zap.Options{
		Development: true,
	}
	opts.BindFlags(flag.CommandLine)
	flag.Parse()

	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&opts)))

	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme:                 scheme,
		MetricsBindAddress:     metricsAddr,
		Port:                   9443,
		HealthProbeBindAddress: probeAddr,
		LeaderElection:         enableLeaderElection,
		LeaderElectionID:       "ddac1ca8.hero.ai",
		// CertDir:                "./cert/",
		// LeaderElectionReleaseOnCancel defines if the leader should step down voluntarily
		// when the Manager ends. This requires the binary to immediately end when the
		// Manager is stopped, otherwise, this setting is unsafe. Setting this significantly
		// speeds up voluntary leader transitions as the new leader don't have to wait
		// LeaseDuration time first.
		//
		// In the default scaffold provided, the program ends immediately after
		// the manager stops, so would be fine to enable this option. However,
		// if you are doing or is intended to do any operation such as perform cleanups
		// after the manager stops then its usage might be unsafe.
		// LeaderElectionReleaseOnCancel: true,
	})
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}

	var startChan = make(chan struct{}, 1)
	go controllerjob.StartCheckLicense(startChan, config.SC.LicenseURL)

	<-startChan

	if err := config.CreateOrUpdateResource(mgr.GetClient()); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}
	controllerjob.InitRsaKey(config.EC.RSA.Private_Key, config.EC.RSA.Public_Key)

	ObjectGarbageCollector := &gc.ObjectGarbageCollector{}
	ObjectGarbageCollector.Register("Tensorboard", gc.NewTensorboardGarbageCollector(mgr.GetClient(), config.SC.ResourceRecycle))
	ObjectGarbageCollector.Register("TrainingJob", gc.NewTrainingjobGarbageCollector(mgr.GetClient(), config.SC.ResourceRecycle))
	ObjectGarbageCollector.Register("ImageMaker", gc.NewImageMakerGarbageCollector(mgr.GetClient(), config.SC.ResourceRecycle))
	ObjectGarbageCollector.Register("Notebook", gc.NewNotebookGarbageCollector(mgr.GetClient(), config.SC.ResourceRecycle))
	ObjectGarbageCollector.Register("syncaction", gc.NewSyncactionGarbageCollector(mgr.GetClient(), config.SC.ResourceRecycle))
	ObjectGarbageCollector.StartGC()

	ctx := ctrl.SetupSignalHandler()
	if err = (&controllerjob.NotebookReconciler{
		Client:        mgr.GetClient(),
		Scheme:        mgr.GetScheme(),
		EventRecord:   event.NewEventRecord(mgr.GetClient(), mgr.GetScheme()),
		Namespace:     config.SC.Namespace,
		IngressConfig: config.EC.Ingress,
		LocalURL:      config.SC.LocalURL,
		VncBaseURL:    config.EC.VncBaseURL,
		TokenPath:     config.SC.TokenPath,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "Notebook")
		os.Exit(1)
	}
	if err = (&controllerjob.TrainingJobReconciler{
		Client:        mgr.GetClient(),
		Scheme:        mgr.GetScheme(),
		EventRecord:   event.NewEventRecord(mgr.GetClient(), mgr.GetScheme()),
		Namespace:     config.SC.Namespace,
		IngressConfig: config.EC.Ingress,
		Context:       ctx,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "TrainingJob")
		os.Exit(1)
	}
	// if err = (&controller.ImageMakerReconciler{
	if err = (&im.ImageMakerReconciler{
		Client:       mgr.GetClient(),
		Scheme:       mgr.GetScheme(),
		Recorder:     mgr.GetEventRecorderFor("imagemaker"),
		ServerConfig: *config.SC,
		ImageConfig:  config.EC.Image,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "ImageMaker")
		os.Exit(1)
	}
	if err = (&controller.TensorboardReconciler{
		Client:        mgr.GetClient(),
		Scheme:        mgr.GetScheme(),
		Recorder:      mgr.GetEventRecorderFor("tensorboard"),
		IngressConfig: config.EC.Ingress,
		ServerConfig:  *config.SC,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "Tensorboard")
		os.Exit(1)
	}

	if err = (&controllerResPool.ResourcePoolReconciler{
		Client: mgr.GetClient(),
		Scheme: mgr.GetScheme(),
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "ResourcePool")
		os.Exit(1)
	}
	if err = (&actions.SyncActionReconciler{
		Client: mgr.GetClient(),
		Scheme: mgr.GetScheme(),
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "SyncAction")
		os.Exit(1)
	}
	if err = (&systemv1alpha1.TrainingJob{}).SetupWebhookWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create webhook", "webhook", "TrainingJob")
		os.Exit(1)
	}
	if err = (&systemv1alpha1.Notebook{}).SetupWebhookWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create webhook", "webhook", "Notebook")
		os.Exit(1)
	}
	if err = (&systemv1alpha1.Tensorboard{}).SetupWebhookWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create webhook", "webhook", "Tensorboard")
		os.Exit(1)
	}
	if err = (&systemv1alpha1.ImageMaker{}).SetupWebhookWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create webhook", "webhook", "ImageMaker")
		os.Exit(1)
	}

	if err = (&systemv1alpha1.ResourcePool{}).SetupWebhookWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create webhook", "webhook", "ResourcePool")
		os.Exit(1)
	}
	//+kubebuilder:scaffold:builder

	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		os.Exit(1)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		os.Exit(1)
	}

	httpServer := controllerjob.InitServer(":8088")
	go httpServer.ListenAndServe()
	go controllerjob.StartUpdateURLStatus(mgr.GetClient(), mgr.GetScheme(), config.SC.Namespace)
	setupLog.Info("starting manager")
	if err := mgr.Start(ctx); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}
}
