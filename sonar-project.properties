sonar.sourceEncoding=UTF-8
sonar.sources=.
sonar.language=go
sonar.inclusions=**/cmd/**,**/internal/controller/**,**/sonar/**,**/api/**
sonar.tests=.
sonar.test.inclusions=**/*_test.go
sonar.test.exclusions=**/vendor/**
sonar.go.tests.reportPaths=sonar/reports/test-report.out
sonar.go.coverage.reportPaths=sonar/reports/coverage.out
sonar.go.golangci-lint.reportPaths=sonar/reports/report.xml
sonar.qualitygate.wait=true