apiVersion: system.hero.ai/v1alpha1
kind: TrainingJob
metadata:
  # annotations:
  #   command.system.hero.ai: stop
  name: hang
  namespace: hero-user
spec:
  faultTolerance:
    healthChecks:
      # 定期检查 job 是否 hang 住
      # 检查结果保存在 .status.faultTolerance[*].healthChecks[*].result 字段
      # result 可能的值为 JobHang
      - name: JobHangCheck
        args:
          # 当超过 maxLogInterval 秒没有任何一个 pod 有日志输出，则判定 job hang 住
          maxLogInterval: 60
    restartPolicy:
      # 当判断 job 需要重启时，等待 delay 秒后，再创建新 pod 重启 job
      delay: 60
      # job 最大重启次数
      maxRetry: 2
      # 当 healthChecks 检查结果包含任何一个 policies 时，重启 job
      policies:
        - JobHang
  imageUrl: registry.cnbita.com:5000/dockerhub/ubuntu:22.04
  maxRetryCount: 3
  plugins:
    - web-terminal
  tasks:
    - command: |-
        echo the first log at beginning
        sleep 20
        echo the second log
        sleep 2m
      extendResource:
        cpuArch: amd64
      minAvaluable: 1
      name: worker
      replicas: 1
      resource:
        cpu: 100m
        memory: 128Mi
      resourcePool: default
status:
  conditions:
    - lastTransitionTime: "2025-02-13T12:27:11Z"
      status: Pending
    - lastTransitionTime: "2025-02-13T12:27:11Z"
      status: Queuing
    - lastTransitionTime: "2025-02-13T12:27:16Z"
      status: Running
    - lastTransitionTime: "2025-02-13T12:28:36Z"
      status: Restarting
    - lastTransitionTime: "2025-02-13T12:29:48Z"
      status: Pending
    - lastTransitionTime: "2025-02-13T12:30:35Z"
      status: Queuing
    - lastTransitionTime: "2025-02-13T12:30:41Z"
      status: Running
  createTime: 2025-02-13 20:27:11.744644959 +0800 CST m=+24.645307979
  faultTolerance:
    "0":
      createTime: "2025-02-13T12:27:11Z"
      startTime: "2025-02-13T12:27:16Z"
      completionTime: "2025-02-13T12:28:36Z"
      healthChecks:
        # 如果 job 始终没有 hang 住，则下面不会有对应的 healthCheck
        - name: JobHangCheck # 与 .spec.faultTolerance.healthChecks[*].name 对应
          result: JobHang # 如果该值存在于 .spec.faultTolerance.restartPolicy.policies 中会重启
          message: No logs more than 60 seconds
          time: "2025-02-13T12:28:36Z"
      phase: Stopped
      restartReasons: # 与 .spec.faultTolerance.restartPolicy.polices 对应
        - JobHang
    "1":
      createTime: "2025-02-13T12:30:35Z"
      startTime: "2025-02-13T12:30:41Z"
      phase: Running
  retryCount: 1
  serverDurations:
    1d2d6713-9b4d-445e-82d6-3d7e68a542a0:
      completedTime: ""
      launchedTime: 2025-02-13 20:30:40 +0800 CST
      name: tj-hang-restart-worker-0
      nodeName: yigou-dev-102-69
      phase: Running
      taskName: worker
      version: 1
    fe922c86-ffb8-407e-aa78-e4781adb5975:
      completedTime: 2025-02-13 20:28:37 +0800 CST
      launchedTime: 2025-02-13 20:27:16 +0800 CST
      name: tj-hang-restart-worker-0
      nodeName: yigou-dev-102-69
      phase: Terminated
      taskName: worker
      version: 0
  startTime: 2025-02-13 20:30:41 +0800 CST
  state:
    lastTransitionTime: 2025-02-13 20:30:35.329279425 +0800 CST m=+22.173914111
    phase: Running
  version: 1
