apiVersion: system.hero.ai/v1alpha1
kind: TrainingJob
metadata:
  # annotations:
  #   command.system.hero.ai: stop
  name: job-failed-errorlog
  namespace: hero-user
spec:
  faultTolerance:
    healthChecks:
      # 任务运行失败后，根据不同策略，分析当前任务是否可以重启
      # 检查结果保存在 .status.faultTolerance[*].healthChecks[*].result 字段
      # result 可能的值为 FailedJobRestartable, FailedJobUnrestartable
      - name: FailedJobRestartableCheck
        args:
          # 判断任务是否可以重启的策略，可选值为
          # OnFailure（默认）：只要 job 运行失败，即 exitCode 不为 0，则可以重启
          # ExitCodeAndErrorMsg：当 exitCode 不为 0，且日志中包含 logRules 中的字符串，则可以重启
          # Advanced：由系统内置的规则，判断当前运行失败的任务是否可以重启
          policy: ExitCodeAndErrorMsg
          # 当 policy 是 ExitCodeAndErrorMsg 时设置
          # 格式是2维字符串数组，当某一行日志同时出现任意 logRules[*] 中的字符串时，则符合规则，可以重启
          logRules:
            - - NCCL timeout
              - socket error
    restartPolicy:
      # 当判断 job 需要重启时，等待 delay 秒后，再创建新 pod 重启 job
      delay: 60
      # job 最大重启次数
      maxRetry: 2
      # 当 healthChecks 检查结果包含任何一个 policies 时，重启 job
      policies:
        - FailedJobRestartable
  imageUrl: registry.cnbita.com:5000/dockerhub/ubuntu:22.04
  maxRetryCount: 3
  plugins:
    - web-terminal
  tasks:
    - command: |-
        echo normal logs
        echo 'NCCL timeout and socket error'
        exit 1
      extendResource:
        cpuArch: amd64
      minAvaluable: 1
      name: worker
      replicas: 1
      resource:
        cpu: 100m
        memory: 128Mi
      resourcePool: default
status:
  completionTime: 2025-02-17 12:38:01.312990651 +0800 CST m=+3454.901993363
  conditions:
    - lastTransitionTime: "2025-02-17T04:35:15Z"
      status: Pending
    - lastTransitionTime: "2025-02-17T04:35:15Z"
      status: Queuing
    - lastTransitionTime: "2025-02-17T04:35:35Z"
      status: Restarting
    - lastTransitionTime: "2025-02-17T04:36:35Z"
      status: Pending
    - lastTransitionTime: "2025-02-17T04:36:35Z"
      status: Queuing
    - lastTransitionTime: "2025-02-17T04:36:53Z"
      status: Restarting
    - lastTransitionTime: "2025-02-17T04:37:53Z"
      status: Pending
    - lastTransitionTime: "2025-02-17T04:37:53Z"
      status: Queuing
    - lastTransitionTime: "2025-02-17T04:38:11Z"
      status: Failed
  createTime: 2025-02-17 12:35:15.248132344 +0800 CST m=+3288.837134969
  faultTolerance:
    "0":
      completionTime: "2025-02-17T04:35:24Z"
      createTime: "2025-02-17T04:35:15Z"
      healthChecks:
        - message: Failed job is restartable, checked by ExitCodeAndErrorMsg policy
          name: FailedJobRestartableCheck
          result: FailedJobRestartable # 如果是 FailedJobUnrestartable 则不可重启
          time: "2025-02-17T04:35:35Z"
      phase: Failed
      restartReasons:
        - FailedJobRestartable
      startTime: "2025-02-17T04:35:24Z"
    "1":
      completionTime: "2025-02-17T04:36:43Z"
      createTime: "2025-02-17T04:36:35Z"
      healthChecks:
        - message: Failed job is restartable, checked by ExitCodeAndErrorMsg policy
          name: FailedJobRestartableCheck
          result: FailedJobRestartable
          time: "2025-02-17T04:36:53Z"
      phase: Failed
      restartReasons:
        - FailedJobRestartable
      startTime: "2025-02-17T04:36:43Z"
    "2":
      completionTime: "2025-02-17T04:38:01Z"
      createTime: "2025-02-17T04:37:53Z"
      healthChecks:
        - message: Failed job is restartable, checked by ExitCodeAndErrorMsg policy
          name: FailedJobRestartableCheck
          result: FailedJobRestartable
          time: "2025-02-17T04:38:11Z"
      phase: Failed
      startTime: "2025-02-17T04:38:01Z"
  retryCount: 2
  serverDurations:
    7408ebca-33af-4580-ae29-b2b37053f2cf:
      completedTime: 2025-02-17 12:37:58 +0800 CST
      launchedTime: 2025-02-17 12:37:58 +0800 CST
      name: tj-job-failed-restart-worker-0
      nodeName: yigou-dev-102-69
      phase: Terminated
      taskName: worker
      version: 2
    962104b0-0bc9-4559-9f39-42bb0533de66:
      completedTime: 2025-02-17 12:35:21 +0800 CST
      launchedTime: 2025-02-17 12:35:21 +0800 CST
      name: tj-job-failed-restart-worker-0
      nodeName: yigou-dev-102-69
      phase: Terminated
      taskName: worker
      version: 0
    cfac6beb-8a7d-4e4f-bd4c-9bb575f64ba3:
      completedTime: 2025-02-17 12:36:40 +0800 CST
      launchedTime: 2025-02-17 12:36:40 +0800 CST
      name: tj-job-failed-restart-worker-0
      nodeName: yigou-dev-102-69
      phase: Terminated
      taskName: worker
      version: 1
  state:
    lastTransitionTime: 2025-02-17 12:37:53.646535071 +0800 CST m=+3447.235537697
    phase: Failed
  version: 2
