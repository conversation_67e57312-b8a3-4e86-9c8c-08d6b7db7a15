apiVersion: system.hero.ai/v1alpha1
kind: TrainingJob
metadata:
  # annotations:
  #   command.system.hero.ai: stop
  name: job-failed-onfailure
  namespace: hero-user
spec:
  faultTolerance:
    healthChecks:
      # 任务运行失败后，根据不同策略，分析当前任务是否可以重启
      # 检查结果保存在 .status.faultTolerance[*].healthChecks[*].result 字段
      # result 可能的值为 FailedJobRestartable, FailedJobUnrestartable
      - name: FailedJobRestartableCheck
        args:
          # 判断任务是否可以重启的策略，可选值为
          # OnFailure（默认）：只要 job 运行失败，即 exitCode 不为 0，则可以重启
          # ExitCodeAndErrorMsg：当 exitCode 不为 0，且日志中包含 logRules 中的字符串，则可以重启
          # Advanced：由系统内置的规则，判断当前运行失败的任务是否可以重启
          policy: OnFailure
    restartPolicy:
      # 当判断 job 需要重启时，等待 delay 秒后，再创建新 pod 重启 job
      delay: 60
      # job 最大重启次数
      maxRetry: 2
      # 当 healthChecks 检查结果包含任何一个 policies 时，重启 job
      policies:
        - FailedJobRestartable
  imageUrl: registry.cnbita.com:5000/dockerhub/ubuntu:22.04
  maxRetryCount: 3
  plugins:
    - web-terminal
  tasks:
    - command: |-
        echo normal logs
        echo 'NCCL timeout and socket error'
        exit 1
      extendResource:
        cpuArch: amd64
      minAvaluable: 1
      name: worker
      replicas: 1
      resource:
        cpu: 100m
        memory: 128Mi
      resourcePool: default
