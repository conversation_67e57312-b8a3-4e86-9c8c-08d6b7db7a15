apiVersion: system.hero.ai/v1alpha1
kind: TrainingJob
metadata:
  # annotations:
  #   command.system.hero.ai: stop
  name: node-health-v2
  namespace: hero-user
spec:
  faultTolerance:
    healthChecks:
      - name: NodeHealthBeforeStartCheck
  imageUrl: registry.cnbita.com:5000/dockerhub/ubuntu:22.04
  plugins:
    - web-terminal
  tasks:
    - command: |-
        echo  main
      extendResource:
        cpuArch: amd64
      minAvaluable: 2
      name: worker
      replicas: 2
      resource:
        cpu: 1
        memory: 1Gi
        nvidia.com/nvidia-rtx-3090-24GB: 1
      resourcePool: default
